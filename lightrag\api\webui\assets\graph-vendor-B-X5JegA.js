import{g as xi,r as G,R as Me}from"./react-vendor-DEwriMA6.js";var Te={exports:{}},ut;function Ti(){if(ut)return Te.exports;ut=1;var n=typeof Reflect=="object"?Reflect:null,i=n&&typeof n.apply=="function"?n.apply:function(p,v,_){return Function.prototype.apply.call(p,v,_)},t;n&&typeof n.ownKeys=="function"?t=n.ownKeys:Object.getOwnPropertySymbols?t=function(p){return Object.getOwnPropertyNames(p).concat(Object.getOwnPropertySymbols(p))}:t=function(p){return Object.getOwnPropertyNames(p)};function e(g){console&&console.warn&&console.warn(g)}var r=Number.isNaN||function(p){return p!==p};function a(){a.init.call(this)}Te.exports=a,Te.exports.once=k,a.EventEmitter=a,a.prototype._events=void 0,a.prototype._eventsCount=0,a.prototype._maxListeners=void 0;var o=10;function s(g){if(typeof g!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof g)}Object.defineProperty(a,"defaultMaxListeners",{enumerable:!0,get:function(){return o},set:function(g){if(typeof g!="number"||g<0||r(g))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+g+".");o=g}}),a.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},a.prototype.setMaxListeners=function(p){if(typeof p!="number"||p<0||r(p))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+p+".");return this._maxListeners=p,this};function u(g){return g._maxListeners===void 0?a.defaultMaxListeners:g._maxListeners}a.prototype.getMaxListeners=function(){return u(this)},a.prototype.emit=function(p){for(var v=[],_=1;_<arguments.length;_++)v.push(arguments[_]);var A=p==="error",D=this._events;if(D!==void 0)A=A&&D.error===void 0;else if(!A)return!1;if(A){var L;if(v.length>0&&(L=v[0]),L instanceof Error)throw L;var F=new Error("Unhandled error."+(L?" ("+L.message+")":""));throw F.context=L,F}var P=D[p];if(P===void 0)return!1;if(typeof P=="function")i(P,this,v);else for(var q=P.length,O=y(P,q),_=0;_<q;++_)i(O[_],this,v);return!0};function h(g,p,v,_){var A,D,L;if(s(v),D=g._events,D===void 0?(D=g._events=Object.create(null),g._eventsCount=0):(D.newListener!==void 0&&(g.emit("newListener",p,v.listener?v.listener:v),D=g._events),L=D[p]),L===void 0)L=D[p]=v,++g._eventsCount;else if(typeof L=="function"?L=D[p]=_?[v,L]:[L,v]:_?L.unshift(v):L.push(v),A=u(g),A>0&&L.length>A&&!L.warned){L.warned=!0;var F=new Error("Possible EventEmitter memory leak detected. "+L.length+" "+String(p)+" listeners added. Use emitter.setMaxListeners() to increase limit");F.name="MaxListenersExceededWarning",F.emitter=g,F.type=p,F.count=L.length,e(F)}return g}a.prototype.addListener=function(p,v){return h(this,p,v,!1)},a.prototype.on=a.prototype.addListener,a.prototype.prependListener=function(p,v){return h(this,p,v,!0)};function d(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function l(g,p,v){var _={fired:!1,wrapFn:void 0,target:g,type:p,listener:v},A=d.bind(_);return A.listener=v,_.wrapFn=A,A}a.prototype.once=function(p,v){return s(v),this.on(p,l(this,p,v)),this},a.prototype.prependOnceListener=function(p,v){return s(v),this.prependListener(p,l(this,p,v)),this},a.prototype.removeListener=function(p,v){var _,A,D,L,F;if(s(v),A=this._events,A===void 0)return this;if(_=A[p],_===void 0)return this;if(_===v||_.listener===v)--this._eventsCount===0?this._events=Object.create(null):(delete A[p],A.removeListener&&this.emit("removeListener",p,_.listener||v));else if(typeof _!="function"){for(D=-1,L=_.length-1;L>=0;L--)if(_[L]===v||_[L].listener===v){F=_[L].listener,D=L;break}if(D<0)return this;D===0?_.shift():b(_,D),_.length===1&&(A[p]=_[0]),A.removeListener!==void 0&&this.emit("removeListener",p,F||v)}return this},a.prototype.off=a.prototype.removeListener,a.prototype.removeAllListeners=function(p){var v,_,A;if(_=this._events,_===void 0)return this;if(_.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):_[p]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete _[p]),this;if(arguments.length===0){var D=Object.keys(_),L;for(A=0;A<D.length;++A)L=D[A],L!=="removeListener"&&this.removeAllListeners(L);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(v=_[p],typeof v=="function")this.removeListener(p,v);else if(v!==void 0)for(A=v.length-1;A>=0;A--)this.removeListener(p,v[A]);return this};function c(g,p,v){var _=g._events;if(_===void 0)return[];var A=_[p];return A===void 0?[]:typeof A=="function"?v?[A.listener||A]:[A]:v?E(A):y(A,A.length)}a.prototype.listeners=function(p){return c(this,p,!0)},a.prototype.rawListeners=function(p){return c(this,p,!1)},a.listenerCount=function(g,p){return typeof g.listenerCount=="function"?g.listenerCount(p):f.call(g,p)},a.prototype.listenerCount=f;function f(g){var p=this._events;if(p!==void 0){var v=p[g];if(typeof v=="function")return 1;if(v!==void 0)return v.length}return 0}a.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]};function y(g,p){for(var v=new Array(p),_=0;_<p;++_)v[_]=g[_];return v}function b(g,p){for(;p+1<g.length;p++)g[p]=g[p+1];g.pop()}function E(g){for(var p=new Array(g.length),v=0;v<p.length;++v)p[v]=g[v].listener||g[v];return p}function k(g,p){return new Promise(function(v,_){function A(L){g.removeListener(p,D),_(L)}function D(){typeof g.removeListener=="function"&&g.removeListener("error",A),v([].slice.call(arguments))}T(g,p,D,{once:!0}),p!=="error"&&x(g,A,{once:!0})})}function x(g,p,v){typeof g.on=="function"&&T(g,"error",p,v)}function T(g,p,v,_){if(typeof g.on=="function")_.once?g.once(p,v):g.on(p,v);else if(typeof g.addEventListener=="function")g.addEventListener(p,function A(D){_.once&&g.removeEventListener(p,A),v(D)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof g)}return Te.exports}var Mt=Ti();function Ci(){const n=arguments[0];for(let i=1,t=arguments.length;i<t;i++)if(arguments[i])for(const e in arguments[i])n[e]=arguments[i][e];return n}let I=Ci;typeof Object.assign=="function"&&(I=Object.assign);function B(n,i,t,e){const r=n._nodes.get(i);let a=null;return r&&(e==="mixed"?a=r.out&&r.out[t]||r.undirected&&r.undirected[t]:e==="directed"?a=r.out&&r.out[t]:a=r.undirected&&r.undirected[t]),a}function M(n){return typeof n=="object"&&n!==null}function Ot(n){let i;for(i in n)return!1;return!0}function z(n,i,t){Object.defineProperty(n,i,{enumerable:!1,configurable:!1,writable:!0,value:t})}function V(n,i,t){const e={enumerable:!0,configurable:!0};typeof t=="function"?e.get=t:(e.value=t,e.writable=!1),Object.defineProperty(n,i,e)}function ht(n){return!(!M(n)||n.attributes&&!Array.isArray(n.attributes))}function Si(){let n=Math.floor(Math.random()*256)&255;return()=>n++}function Q(){const n=arguments;let i=null,t=-1;return{[Symbol.iterator](){return this},next(){let e=null;do{if(i===null){if(t++,t>=n.length)return{done:!0};i=n[t][Symbol.iterator]()}if(e=i.next(),e.done){i=null;continue}break}while(!0);return e}}}function ce(){return{[Symbol.iterator](){return this},next(){return{done:!0}}}}class Qe extends Error{constructor(i){super(),this.name="GraphError",this.message=i}}class C extends Qe{constructor(i){super(i),this.name="InvalidArgumentsGraphError",typeof Error.captureStackTrace=="function"&&Error.captureStackTrace(this,C.prototype.constructor)}}class w extends Qe{constructor(i){super(i),this.name="NotFoundGraphError",typeof Error.captureStackTrace=="function"&&Error.captureStackTrace(this,w.prototype.constructor)}}class R extends Qe{constructor(i){super(i),this.name="UsageGraphError",typeof Error.captureStackTrace=="function"&&Error.captureStackTrace(this,R.prototype.constructor)}}function Ut(n,i){this.key=n,this.attributes=i,this.clear()}Ut.prototype.clear=function(){this.inDegree=0,this.outDegree=0,this.undirectedDegree=0,this.undirectedLoops=0,this.directedLoops=0,this.in={},this.out={},this.undirected={}};function $t(n,i){this.key=n,this.attributes=i,this.clear()}$t.prototype.clear=function(){this.inDegree=0,this.outDegree=0,this.directedLoops=0,this.in={},this.out={}};function zt(n,i){this.key=n,this.attributes=i,this.clear()}zt.prototype.clear=function(){this.undirectedDegree=0,this.undirectedLoops=0,this.undirected={}};function fe(n,i,t,e,r){this.key=i,this.attributes=r,this.undirected=n,this.source=t,this.target=e}fe.prototype.attach=function(){let n="out",i="in";this.undirected&&(n=i="undirected");const t=this.source.key,e=this.target.key;this.source[n][e]=this,!(this.undirected&&t===e)&&(this.target[i][t]=this)};fe.prototype.attachMulti=function(){let n="out",i="in";const t=this.source.key,e=this.target.key;this.undirected&&(n=i="undirected");const r=this.source[n],a=r[e];if(typeof a>"u"){r[e]=this,this.undirected&&t===e||(this.target[i][t]=this);return}a.previous=this,this.next=a,r[e]=this,this.target[i][t]=this};fe.prototype.detach=function(){const n=this.source.key,i=this.target.key;let t="out",e="in";this.undirected&&(t=e="undirected"),delete this.source[t][i],delete this.target[e][n]};fe.prototype.detachMulti=function(){const n=this.source.key,i=this.target.key;let t="out",e="in";this.undirected&&(t=e="undirected"),this.previous===void 0?this.next===void 0?(delete this.source[t][i],delete this.target[e][n]):(this.next.previous=void 0,this.source[t][i]=this.next,this.target[e][n]=this.next):(this.previous.next=this.next,this.next!==void 0&&(this.next.previous=this.previous))};const Bt=0,Ht=1,ki=2,Wt=3;function ee(n,i,t,e,r,a,o){let s,u,h,d;if(e=""+e,t===Bt){if(s=n._nodes.get(e),!s)throw new w(`Graph.${i}: could not find the "${e}" node in the graph.`);h=r,d=a}else if(t===Wt){if(r=""+r,u=n._edges.get(r),!u)throw new w(`Graph.${i}: could not find the "${r}" edge in the graph.`);const l=u.source.key,c=u.target.key;if(e===l)s=u.target;else if(e===c)s=u.source;else throw new w(`Graph.${i}: the "${e}" node is not attached to the "${r}" edge (${l}, ${c}).`);h=a,d=o}else{if(u=n._edges.get(e),!u)throw new w(`Graph.${i}: could not find the "${e}" edge in the graph.`);t===Ht?s=u.source:s=u.target,h=r,d=a}return[s,h,d]}function Ai(n,i,t){n.prototype[i]=function(e,r,a){const[o,s]=ee(this,i,t,e,r,a);return o.attributes[s]}}function Ri(n,i,t){n.prototype[i]=function(e,r){const[a]=ee(this,i,t,e,r);return a.attributes}}function Li(n,i,t){n.prototype[i]=function(e,r,a){const[o,s]=ee(this,i,t,e,r,a);return o.attributes.hasOwnProperty(s)}}function Di(n,i,t){n.prototype[i]=function(e,r,a,o){const[s,u,h]=ee(this,i,t,e,r,a,o);return s.attributes[u]=h,this.emit("nodeAttributesUpdated",{key:s.key,type:"set",attributes:s.attributes,name:u}),this}}function Ni(n,i,t){n.prototype[i]=function(e,r,a,o){const[s,u,h]=ee(this,i,t,e,r,a,o);if(typeof h!="function")throw new C(`Graph.${i}: updater should be a function.`);const d=s.attributes,l=h(d[u]);return d[u]=l,this.emit("nodeAttributesUpdated",{key:s.key,type:"set",attributes:s.attributes,name:u}),this}}function Gi(n,i,t){n.prototype[i]=function(e,r,a){const[o,s]=ee(this,i,t,e,r,a);return delete o.attributes[s],this.emit("nodeAttributesUpdated",{key:o.key,type:"remove",attributes:o.attributes,name:s}),this}}function Fi(n,i,t){n.prototype[i]=function(e,r,a){const[o,s]=ee(this,i,t,e,r,a);if(!M(s))throw new C(`Graph.${i}: provided attributes are not a plain object.`);return o.attributes=s,this.emit("nodeAttributesUpdated",{key:o.key,type:"replace",attributes:o.attributes}),this}}function Pi(n,i,t){n.prototype[i]=function(e,r,a){const[o,s]=ee(this,i,t,e,r,a);if(!M(s))throw new C(`Graph.${i}: provided attributes are not a plain object.`);return I(o.attributes,s),this.emit("nodeAttributesUpdated",{key:o.key,type:"merge",attributes:o.attributes,data:s}),this}}function Ii(n,i,t){n.prototype[i]=function(e,r,a){const[o,s]=ee(this,i,t,e,r,a);if(typeof s!="function")throw new C(`Graph.${i}: provided updater is not a function.`);return o.attributes=s(o.attributes),this.emit("nodeAttributesUpdated",{key:o.key,type:"update",attributes:o.attributes}),this}}const Mi=[{name:n=>`get${n}Attribute`,attacher:Ai},{name:n=>`get${n}Attributes`,attacher:Ri},{name:n=>`has${n}Attribute`,attacher:Li},{name:n=>`set${n}Attribute`,attacher:Di},{name:n=>`update${n}Attribute`,attacher:Ni},{name:n=>`remove${n}Attribute`,attacher:Gi},{name:n=>`replace${n}Attributes`,attacher:Fi},{name:n=>`merge${n}Attributes`,attacher:Pi},{name:n=>`update${n}Attributes`,attacher:Ii}];function Oi(n){Mi.forEach(function({name:i,attacher:t}){t(n,i("Node"),Bt),t(n,i("Source"),Ht),t(n,i("Target"),ki),t(n,i("Opposite"),Wt)})}function Ui(n,i,t){n.prototype[i]=function(e,r){let a;if(this.type!=="mixed"&&t!=="mixed"&&t!==this.type)throw new R(`Graph.${i}: cannot find this type of edges in your ${this.type} graph.`);if(arguments.length>2){if(this.multi)throw new R(`Graph.${i}: cannot use a {source,target} combo when asking about an edge's attributes in a MultiGraph since we cannot infer the one you want information about.`);const o=""+e,s=""+r;if(r=arguments[2],a=B(this,o,s,t),!a)throw new w(`Graph.${i}: could not find an edge for the given path ("${o}" - "${s}").`)}else{if(t!=="mixed")throw new R(`Graph.${i}: calling this method with only a key (vs. a source and target) does not make sense since an edge with this key could have the other type.`);if(e=""+e,a=this._edges.get(e),!a)throw new w(`Graph.${i}: could not find the "${e}" edge in the graph.`)}return a.attributes[r]}}function $i(n,i,t){n.prototype[i]=function(e){let r;if(this.type!=="mixed"&&t!=="mixed"&&t!==this.type)throw new R(`Graph.${i}: cannot find this type of edges in your ${this.type} graph.`);if(arguments.length>1){if(this.multi)throw new R(`Graph.${i}: cannot use a {source,target} combo when asking about an edge's attributes in a MultiGraph since we cannot infer the one you want information about.`);const a=""+e,o=""+arguments[1];if(r=B(this,a,o,t),!r)throw new w(`Graph.${i}: could not find an edge for the given path ("${a}" - "${o}").`)}else{if(t!=="mixed")throw new R(`Graph.${i}: calling this method with only a key (vs. a source and target) does not make sense since an edge with this key could have the other type.`);if(e=""+e,r=this._edges.get(e),!r)throw new w(`Graph.${i}: could not find the "${e}" edge in the graph.`)}return r.attributes}}function zi(n,i,t){n.prototype[i]=function(e,r){let a;if(this.type!=="mixed"&&t!=="mixed"&&t!==this.type)throw new R(`Graph.${i}: cannot find this type of edges in your ${this.type} graph.`);if(arguments.length>2){if(this.multi)throw new R(`Graph.${i}: cannot use a {source,target} combo when asking about an edge's attributes in a MultiGraph since we cannot infer the one you want information about.`);const o=""+e,s=""+r;if(r=arguments[2],a=B(this,o,s,t),!a)throw new w(`Graph.${i}: could not find an edge for the given path ("${o}" - "${s}").`)}else{if(t!=="mixed")throw new R(`Graph.${i}: calling this method with only a key (vs. a source and target) does not make sense since an edge with this key could have the other type.`);if(e=""+e,a=this._edges.get(e),!a)throw new w(`Graph.${i}: could not find the "${e}" edge in the graph.`)}return a.attributes.hasOwnProperty(r)}}function Bi(n,i,t){n.prototype[i]=function(e,r,a){let o;if(this.type!=="mixed"&&t!=="mixed"&&t!==this.type)throw new R(`Graph.${i}: cannot find this type of edges in your ${this.type} graph.`);if(arguments.length>3){if(this.multi)throw new R(`Graph.${i}: cannot use a {source,target} combo when asking about an edge's attributes in a MultiGraph since we cannot infer the one you want information about.`);const s=""+e,u=""+r;if(r=arguments[2],a=arguments[3],o=B(this,s,u,t),!o)throw new w(`Graph.${i}: could not find an edge for the given path ("${s}" - "${u}").`)}else{if(t!=="mixed")throw new R(`Graph.${i}: calling this method with only a key (vs. a source and target) does not make sense since an edge with this key could have the other type.`);if(e=""+e,o=this._edges.get(e),!o)throw new w(`Graph.${i}: could not find the "${e}" edge in the graph.`)}return o.attributes[r]=a,this.emit("edgeAttributesUpdated",{key:o.key,type:"set",attributes:o.attributes,name:r}),this}}function Hi(n,i,t){n.prototype[i]=function(e,r,a){let o;if(this.type!=="mixed"&&t!=="mixed"&&t!==this.type)throw new R(`Graph.${i}: cannot find this type of edges in your ${this.type} graph.`);if(arguments.length>3){if(this.multi)throw new R(`Graph.${i}: cannot use a {source,target} combo when asking about an edge's attributes in a MultiGraph since we cannot infer the one you want information about.`);const s=""+e,u=""+r;if(r=arguments[2],a=arguments[3],o=B(this,s,u,t),!o)throw new w(`Graph.${i}: could not find an edge for the given path ("${s}" - "${u}").`)}else{if(t!=="mixed")throw new R(`Graph.${i}: calling this method with only a key (vs. a source and target) does not make sense since an edge with this key could have the other type.`);if(e=""+e,o=this._edges.get(e),!o)throw new w(`Graph.${i}: could not find the "${e}" edge in the graph.`)}if(typeof a!="function")throw new C(`Graph.${i}: updater should be a function.`);return o.attributes[r]=a(o.attributes[r]),this.emit("edgeAttributesUpdated",{key:o.key,type:"set",attributes:o.attributes,name:r}),this}}function Wi(n,i,t){n.prototype[i]=function(e,r){let a;if(this.type!=="mixed"&&t!=="mixed"&&t!==this.type)throw new R(`Graph.${i}: cannot find this type of edges in your ${this.type} graph.`);if(arguments.length>2){if(this.multi)throw new R(`Graph.${i}: cannot use a {source,target} combo when asking about an edge's attributes in a MultiGraph since we cannot infer the one you want information about.`);const o=""+e,s=""+r;if(r=arguments[2],a=B(this,o,s,t),!a)throw new w(`Graph.${i}: could not find an edge for the given path ("${o}" - "${s}").`)}else{if(t!=="mixed")throw new R(`Graph.${i}: calling this method with only a key (vs. a source and target) does not make sense since an edge with this key could have the other type.`);if(e=""+e,a=this._edges.get(e),!a)throw new w(`Graph.${i}: could not find the "${e}" edge in the graph.`)}return delete a.attributes[r],this.emit("edgeAttributesUpdated",{key:a.key,type:"remove",attributes:a.attributes,name:r}),this}}function ji(n,i,t){n.prototype[i]=function(e,r){let a;if(this.type!=="mixed"&&t!=="mixed"&&t!==this.type)throw new R(`Graph.${i}: cannot find this type of edges in your ${this.type} graph.`);if(arguments.length>2){if(this.multi)throw new R(`Graph.${i}: cannot use a {source,target} combo when asking about an edge's attributes in a MultiGraph since we cannot infer the one you want information about.`);const o=""+e,s=""+r;if(r=arguments[2],a=B(this,o,s,t),!a)throw new w(`Graph.${i}: could not find an edge for the given path ("${o}" - "${s}").`)}else{if(t!=="mixed")throw new R(`Graph.${i}: calling this method with only a key (vs. a source and target) does not make sense since an edge with this key could have the other type.`);if(e=""+e,a=this._edges.get(e),!a)throw new w(`Graph.${i}: could not find the "${e}" edge in the graph.`)}if(!M(r))throw new C(`Graph.${i}: provided attributes are not a plain object.`);return a.attributes=r,this.emit("edgeAttributesUpdated",{key:a.key,type:"replace",attributes:a.attributes}),this}}function Vi(n,i,t){n.prototype[i]=function(e,r){let a;if(this.type!=="mixed"&&t!=="mixed"&&t!==this.type)throw new R(`Graph.${i}: cannot find this type of edges in your ${this.type} graph.`);if(arguments.length>2){if(this.multi)throw new R(`Graph.${i}: cannot use a {source,target} combo when asking about an edge's attributes in a MultiGraph since we cannot infer the one you want information about.`);const o=""+e,s=""+r;if(r=arguments[2],a=B(this,o,s,t),!a)throw new w(`Graph.${i}: could not find an edge for the given path ("${o}" - "${s}").`)}else{if(t!=="mixed")throw new R(`Graph.${i}: calling this method with only a key (vs. a source and target) does not make sense since an edge with this key could have the other type.`);if(e=""+e,a=this._edges.get(e),!a)throw new w(`Graph.${i}: could not find the "${e}" edge in the graph.`)}if(!M(r))throw new C(`Graph.${i}: provided attributes are not a plain object.`);return I(a.attributes,r),this.emit("edgeAttributesUpdated",{key:a.key,type:"merge",attributes:a.attributes,data:r}),this}}function Ki(n,i,t){n.prototype[i]=function(e,r){let a;if(this.type!=="mixed"&&t!=="mixed"&&t!==this.type)throw new R(`Graph.${i}: cannot find this type of edges in your ${this.type} graph.`);if(arguments.length>2){if(this.multi)throw new R(`Graph.${i}: cannot use a {source,target} combo when asking about an edge's attributes in a MultiGraph since we cannot infer the one you want information about.`);const o=""+e,s=""+r;if(r=arguments[2],a=B(this,o,s,t),!a)throw new w(`Graph.${i}: could not find an edge for the given path ("${o}" - "${s}").`)}else{if(t!=="mixed")throw new R(`Graph.${i}: calling this method with only a key (vs. a source and target) does not make sense since an edge with this key could have the other type.`);if(e=""+e,a=this._edges.get(e),!a)throw new w(`Graph.${i}: could not find the "${e}" edge in the graph.`)}if(typeof r!="function")throw new C(`Graph.${i}: provided updater is not a function.`);return a.attributes=r(a.attributes),this.emit("edgeAttributesUpdated",{key:a.key,type:"update",attributes:a.attributes}),this}}const Yi=[{name:n=>`get${n}Attribute`,attacher:Ui},{name:n=>`get${n}Attributes`,attacher:$i},{name:n=>`has${n}Attribute`,attacher:zi},{name:n=>`set${n}Attribute`,attacher:Bi},{name:n=>`update${n}Attribute`,attacher:Hi},{name:n=>`remove${n}Attribute`,attacher:Wi},{name:n=>`replace${n}Attributes`,attacher:ji},{name:n=>`merge${n}Attributes`,attacher:Vi},{name:n=>`update${n}Attributes`,attacher:Ki}];function qi(n){Yi.forEach(function({name:i,attacher:t}){t(n,i("Edge"),"mixed"),t(n,i("DirectedEdge"),"directed"),t(n,i("UndirectedEdge"),"undirected")})}const Zi=[{name:"edges",type:"mixed"},{name:"inEdges",type:"directed",direction:"in"},{name:"outEdges",type:"directed",direction:"out"},{name:"inboundEdges",type:"mixed",direction:"in"},{name:"outboundEdges",type:"mixed",direction:"out"},{name:"directedEdges",type:"directed"},{name:"undirectedEdges",type:"undirected"}];function Xi(n,i,t,e){let r=!1;for(const a in i){if(a===e)continue;const o=i[a];if(r=t(o.key,o.attributes,o.source.key,o.target.key,o.source.attributes,o.target.attributes,o.undirected),n&&r)return o.key}}function Ji(n,i,t,e){let r,a,o,s=!1;for(const u in i)if(u!==e){r=i[u];do{if(a=r.source,o=r.target,s=t(r.key,r.attributes,a.key,o.key,a.attributes,o.attributes,r.undirected),n&&s)return r.key;r=r.next}while(r!==void 0)}}function Oe(n,i){const t=Object.keys(n),e=t.length;let r,a=0;return{[Symbol.iterator](){return this},next(){do if(r)r=r.next;else{if(a>=e)return{done:!0};const o=t[a++];if(o===i){r=void 0;continue}r=n[o]}while(!r);return{done:!1,value:{edge:r.key,attributes:r.attributes,source:r.source.key,target:r.target.key,sourceAttributes:r.source.attributes,targetAttributes:r.target.attributes,undirected:r.undirected}}}}}function Qi(n,i,t,e){const r=i[t];if(!r)return;const a=r.source,o=r.target;if(e(r.key,r.attributes,a.key,o.key,a.attributes,o.attributes,r.undirected)&&n)return r.key}function er(n,i,t,e){let r=i[t];if(!r)return;let a=!1;do{if(a=e(r.key,r.attributes,r.source.key,r.target.key,r.source.attributes,r.target.attributes,r.undirected),n&&a)return r.key;r=r.next}while(r!==void 0)}function Ue(n,i){let t=n[i];if(t.next!==void 0)return{[Symbol.iterator](){return this},next(){if(!t)return{done:!0};const r={edge:t.key,attributes:t.attributes,source:t.source.key,target:t.target.key,sourceAttributes:t.source.attributes,targetAttributes:t.target.attributes,undirected:t.undirected};return t=t.next,{done:!1,value:r}}};let e=!1;return{[Symbol.iterator](){return this},next(){return e===!0?{done:!0}:(e=!0,{done:!1,value:{edge:t.key,attributes:t.attributes,source:t.source.key,target:t.target.key,sourceAttributes:t.source.attributes,targetAttributes:t.target.attributes,undirected:t.undirected}})}}}function tr(n,i){if(n.size===0)return[];if(i==="mixed"||i===n.type)return Array.from(n._edges.keys());const t=i==="undirected"?n.undirectedSize:n.directedSize,e=new Array(t),r=i==="undirected",a=n._edges.values();let o=0,s,u;for(;s=a.next(),s.done!==!0;)u=s.value,u.undirected===r&&(e[o++]=u.key);return e}function jt(n,i,t,e){if(i.size===0)return;const r=t!=="mixed"&&t!==i.type,a=t==="undirected";let o,s,u=!1;const h=i._edges.values();for(;o=h.next(),o.done!==!0;){if(s=o.value,r&&s.undirected!==a)continue;const{key:d,attributes:l,source:c,target:f}=s;if(u=e(d,l,c.key,f.key,c.attributes,f.attributes,s.undirected),n&&u)return d}}function ir(n,i){if(n.size===0)return ce();const t=i!=="mixed"&&i!==n.type,e=i==="undirected",r=n._edges.values();return{[Symbol.iterator](){return this},next(){let a,o;for(;;){if(a=r.next(),a.done)return a;if(o=a.value,!(t&&o.undirected!==e))break}return{value:{edge:o.key,attributes:o.attributes,source:o.source.key,target:o.target.key,sourceAttributes:o.source.attributes,targetAttributes:o.target.attributes,undirected:o.undirected},done:!1}}}}function et(n,i,t,e,r,a){const o=i?Ji:Xi;let s;if(t!=="undirected"&&(e!=="out"&&(s=o(n,r.in,a),n&&s)||e!=="in"&&(s=o(n,r.out,a,e?void 0:r.key),n&&s))||t!=="directed"&&(s=o(n,r.undirected,a),n&&s))return s}function rr(n,i,t,e){const r=[];return et(!1,n,i,t,e,function(a){r.push(a)}),r}function nr(n,i,t){let e=ce();return n!=="undirected"&&(i!=="out"&&typeof t.in<"u"&&(e=Q(e,Oe(t.in))),i!=="in"&&typeof t.out<"u"&&(e=Q(e,Oe(t.out,i?void 0:t.key)))),n!=="directed"&&typeof t.undirected<"u"&&(e=Q(e,Oe(t.undirected))),e}function tt(n,i,t,e,r,a,o){const s=t?er:Qi;let u;if(i!=="undirected"&&(typeof r.in<"u"&&e!=="out"&&(u=s(n,r.in,a,o),n&&u)||typeof r.out<"u"&&e!=="in"&&(e||r.key!==a)&&(u=s(n,r.out,a,o),n&&u))||i!=="directed"&&typeof r.undirected<"u"&&(u=s(n,r.undirected,a,o),n&&u))return u}function ar(n,i,t,e,r){const a=[];return tt(!1,n,i,t,e,r,function(o){a.push(o)}),a}function or(n,i,t,e){let r=ce();return n!=="undirected"&&(typeof t.in<"u"&&i!=="out"&&e in t.in&&(r=Q(r,Ue(t.in,e))),typeof t.out<"u"&&i!=="in"&&e in t.out&&(i||t.key!==e)&&(r=Q(r,Ue(t.out,e)))),n!=="directed"&&typeof t.undirected<"u"&&e in t.undirected&&(r=Q(r,Ue(t.undirected,e))),r}function sr(n,i){const{name:t,type:e,direction:r}=i;n.prototype[t]=function(a,o){if(e!=="mixed"&&this.type!=="mixed"&&e!==this.type)return[];if(!arguments.length)return tr(this,e);if(arguments.length===1){a=""+a;const s=this._nodes.get(a);if(typeof s>"u")throw new w(`Graph.${t}: could not find the "${a}" node in the graph.`);return rr(this.multi,e==="mixed"?this.type:e,r,s)}if(arguments.length===2){a=""+a,o=""+o;const s=this._nodes.get(a);if(!s)throw new w(`Graph.${t}:  could not find the "${a}" source node in the graph.`);if(!this._nodes.has(o))throw new w(`Graph.${t}:  could not find the "${o}" target node in the graph.`);return ar(e,this.multi,r,s,o)}throw new C(`Graph.${t}: too many arguments (expecting 0, 1 or 2 and got ${arguments.length}).`)}}function ur(n,i){const{name:t,type:e,direction:r}=i,a="forEach"+t[0].toUpperCase()+t.slice(1,-1);n.prototype[a]=function(h,d,l){if(!(e!=="mixed"&&this.type!=="mixed"&&e!==this.type)){if(arguments.length===1)return l=h,jt(!1,this,e,l);if(arguments.length===2){h=""+h,l=d;const c=this._nodes.get(h);if(typeof c>"u")throw new w(`Graph.${a}: could not find the "${h}" node in the graph.`);return et(!1,this.multi,e==="mixed"?this.type:e,r,c,l)}if(arguments.length===3){h=""+h,d=""+d;const c=this._nodes.get(h);if(!c)throw new w(`Graph.${a}:  could not find the "${h}" source node in the graph.`);if(!this._nodes.has(d))throw new w(`Graph.${a}:  could not find the "${d}" target node in the graph.`);return tt(!1,e,this.multi,r,c,d,l)}throw new C(`Graph.${a}: too many arguments (expecting 1, 2 or 3 and got ${arguments.length}).`)}};const o="map"+t[0].toUpperCase()+t.slice(1);n.prototype[o]=function(){const h=Array.prototype.slice.call(arguments),d=h.pop();let l;if(h.length===0){let c=0;e!=="directed"&&(c+=this.undirectedSize),e!=="undirected"&&(c+=this.directedSize),l=new Array(c);let f=0;h.push((y,b,E,k,x,T,g)=>{l[f++]=d(y,b,E,k,x,T,g)})}else l=[],h.push((c,f,y,b,E,k,x)=>{l.push(d(c,f,y,b,E,k,x))});return this[a].apply(this,h),l};const s="filter"+t[0].toUpperCase()+t.slice(1);n.prototype[s]=function(){const h=Array.prototype.slice.call(arguments),d=h.pop(),l=[];return h.push((c,f,y,b,E,k,x)=>{d(c,f,y,b,E,k,x)&&l.push(c)}),this[a].apply(this,h),l};const u="reduce"+t[0].toUpperCase()+t.slice(1);n.prototype[u]=function(){let h=Array.prototype.slice.call(arguments);if(h.length<2||h.length>4)throw new C(`Graph.${u}: invalid number of arguments (expecting 2, 3 or 4 and got ${h.length}).`);if(typeof h[h.length-1]=="function"&&typeof h[h.length-2]!="function")throw new C(`Graph.${u}: missing initial value. You must provide it because the callback takes more than one argument and we cannot infer the initial value from the first iteration, as you could with a simple array.`);let d,l;h.length===2?(d=h[0],l=h[1],h=[]):h.length===3?(d=h[1],l=h[2],h=[h[0]]):h.length===4&&(d=h[2],l=h[3],h=[h[0],h[1]]);let c=l;return h.push((f,y,b,E,k,x,T)=>{c=d(c,f,y,b,E,k,x,T)}),this[a].apply(this,h),c}}function hr(n,i){const{name:t,type:e,direction:r}=i,a="find"+t[0].toUpperCase()+t.slice(1,-1);n.prototype[a]=function(u,h,d){if(e!=="mixed"&&this.type!=="mixed"&&e!==this.type)return!1;if(arguments.length===1)return d=u,jt(!0,this,e,d);if(arguments.length===2){u=""+u,d=h;const l=this._nodes.get(u);if(typeof l>"u")throw new w(`Graph.${a}: could not find the "${u}" node in the graph.`);return et(!0,this.multi,e==="mixed"?this.type:e,r,l,d)}if(arguments.length===3){u=""+u,h=""+h;const l=this._nodes.get(u);if(!l)throw new w(`Graph.${a}:  could not find the "${u}" source node in the graph.`);if(!this._nodes.has(h))throw new w(`Graph.${a}:  could not find the "${h}" target node in the graph.`);return tt(!0,e,this.multi,r,l,h,d)}throw new C(`Graph.${a}: too many arguments (expecting 1, 2 or 3 and got ${arguments.length}).`)};const o="some"+t[0].toUpperCase()+t.slice(1,-1);n.prototype[o]=function(){const u=Array.prototype.slice.call(arguments),h=u.pop();return u.push((l,c,f,y,b,E,k)=>h(l,c,f,y,b,E,k)),!!this[a].apply(this,u)};const s="every"+t[0].toUpperCase()+t.slice(1,-1);n.prototype[s]=function(){const u=Array.prototype.slice.call(arguments),h=u.pop();return u.push((l,c,f,y,b,E,k)=>!h(l,c,f,y,b,E,k)),!this[a].apply(this,u)}}function dr(n,i){const{name:t,type:e,direction:r}=i,a=t.slice(0,-1)+"Entries";n.prototype[a]=function(o,s){if(e!=="mixed"&&this.type!=="mixed"&&e!==this.type)return ce();if(!arguments.length)return ir(this,e);if(arguments.length===1){o=""+o;const u=this._nodes.get(o);if(!u)throw new w(`Graph.${a}: could not find the "${o}" node in the graph.`);return nr(e,r,u)}if(arguments.length===2){o=""+o,s=""+s;const u=this._nodes.get(o);if(!u)throw new w(`Graph.${a}:  could not find the "${o}" source node in the graph.`);if(!this._nodes.has(s))throw new w(`Graph.${a}:  could not find the "${s}" target node in the graph.`);return or(e,r,u,s)}throw new C(`Graph.${a}: too many arguments (expecting 0, 1 or 2 and got ${arguments.length}).`)}}function lr(n){Zi.forEach(i=>{sr(n,i),ur(n,i),hr(n,i),dr(n,i)})}const cr=[{name:"neighbors",type:"mixed"},{name:"inNeighbors",type:"directed",direction:"in"},{name:"outNeighbors",type:"directed",direction:"out"},{name:"inboundNeighbors",type:"mixed",direction:"in"},{name:"outboundNeighbors",type:"mixed",direction:"out"},{name:"directedNeighbors",type:"directed"},{name:"undirectedNeighbors",type:"undirected"}];function Ne(){this.A=null,this.B=null}Ne.prototype.wrap=function(n){this.A===null?this.A=n:this.B===null&&(this.B=n)};Ne.prototype.has=function(n){return this.A!==null&&n in this.A||this.B!==null&&n in this.B};function me(n,i,t,e,r){for(const a in e){const o=e[a],s=o.source,u=o.target,h=s===t?u:s;if(i&&i.has(h.key))continue;const d=r(h.key,h.attributes);if(n&&d)return h.key}}function it(n,i,t,e,r){if(i!=="mixed"){if(i==="undirected")return me(n,null,e,e.undirected,r);if(typeof t=="string")return me(n,null,e,e[t],r)}const a=new Ne;let o;if(i!=="undirected"){if(t!=="out"){if(o=me(n,null,e,e.in,r),n&&o)return o;a.wrap(e.in)}if(t!=="in"){if(o=me(n,a,e,e.out,r),n&&o)return o;a.wrap(e.out)}}if(i!=="directed"&&(o=me(n,a,e,e.undirected,r),n&&o))return o}function fr(n,i,t){if(n!=="mixed"){if(n==="undirected")return Object.keys(t.undirected);if(typeof i=="string")return Object.keys(t[i])}const e=[];return it(!1,n,i,t,function(r){e.push(r)}),e}function ve(n,i,t){const e=Object.keys(t),r=e.length;let a=0;return{[Symbol.iterator](){return this},next(){let o=null;do{if(a>=r)return n&&n.wrap(t),{done:!0};const s=t[e[a++]],u=s.source,h=s.target;if(o=u===i?h:u,n&&n.has(o.key)){o=null;continue}}while(o===null);return{done:!1,value:{neighbor:o.key,attributes:o.attributes}}}}}function gr(n,i,t){if(n!=="mixed"){if(n==="undirected")return ve(null,t,t.undirected);if(typeof i=="string")return ve(null,t,t[i])}let e=ce();const r=new Ne;return n!=="undirected"&&(i!=="out"&&(e=Q(e,ve(r,t,t.in))),i!=="in"&&(e=Q(e,ve(r,t,t.out)))),n!=="directed"&&(e=Q(e,ve(r,t,t.undirected))),e}function pr(n,i){const{name:t,type:e,direction:r}=i;n.prototype[t]=function(a){if(e!=="mixed"&&this.type!=="mixed"&&e!==this.type)return[];a=""+a;const o=this._nodes.get(a);if(typeof o>"u")throw new w(`Graph.${t}: could not find the "${a}" node in the graph.`);return fr(e==="mixed"?this.type:e,r,o)}}function mr(n,i){const{name:t,type:e,direction:r}=i,a="forEach"+t[0].toUpperCase()+t.slice(1,-1);n.prototype[a]=function(h,d){if(e!=="mixed"&&this.type!=="mixed"&&e!==this.type)return;h=""+h;const l=this._nodes.get(h);if(typeof l>"u")throw new w(`Graph.${a}: could not find the "${h}" node in the graph.`);it(!1,e==="mixed"?this.type:e,r,l,d)};const o="map"+t[0].toUpperCase()+t.slice(1);n.prototype[o]=function(h,d){const l=[];return this[a](h,(c,f)=>{l.push(d(c,f))}),l};const s="filter"+t[0].toUpperCase()+t.slice(1);n.prototype[s]=function(h,d){const l=[];return this[a](h,(c,f)=>{d(c,f)&&l.push(c)}),l};const u="reduce"+t[0].toUpperCase()+t.slice(1);n.prototype[u]=function(h,d,l){if(arguments.length<3)throw new C(`Graph.${u}: missing initial value. You must provide it because the callback takes more than one argument and we cannot infer the initial value from the first iteration, as you could with a simple array.`);let c=l;return this[a](h,(f,y)=>{c=d(c,f,y)}),c}}function vr(n,i){const{name:t,type:e,direction:r}=i,a=t[0].toUpperCase()+t.slice(1,-1),o="find"+a;n.prototype[o]=function(h,d){if(e!=="mixed"&&this.type!=="mixed"&&e!==this.type)return;h=""+h;const l=this._nodes.get(h);if(typeof l>"u")throw new w(`Graph.${o}: could not find the "${h}" node in the graph.`);return it(!0,e==="mixed"?this.type:e,r,l,d)};const s="some"+a;n.prototype[s]=function(h,d){return!!this[o](h,d)};const u="every"+a;n.prototype[u]=function(h,d){return!this[o](h,(c,f)=>!d(c,f))}}function yr(n,i){const{name:t,type:e,direction:r}=i,a=t.slice(0,-1)+"Entries";n.prototype[a]=function(o){if(e!=="mixed"&&this.type!=="mixed"&&e!==this.type)return ce();o=""+o;const s=this._nodes.get(o);if(typeof s>"u")throw new w(`Graph.${a}: could not find the "${o}" node in the graph.`);return gr(e==="mixed"?this.type:e,r,s)}}function br(n){cr.forEach(i=>{pr(n,i),mr(n,i),vr(n,i),yr(n,i)})}function Ce(n,i,t,e,r){const a=e._nodes.values(),o=e.type;let s,u,h,d,l,c;for(;s=a.next(),s.done!==!0;){let f=!1;if(u=s.value,o!=="undirected"){d=u.out;for(h in d){l=d[h];do c=l.target,f=!0,r(u.key,c.key,u.attributes,c.attributes,l.key,l.attributes,l.undirected),l=l.next;while(l)}}if(o!=="directed"){d=u.undirected;for(h in d)if(!(i&&u.key>h)){l=d[h];do c=l.target,c.key!==h&&(c=l.source),f=!0,r(u.key,c.key,u.attributes,c.attributes,l.key,l.attributes,l.undirected),l=l.next;while(l)}}t&&!f&&r(u.key,null,u.attributes,null,null,null,null)}}function wr(n,i){const t={key:n};return Ot(i.attributes)||(t.attributes=I({},i.attributes)),t}function Er(n,i,t){const e={key:i,source:t.source.key,target:t.target.key};return Ot(t.attributes)||(e.attributes=I({},t.attributes)),n==="mixed"&&t.undirected&&(e.undirected=!0),e}function _r(n){if(!M(n))throw new C('Graph.import: invalid serialized node. A serialized node should be a plain object with at least a "key" property.');if(!("key"in n))throw new C("Graph.import: serialized node is missing its key.");if("attributes"in n&&(!M(n.attributes)||n.attributes===null))throw new C("Graph.import: invalid attributes. Attributes should be a plain object, null or omitted.")}function xr(n){if(!M(n))throw new C('Graph.import: invalid serialized edge. A serialized edge should be a plain object with at least a "source" & "target" property.');if(!("source"in n))throw new C("Graph.import: serialized edge is missing its source.");if(!("target"in n))throw new C("Graph.import: serialized edge is missing its target.");if("attributes"in n&&(!M(n.attributes)||n.attributes===null))throw new C("Graph.import: invalid attributes. Attributes should be a plain object, null or omitted.");if("undirected"in n&&typeof n.undirected!="boolean")throw new C("Graph.import: invalid undirectedness information. Undirected should be boolean or omitted.")}const Tr=Si(),Cr=new Set(["directed","undirected","mixed"]),dt=new Set(["domain","_events","_eventsCount","_maxListeners"]),Sr=[{name:n=>`${n}Edge`,generateKey:!0},{name:n=>`${n}DirectedEdge`,generateKey:!0,type:"directed"},{name:n=>`${n}UndirectedEdge`,generateKey:!0,type:"undirected"},{name:n=>`${n}EdgeWithKey`},{name:n=>`${n}DirectedEdgeWithKey`,type:"directed"},{name:n=>`${n}UndirectedEdgeWithKey`,type:"undirected"}],kr={allowSelfLoops:!0,multi:!1,type:"mixed"};function Ar(n,i,t){if(t&&!M(t))throw new C(`Graph.addNode: invalid attributes. Expecting an object but got "${t}"`);if(i=""+i,t=t||{},n._nodes.has(i))throw new R(`Graph.addNode: the "${i}" node already exist in the graph.`);const e=new n.NodeDataClass(i,t);return n._nodes.set(i,e),n.emit("nodeAdded",{key:i,attributes:t}),e}function lt(n,i,t){const e=new n.NodeDataClass(i,t);return n._nodes.set(i,e),n.emit("nodeAdded",{key:i,attributes:t}),e}function Vt(n,i,t,e,r,a,o,s){if(!e&&n.type==="undirected")throw new R(`Graph.${i}: you cannot add a directed edge to an undirected graph. Use the #.addEdge or #.addUndirectedEdge instead.`);if(e&&n.type==="directed")throw new R(`Graph.${i}: you cannot add an undirected edge to a directed graph. Use the #.addEdge or #.addDirectedEdge instead.`);if(s&&!M(s))throw new C(`Graph.${i}: invalid attributes. Expecting an object but got "${s}"`);if(a=""+a,o=""+o,s=s||{},!n.allowSelfLoops&&a===o)throw new R(`Graph.${i}: source & target are the same ("${a}"), thus creating a loop explicitly forbidden by this graph 'allowSelfLoops' option set to false.`);const u=n._nodes.get(a),h=n._nodes.get(o);if(!u)throw new w(`Graph.${i}: source node "${a}" not found.`);if(!h)throw new w(`Graph.${i}: target node "${o}" not found.`);const d={key:null,undirected:e,source:a,target:o,attributes:s};if(t)r=n._edgeKeyGenerator();else if(r=""+r,n._edges.has(r))throw new R(`Graph.${i}: the "${r}" edge already exists in the graph.`);if(!n.multi&&(e?typeof u.undirected[o]<"u":typeof u.out[o]<"u"))throw new R(`Graph.${i}: an edge linking "${a}" to "${o}" already exists. If you really want to add multiple edges linking those nodes, you should create a multi graph by using the 'multi' option.`);const l=new fe(e,r,u,h,s);n._edges.set(r,l);const c=a===o;return e?(u.undirectedDegree++,h.undirectedDegree++,c&&(u.undirectedLoops++,n._undirectedSelfLoopCount++)):(u.outDegree++,h.inDegree++,c&&(u.directedLoops++,n._directedSelfLoopCount++)),n.multi?l.attachMulti():l.attach(),e?n._undirectedSize++:n._directedSize++,d.key=r,n.emit("edgeAdded",d),r}function Rr(n,i,t,e,r,a,o,s,u){if(!e&&n.type==="undirected")throw new R(`Graph.${i}: you cannot merge/update a directed edge to an undirected graph. Use the #.mergeEdge/#.updateEdge or #.addUndirectedEdge instead.`);if(e&&n.type==="directed")throw new R(`Graph.${i}: you cannot merge/update an undirected edge to a directed graph. Use the #.mergeEdge/#.updateEdge or #.addDirectedEdge instead.`);if(s){if(u){if(typeof s!="function")throw new C(`Graph.${i}: invalid updater function. Expecting a function but got "${s}"`)}else if(!M(s))throw new C(`Graph.${i}: invalid attributes. Expecting an object but got "${s}"`)}a=""+a,o=""+o;let h;if(u&&(h=s,s=void 0),!n.allowSelfLoops&&a===o)throw new R(`Graph.${i}: source & target are the same ("${a}"), thus creating a loop explicitly forbidden by this graph 'allowSelfLoops' option set to false.`);let d=n._nodes.get(a),l=n._nodes.get(o),c,f;if(!t&&(c=n._edges.get(r),c)){if((c.source.key!==a||c.target.key!==o)&&(!e||c.source.key!==o||c.target.key!==a))throw new R(`Graph.${i}: inconsistency detected when attempting to merge the "${r}" edge with "${a}" source & "${o}" target vs. ("${c.source.key}", "${c.target.key}").`);f=c}if(!f&&!n.multi&&d&&(f=e?d.undirected[o]:d.out[o]),f){const x=[f.key,!1,!1,!1];if(u?!h:!s)return x;if(u){const T=f.attributes;f.attributes=h(T),n.emit("edgeAttributesUpdated",{type:"replace",key:f.key,attributes:f.attributes})}else I(f.attributes,s),n.emit("edgeAttributesUpdated",{type:"merge",key:f.key,attributes:f.attributes,data:s});return x}s=s||{},u&&h&&(s=h(s));const y={key:null,undirected:e,source:a,target:o,attributes:s};if(t)r=n._edgeKeyGenerator();else if(r=""+r,n._edges.has(r))throw new R(`Graph.${i}: the "${r}" edge already exists in the graph.`);let b=!1,E=!1;d||(d=lt(n,a,{}),b=!0,a===o&&(l=d,E=!0)),l||(l=lt(n,o,{}),E=!0),c=new fe(e,r,d,l,s),n._edges.set(r,c);const k=a===o;return e?(d.undirectedDegree++,l.undirectedDegree++,k&&(d.undirectedLoops++,n._undirectedSelfLoopCount++)):(d.outDegree++,l.inDegree++,k&&(d.directedLoops++,n._directedSelfLoopCount++)),n.multi?c.attachMulti():c.attach(),e?n._undirectedSize++:n._directedSize++,y.key=r,n.emit("edgeAdded",y),[r,!0,b,E]}function ue(n,i){n._edges.delete(i.key);const{source:t,target:e,attributes:r}=i,a=i.undirected,o=t===e;a?(t.undirectedDegree--,e.undirectedDegree--,o&&(t.undirectedLoops--,n._undirectedSelfLoopCount--)):(t.outDegree--,e.inDegree--,o&&(t.directedLoops--,n._directedSelfLoopCount--)),n.multi?i.detachMulti():i.detach(),a?n._undirectedSize--:n._directedSize--,n.emit("edgeDropped",{key:i.key,attributes:r,source:t.key,target:e.key,undirected:a})}class N extends Mt.EventEmitter{constructor(i){if(super(),i=I({},kr,i),typeof i.multi!="boolean")throw new C(`Graph.constructor: invalid 'multi' option. Expecting a boolean but got "${i.multi}".`);if(!Cr.has(i.type))throw new C(`Graph.constructor: invalid 'type' option. Should be one of "mixed", "directed" or "undirected" but got "${i.type}".`);if(typeof i.allowSelfLoops!="boolean")throw new C(`Graph.constructor: invalid 'allowSelfLoops' option. Expecting a boolean but got "${i.allowSelfLoops}".`);const t=i.type==="mixed"?Ut:i.type==="directed"?$t:zt;z(this,"NodeDataClass",t);const e="geid_"+Tr()+"_";let r=0;const a=()=>{let o;do o=e+r++;while(this._edges.has(o));return o};z(this,"_attributes",{}),z(this,"_nodes",new Map),z(this,"_edges",new Map),z(this,"_directedSize",0),z(this,"_undirectedSize",0),z(this,"_directedSelfLoopCount",0),z(this,"_undirectedSelfLoopCount",0),z(this,"_edgeKeyGenerator",a),z(this,"_options",i),dt.forEach(o=>z(this,o,this[o])),V(this,"order",()=>this._nodes.size),V(this,"size",()=>this._edges.size),V(this,"directedSize",()=>this._directedSize),V(this,"undirectedSize",()=>this._undirectedSize),V(this,"selfLoopCount",()=>this._directedSelfLoopCount+this._undirectedSelfLoopCount),V(this,"directedSelfLoopCount",()=>this._directedSelfLoopCount),V(this,"undirectedSelfLoopCount",()=>this._undirectedSelfLoopCount),V(this,"multi",this._options.multi),V(this,"type",this._options.type),V(this,"allowSelfLoops",this._options.allowSelfLoops),V(this,"implementation",()=>"graphology")}_resetInstanceCounters(){this._directedSize=0,this._undirectedSize=0,this._directedSelfLoopCount=0,this._undirectedSelfLoopCount=0}hasNode(i){return this._nodes.has(""+i)}hasDirectedEdge(i,t){if(this.type==="undirected")return!1;if(arguments.length===1){const e=""+i,r=this._edges.get(e);return!!r&&!r.undirected}else if(arguments.length===2){i=""+i,t=""+t;const e=this._nodes.get(i);return e?e.out.hasOwnProperty(t):!1}throw new C(`Graph.hasDirectedEdge: invalid arity (${arguments.length}, instead of 1 or 2). You can either ask for an edge id or for the existence of an edge between a source & a target.`)}hasUndirectedEdge(i,t){if(this.type==="directed")return!1;if(arguments.length===1){const e=""+i,r=this._edges.get(e);return!!r&&r.undirected}else if(arguments.length===2){i=""+i,t=""+t;const e=this._nodes.get(i);return e?e.undirected.hasOwnProperty(t):!1}throw new C(`Graph.hasDirectedEdge: invalid arity (${arguments.length}, instead of 1 or 2). You can either ask for an edge id or for the existence of an edge between a source & a target.`)}hasEdge(i,t){if(arguments.length===1){const e=""+i;return this._edges.has(e)}else if(arguments.length===2){i=""+i,t=""+t;const e=this._nodes.get(i);return e?typeof e.out<"u"&&e.out.hasOwnProperty(t)||typeof e.undirected<"u"&&e.undirected.hasOwnProperty(t):!1}throw new C(`Graph.hasEdge: invalid arity (${arguments.length}, instead of 1 or 2). You can either ask for an edge id or for the existence of an edge between a source & a target.`)}directedEdge(i,t){if(this.type==="undirected")return;if(i=""+i,t=""+t,this.multi)throw new R("Graph.directedEdge: this method is irrelevant with multigraphs since there might be multiple edges between source & target. See #.directedEdges instead.");const e=this._nodes.get(i);if(!e)throw new w(`Graph.directedEdge: could not find the "${i}" source node in the graph.`);if(!this._nodes.has(t))throw new w(`Graph.directedEdge: could not find the "${t}" target node in the graph.`);const r=e.out&&e.out[t]||void 0;if(r)return r.key}undirectedEdge(i,t){if(this.type==="directed")return;if(i=""+i,t=""+t,this.multi)throw new R("Graph.undirectedEdge: this method is irrelevant with multigraphs since there might be multiple edges between source & target. See #.undirectedEdges instead.");const e=this._nodes.get(i);if(!e)throw new w(`Graph.undirectedEdge: could not find the "${i}" source node in the graph.`);if(!this._nodes.has(t))throw new w(`Graph.undirectedEdge: could not find the "${t}" target node in the graph.`);const r=e.undirected&&e.undirected[t]||void 0;if(r)return r.key}edge(i,t){if(this.multi)throw new R("Graph.edge: this method is irrelevant with multigraphs since there might be multiple edges between source & target. See #.edges instead.");i=""+i,t=""+t;const e=this._nodes.get(i);if(!e)throw new w(`Graph.edge: could not find the "${i}" source node in the graph.`);if(!this._nodes.has(t))throw new w(`Graph.edge: could not find the "${t}" target node in the graph.`);const r=e.out&&e.out[t]||e.undirected&&e.undirected[t]||void 0;if(r)return r.key}areDirectedNeighbors(i,t){i=""+i,t=""+t;const e=this._nodes.get(i);if(!e)throw new w(`Graph.areDirectedNeighbors: could not find the "${i}" node in the graph.`);return this.type==="undirected"?!1:t in e.in||t in e.out}areOutNeighbors(i,t){i=""+i,t=""+t;const e=this._nodes.get(i);if(!e)throw new w(`Graph.areOutNeighbors: could not find the "${i}" node in the graph.`);return this.type==="undirected"?!1:t in e.out}areInNeighbors(i,t){i=""+i,t=""+t;const e=this._nodes.get(i);if(!e)throw new w(`Graph.areInNeighbors: could not find the "${i}" node in the graph.`);return this.type==="undirected"?!1:t in e.in}areUndirectedNeighbors(i,t){i=""+i,t=""+t;const e=this._nodes.get(i);if(!e)throw new w(`Graph.areUndirectedNeighbors: could not find the "${i}" node in the graph.`);return this.type==="directed"?!1:t in e.undirected}areNeighbors(i,t){i=""+i,t=""+t;const e=this._nodes.get(i);if(!e)throw new w(`Graph.areNeighbors: could not find the "${i}" node in the graph.`);return this.type!=="undirected"&&(t in e.in||t in e.out)||this.type!=="directed"&&t in e.undirected}areInboundNeighbors(i,t){i=""+i,t=""+t;const e=this._nodes.get(i);if(!e)throw new w(`Graph.areInboundNeighbors: could not find the "${i}" node in the graph.`);return this.type!=="undirected"&&t in e.in||this.type!=="directed"&&t in e.undirected}areOutboundNeighbors(i,t){i=""+i,t=""+t;const e=this._nodes.get(i);if(!e)throw new w(`Graph.areOutboundNeighbors: could not find the "${i}" node in the graph.`);return this.type!=="undirected"&&t in e.out||this.type!=="directed"&&t in e.undirected}inDegree(i){i=""+i;const t=this._nodes.get(i);if(!t)throw new w(`Graph.inDegree: could not find the "${i}" node in the graph.`);return this.type==="undirected"?0:t.inDegree}outDegree(i){i=""+i;const t=this._nodes.get(i);if(!t)throw new w(`Graph.outDegree: could not find the "${i}" node in the graph.`);return this.type==="undirected"?0:t.outDegree}directedDegree(i){i=""+i;const t=this._nodes.get(i);if(!t)throw new w(`Graph.directedDegree: could not find the "${i}" node in the graph.`);return this.type==="undirected"?0:t.inDegree+t.outDegree}undirectedDegree(i){i=""+i;const t=this._nodes.get(i);if(!t)throw new w(`Graph.undirectedDegree: could not find the "${i}" node in the graph.`);return this.type==="directed"?0:t.undirectedDegree}inboundDegree(i){i=""+i;const t=this._nodes.get(i);if(!t)throw new w(`Graph.inboundDegree: could not find the "${i}" node in the graph.`);let e=0;return this.type!=="directed"&&(e+=t.undirectedDegree),this.type!=="undirected"&&(e+=t.inDegree),e}outboundDegree(i){i=""+i;const t=this._nodes.get(i);if(!t)throw new w(`Graph.outboundDegree: could not find the "${i}" node in the graph.`);let e=0;return this.type!=="directed"&&(e+=t.undirectedDegree),this.type!=="undirected"&&(e+=t.outDegree),e}degree(i){i=""+i;const t=this._nodes.get(i);if(!t)throw new w(`Graph.degree: could not find the "${i}" node in the graph.`);let e=0;return this.type!=="directed"&&(e+=t.undirectedDegree),this.type!=="undirected"&&(e+=t.inDegree+t.outDegree),e}inDegreeWithoutSelfLoops(i){i=""+i;const t=this._nodes.get(i);if(!t)throw new w(`Graph.inDegreeWithoutSelfLoops: could not find the "${i}" node in the graph.`);return this.type==="undirected"?0:t.inDegree-t.directedLoops}outDegreeWithoutSelfLoops(i){i=""+i;const t=this._nodes.get(i);if(!t)throw new w(`Graph.outDegreeWithoutSelfLoops: could not find the "${i}" node in the graph.`);return this.type==="undirected"?0:t.outDegree-t.directedLoops}directedDegreeWithoutSelfLoops(i){i=""+i;const t=this._nodes.get(i);if(!t)throw new w(`Graph.directedDegreeWithoutSelfLoops: could not find the "${i}" node in the graph.`);return this.type==="undirected"?0:t.inDegree+t.outDegree-t.directedLoops*2}undirectedDegreeWithoutSelfLoops(i){i=""+i;const t=this._nodes.get(i);if(!t)throw new w(`Graph.undirectedDegreeWithoutSelfLoops: could not find the "${i}" node in the graph.`);return this.type==="directed"?0:t.undirectedDegree-t.undirectedLoops*2}inboundDegreeWithoutSelfLoops(i){i=""+i;const t=this._nodes.get(i);if(!t)throw new w(`Graph.inboundDegreeWithoutSelfLoops: could not find the "${i}" node in the graph.`);let e=0,r=0;return this.type!=="directed"&&(e+=t.undirectedDegree,r+=t.undirectedLoops*2),this.type!=="undirected"&&(e+=t.inDegree,r+=t.directedLoops),e-r}outboundDegreeWithoutSelfLoops(i){i=""+i;const t=this._nodes.get(i);if(!t)throw new w(`Graph.outboundDegreeWithoutSelfLoops: could not find the "${i}" node in the graph.`);let e=0,r=0;return this.type!=="directed"&&(e+=t.undirectedDegree,r+=t.undirectedLoops*2),this.type!=="undirected"&&(e+=t.outDegree,r+=t.directedLoops),e-r}degreeWithoutSelfLoops(i){i=""+i;const t=this._nodes.get(i);if(!t)throw new w(`Graph.degreeWithoutSelfLoops: could not find the "${i}" node in the graph.`);let e=0,r=0;return this.type!=="directed"&&(e+=t.undirectedDegree,r+=t.undirectedLoops*2),this.type!=="undirected"&&(e+=t.inDegree+t.outDegree,r+=t.directedLoops*2),e-r}source(i){i=""+i;const t=this._edges.get(i);if(!t)throw new w(`Graph.source: could not find the "${i}" edge in the graph.`);return t.source.key}target(i){i=""+i;const t=this._edges.get(i);if(!t)throw new w(`Graph.target: could not find the "${i}" edge in the graph.`);return t.target.key}extremities(i){i=""+i;const t=this._edges.get(i);if(!t)throw new w(`Graph.extremities: could not find the "${i}" edge in the graph.`);return[t.source.key,t.target.key]}opposite(i,t){i=""+i,t=""+t;const e=this._edges.get(t);if(!e)throw new w(`Graph.opposite: could not find the "${t}" edge in the graph.`);const r=e.source.key,a=e.target.key;if(i===r)return a;if(i===a)return r;throw new w(`Graph.opposite: the "${i}" node is not attached to the "${t}" edge (${r}, ${a}).`)}hasExtremity(i,t){i=""+i,t=""+t;const e=this._edges.get(i);if(!e)throw new w(`Graph.hasExtremity: could not find the "${i}" edge in the graph.`);return e.source.key===t||e.target.key===t}isUndirected(i){i=""+i;const t=this._edges.get(i);if(!t)throw new w(`Graph.isUndirected: could not find the "${i}" edge in the graph.`);return t.undirected}isDirected(i){i=""+i;const t=this._edges.get(i);if(!t)throw new w(`Graph.isDirected: could not find the "${i}" edge in the graph.`);return!t.undirected}isSelfLoop(i){i=""+i;const t=this._edges.get(i);if(!t)throw new w(`Graph.isSelfLoop: could not find the "${i}" edge in the graph.`);return t.source===t.target}addNode(i,t){return Ar(this,i,t).key}mergeNode(i,t){if(t&&!M(t))throw new C(`Graph.mergeNode: invalid attributes. Expecting an object but got "${t}"`);i=""+i,t=t||{};let e=this._nodes.get(i);return e?(t&&(I(e.attributes,t),this.emit("nodeAttributesUpdated",{type:"merge",key:i,attributes:e.attributes,data:t})),[i,!1]):(e=new this.NodeDataClass(i,t),this._nodes.set(i,e),this.emit("nodeAdded",{key:i,attributes:t}),[i,!0])}updateNode(i,t){if(t&&typeof t!="function")throw new C(`Graph.updateNode: invalid updater function. Expecting a function but got "${t}"`);i=""+i;let e=this._nodes.get(i);if(e){if(t){const a=e.attributes;e.attributes=t(a),this.emit("nodeAttributesUpdated",{type:"replace",key:i,attributes:e.attributes})}return[i,!1]}const r=t?t({}):{};return e=new this.NodeDataClass(i,r),this._nodes.set(i,e),this.emit("nodeAdded",{key:i,attributes:r}),[i,!0]}dropNode(i){i=""+i;const t=this._nodes.get(i);if(!t)throw new w(`Graph.dropNode: could not find the "${i}" node in the graph.`);let e;if(this.type!=="undirected"){for(const r in t.out){e=t.out[r];do ue(this,e),e=e.next;while(e)}for(const r in t.in){e=t.in[r];do ue(this,e),e=e.next;while(e)}}if(this.type!=="directed")for(const r in t.undirected){e=t.undirected[r];do ue(this,e),e=e.next;while(e)}this._nodes.delete(i),this.emit("nodeDropped",{key:i,attributes:t.attributes})}dropEdge(i){let t;if(arguments.length>1){const e=""+arguments[0],r=""+arguments[1];if(t=B(this,e,r,this.type),!t)throw new w(`Graph.dropEdge: could not find the "${e}" -> "${r}" edge in the graph.`)}else if(i=""+i,t=this._edges.get(i),!t)throw new w(`Graph.dropEdge: could not find the "${i}" edge in the graph.`);return ue(this,t),this}dropDirectedEdge(i,t){if(arguments.length<2)throw new R("Graph.dropDirectedEdge: it does not make sense to try and drop a directed edge by key. What if the edge with this key is undirected? Use #.dropEdge for this purpose instead.");if(this.multi)throw new R("Graph.dropDirectedEdge: cannot use a {source,target} combo when dropping an edge in a MultiGraph since we cannot infer the one you want to delete as there could be multiple ones.");i=""+i,t=""+t;const e=B(this,i,t,"directed");if(!e)throw new w(`Graph.dropDirectedEdge: could not find a "${i}" -> "${t}" edge in the graph.`);return ue(this,e),this}dropUndirectedEdge(i,t){if(arguments.length<2)throw new R("Graph.dropUndirectedEdge: it does not make sense to drop a directed edge by key. What if the edge with this key is undirected? Use #.dropEdge for this purpose instead.");if(this.multi)throw new R("Graph.dropUndirectedEdge: cannot use a {source,target} combo when dropping an edge in a MultiGraph since we cannot infer the one you want to delete as there could be multiple ones.");const e=B(this,i,t,"undirected");if(!e)throw new w(`Graph.dropUndirectedEdge: could not find a "${i}" -> "${t}" edge in the graph.`);return ue(this,e),this}clear(){this._edges.clear(),this._nodes.clear(),this._resetInstanceCounters(),this.emit("cleared")}clearEdges(){const i=this._nodes.values();let t;for(;t=i.next(),t.done!==!0;)t.value.clear();this._edges.clear(),this._resetInstanceCounters(),this.emit("edgesCleared")}getAttribute(i){return this._attributes[i]}getAttributes(){return this._attributes}hasAttribute(i){return this._attributes.hasOwnProperty(i)}setAttribute(i,t){return this._attributes[i]=t,this.emit("attributesUpdated",{type:"set",attributes:this._attributes,name:i}),this}updateAttribute(i,t){if(typeof t!="function")throw new C("Graph.updateAttribute: updater should be a function.");const e=this._attributes[i];return this._attributes[i]=t(e),this.emit("attributesUpdated",{type:"set",attributes:this._attributes,name:i}),this}removeAttribute(i){return delete this._attributes[i],this.emit("attributesUpdated",{type:"remove",attributes:this._attributes,name:i}),this}replaceAttributes(i){if(!M(i))throw new C("Graph.replaceAttributes: provided attributes are not a plain object.");return this._attributes=i,this.emit("attributesUpdated",{type:"replace",attributes:this._attributes}),this}mergeAttributes(i){if(!M(i))throw new C("Graph.mergeAttributes: provided attributes are not a plain object.");return I(this._attributes,i),this.emit("attributesUpdated",{type:"merge",attributes:this._attributes,data:i}),this}updateAttributes(i){if(typeof i!="function")throw new C("Graph.updateAttributes: provided updater is not a function.");return this._attributes=i(this._attributes),this.emit("attributesUpdated",{type:"update",attributes:this._attributes}),this}updateEachNodeAttributes(i,t){if(typeof i!="function")throw new C("Graph.updateEachNodeAttributes: expecting an updater function.");if(t&&!ht(t))throw new C("Graph.updateEachNodeAttributes: invalid hints. Expecting an object having the following shape: {attributes?: [string]}");const e=this._nodes.values();let r,a;for(;r=e.next(),r.done!==!0;)a=r.value,a.attributes=i(a.key,a.attributes);this.emit("eachNodeAttributesUpdated",{hints:t||null})}updateEachEdgeAttributes(i,t){if(typeof i!="function")throw new C("Graph.updateEachEdgeAttributes: expecting an updater function.");if(t&&!ht(t))throw new C("Graph.updateEachEdgeAttributes: invalid hints. Expecting an object having the following shape: {attributes?: [string]}");const e=this._edges.values();let r,a,o,s;for(;r=e.next(),r.done!==!0;)a=r.value,o=a.source,s=a.target,a.attributes=i(a.key,a.attributes,o.key,s.key,o.attributes,s.attributes,a.undirected);this.emit("eachEdgeAttributesUpdated",{hints:t||null})}forEachAdjacencyEntry(i){if(typeof i!="function")throw new C("Graph.forEachAdjacencyEntry: expecting a callback.");Ce(!1,!1,!1,this,i)}forEachAdjacencyEntryWithOrphans(i){if(typeof i!="function")throw new C("Graph.forEachAdjacencyEntryWithOrphans: expecting a callback.");Ce(!1,!1,!0,this,i)}forEachAssymetricAdjacencyEntry(i){if(typeof i!="function")throw new C("Graph.forEachAssymetricAdjacencyEntry: expecting a callback.");Ce(!1,!0,!1,this,i)}forEachAssymetricAdjacencyEntryWithOrphans(i){if(typeof i!="function")throw new C("Graph.forEachAssymetricAdjacencyEntryWithOrphans: expecting a callback.");Ce(!1,!0,!0,this,i)}nodes(){return Array.from(this._nodes.keys())}forEachNode(i){if(typeof i!="function")throw new C("Graph.forEachNode: expecting a callback.");const t=this._nodes.values();let e,r;for(;e=t.next(),e.done!==!0;)r=e.value,i(r.key,r.attributes)}findNode(i){if(typeof i!="function")throw new C("Graph.findNode: expecting a callback.");const t=this._nodes.values();let e,r;for(;e=t.next(),e.done!==!0;)if(r=e.value,i(r.key,r.attributes))return r.key}mapNodes(i){if(typeof i!="function")throw new C("Graph.mapNode: expecting a callback.");const t=this._nodes.values();let e,r;const a=new Array(this.order);let o=0;for(;e=t.next(),e.done!==!0;)r=e.value,a[o++]=i(r.key,r.attributes);return a}someNode(i){if(typeof i!="function")throw new C("Graph.someNode: expecting a callback.");const t=this._nodes.values();let e,r;for(;e=t.next(),e.done!==!0;)if(r=e.value,i(r.key,r.attributes))return!0;return!1}everyNode(i){if(typeof i!="function")throw new C("Graph.everyNode: expecting a callback.");const t=this._nodes.values();let e,r;for(;e=t.next(),e.done!==!0;)if(r=e.value,!i(r.key,r.attributes))return!1;return!0}filterNodes(i){if(typeof i!="function")throw new C("Graph.filterNodes: expecting a callback.");const t=this._nodes.values();let e,r;const a=[];for(;e=t.next(),e.done!==!0;)r=e.value,i(r.key,r.attributes)&&a.push(r.key);return a}reduceNodes(i,t){if(typeof i!="function")throw new C("Graph.reduceNodes: expecting a callback.");if(arguments.length<2)throw new C("Graph.reduceNodes: missing initial value. You must provide it because the callback takes more than one argument and we cannot infer the initial value from the first iteration, as you could with a simple array.");let e=t;const r=this._nodes.values();let a,o;for(;a=r.next(),a.done!==!0;)o=a.value,e=i(e,o.key,o.attributes);return e}nodeEntries(){const i=this._nodes.values();return{[Symbol.iterator](){return this},next(){const t=i.next();if(t.done)return t;const e=t.value;return{value:{node:e.key,attributes:e.attributes},done:!1}}}}export(){const i=new Array(this._nodes.size);let t=0;this._nodes.forEach((r,a)=>{i[t++]=wr(a,r)});const e=new Array(this._edges.size);return t=0,this._edges.forEach((r,a)=>{e[t++]=Er(this.type,a,r)}),{options:{type:this.type,multi:this.multi,allowSelfLoops:this.allowSelfLoops},attributes:this.getAttributes(),nodes:i,edges:e}}import(i,t=!1){if(i instanceof N)return i.forEachNode((u,h)=>{t?this.mergeNode(u,h):this.addNode(u,h)}),i.forEachEdge((u,h,d,l,c,f,y)=>{t?y?this.mergeUndirectedEdgeWithKey(u,d,l,h):this.mergeDirectedEdgeWithKey(u,d,l,h):y?this.addUndirectedEdgeWithKey(u,d,l,h):this.addDirectedEdgeWithKey(u,d,l,h)}),this;if(!M(i))throw new C("Graph.import: invalid argument. Expecting a serialized graph or, alternatively, a Graph instance.");if(i.attributes){if(!M(i.attributes))throw new C("Graph.import: invalid attributes. Expecting a plain object.");t?this.mergeAttributes(i.attributes):this.replaceAttributes(i.attributes)}let e,r,a,o,s;if(i.nodes){if(a=i.nodes,!Array.isArray(a))throw new C("Graph.import: invalid nodes. Expecting an array.");for(e=0,r=a.length;e<r;e++){o=a[e],_r(o);const{key:u,attributes:h}=o;t?this.mergeNode(u,h):this.addNode(u,h)}}if(i.edges){let u=!1;if(this.type==="undirected"&&(u=!0),a=i.edges,!Array.isArray(a))throw new C("Graph.import: invalid edges. Expecting an array.");for(e=0,r=a.length;e<r;e++){s=a[e],xr(s);const{source:h,target:d,attributes:l,undirected:c=u}=s;let f;"key"in s?(f=t?c?this.mergeUndirectedEdgeWithKey:this.mergeDirectedEdgeWithKey:c?this.addUndirectedEdgeWithKey:this.addDirectedEdgeWithKey,f.call(this,s.key,h,d,l)):(f=t?c?this.mergeUndirectedEdge:this.mergeDirectedEdge:c?this.addUndirectedEdge:this.addDirectedEdge,f.call(this,h,d,l))}}return this}nullCopy(i){const t=new N(I({},this._options,i));return t.replaceAttributes(I({},this.getAttributes())),t}emptyCopy(i){const t=this.nullCopy(i);return this._nodes.forEach((e,r)=>{const a=I({},e.attributes);e=new t.NodeDataClass(r,a),t._nodes.set(r,e)}),t}copy(i){if(i=i||{},typeof i.type=="string"&&i.type!==this.type&&i.type!=="mixed")throw new R(`Graph.copy: cannot create an incompatible copy from "${this.type}" type to "${i.type}" because this would mean losing information about the current graph.`);if(typeof i.multi=="boolean"&&i.multi!==this.multi&&i.multi!==!0)throw new R("Graph.copy: cannot create an incompatible copy by downgrading a multi graph to a simple one because this would mean losing information about the current graph.");if(typeof i.allowSelfLoops=="boolean"&&i.allowSelfLoops!==this.allowSelfLoops&&i.allowSelfLoops!==!0)throw new R("Graph.copy: cannot create an incompatible copy from a graph allowing self loops to one that does not because this would mean losing information about the current graph.");const t=this.emptyCopy(i),e=this._edges.values();let r,a;for(;r=e.next(),r.done!==!0;)a=r.value,Vt(t,"copy",!1,a.undirected,a.key,a.source.key,a.target.key,I({},a.attributes));return t}toJSON(){return this.export()}toString(){return"[object Graph]"}inspect(){const i={};this._nodes.forEach((a,o)=>{i[o]=a.attributes});const t={},e={};this._edges.forEach((a,o)=>{const s=a.undirected?"--":"->";let u="",h=a.source.key,d=a.target.key,l;a.undirected&&h>d&&(l=h,h=d,d=l);const c=`(${h})${s}(${d})`;o.startsWith("geid_")?this.multi&&(typeof e[c]>"u"?e[c]=0:e[c]++,u+=`${e[c]}. `):u+=`[${o}]: `,u+=c,t[u]=a.attributes});const r={};for(const a in this)this.hasOwnProperty(a)&&!dt.has(a)&&typeof this[a]!="function"&&typeof a!="symbol"&&(r[a]=this[a]);return r.attributes=this._attributes,r.nodes=i,r.edges=t,z(r,"constructor",this.constructor),r}}typeof Symbol<"u"&&(N.prototype[Symbol.for("nodejs.util.inspect.custom")]=N.prototype.inspect);Sr.forEach(n=>{["add","merge","update"].forEach(i=>{const t=n.name(i),e=i==="add"?Vt:Rr;n.generateKey?N.prototype[t]=function(r,a,o){return e(this,t,!0,(n.type||this.type)==="undirected",null,r,a,o,i==="update")}:N.prototype[t]=function(r,a,o,s){return e(this,t,!1,(n.type||this.type)==="undirected",r,a,o,s,i==="update")}})});Oi(N);qi(N);lr(N);br(N);class Kt extends N{constructor(i){const t=I({type:"directed"},i);if("multi"in t&&t.multi!==!1)throw new C("DirectedGraph.from: inconsistent indication that the graph should be multi in given options!");if(t.type!=="directed")throw new C('DirectedGraph.from: inconsistent "'+t.type+'" type in given options!');super(t)}}class Yt extends N{constructor(i){const t=I({type:"undirected"},i);if("multi"in t&&t.multi!==!1)throw new C("UndirectedGraph.from: inconsistent indication that the graph should be multi in given options!");if(t.type!=="undirected")throw new C('UndirectedGraph.from: inconsistent "'+t.type+'" type in given options!');super(t)}}class qt extends N{constructor(i){const t=I({multi:!0},i);if("multi"in t&&t.multi!==!0)throw new C("MultiGraph.from: inconsistent indication that the graph should be simple in given options!");super(t)}}class Zt extends N{constructor(i){const t=I({type:"directed",multi:!0},i);if("multi"in t&&t.multi!==!0)throw new C("MultiDirectedGraph.from: inconsistent indication that the graph should be simple in given options!");if(t.type!=="directed")throw new C('MultiDirectedGraph.from: inconsistent "'+t.type+'" type in given options!');super(t)}}class Xt extends N{constructor(i){const t=I({type:"undirected",multi:!0},i);if("multi"in t&&t.multi!==!0)throw new C("MultiUndirectedGraph.from: inconsistent indication that the graph should be simple in given options!");if(t.type!=="undirected")throw new C('MultiUndirectedGraph.from: inconsistent "'+t.type+'" type in given options!');super(t)}}function ge(n){n.from=function(i,t){const e=I({},i.options,t),r=new n(e);return r.import(i),r}}ge(N);ge(Kt);ge(Yt);ge(qt);ge(Zt);ge(Xt);N.Graph=N;N.DirectedGraph=Kt;N.UndirectedGraph=Yt;N.MultiGraph=qt;N.MultiDirectedGraph=Zt;N.MultiUndirectedGraph=Xt;N.InvalidArgumentsGraphError=C;N.NotFoundGraphError=w;N.UsageGraphError=R;function Lr(n,i){if(typeof n!="object"||!n)return n;var t=n[Symbol.toPrimitive];if(t!==void 0){var e=t.call(n,i);if(typeof e!="object")return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(n)}function _e(n){var i=Lr(n,"string");return typeof i=="symbol"?i:i+""}function U(n,i){if(!(n instanceof i))throw new TypeError("Cannot call a class as a function")}function ct(n,i){for(var t=0;t<i.length;t++){var e=i[t];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(n,_e(e.key),e)}}function $(n,i,t){return i&&ct(n.prototype,i),t&&ct(n,t),Object.defineProperty(n,"prototype",{writable:!1}),n}function de(n){return de=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(i){return i.__proto__||Object.getPrototypeOf(i)},de(n)}function Jt(){try{var n=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Jt=function(){return!!n})()}function Dr(n){if(n===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n}function Nr(n,i){if(i&&(typeof i=="object"||typeof i=="function"))return i;if(i!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Dr(n)}function H(n,i,t){return i=de(i),Nr(n,Jt()?Reflect.construct(i,t||[],de(n).constructor):i.apply(n,t))}function Ye(n,i){return Ye=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Ye(n,i)}function W(n,i){if(typeof i!="function"&&i!==null)throw new TypeError("Super expression must either be null or a function");n.prototype=Object.create(i&&i.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),i&&Ye(n,i)}function Gr(n){if(Array.isArray(n))return n}function Fr(n,i){var t=n==null?null:typeof Symbol<"u"&&n[Symbol.iterator]||n["@@iterator"];if(t!=null){var e,r,a,o,s=[],u=!0,h=!1;try{if(a=(t=t.call(n)).next,i===0){if(Object(t)!==t)return;u=!1}else for(;!(u=(e=a.call(t)).done)&&(s.push(e.value),s.length!==i);u=!0);}catch(d){h=!0,r=d}finally{try{if(!u&&t.return!=null&&(o=t.return(),Object(o)!==o))return}finally{if(h)throw r}}return s}}function qe(n,i){(i==null||i>n.length)&&(i=n.length);for(var t=0,e=Array(i);t<i;t++)e[t]=n[t];return e}function Qt(n,i){if(n){if(typeof n=="string")return qe(n,i);var t={}.toString.call(n).slice(8,-1);return t==="Object"&&n.constructor&&(t=n.constructor.name),t==="Map"||t==="Set"?Array.from(n):t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?qe(n,i):void 0}}function Pr(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function le(n,i){return Gr(n)||Fr(n,i)||Qt(n,i)||Pr()}var $e={black:"#000000",silver:"#C0C0C0",gray:"#808080",grey:"#808080",white:"#FFFFFF",maroon:"#800000",red:"#FF0000",purple:"#800080",fuchsia:"#FF00FF",green:"#008000",lime:"#00FF00",olive:"#808000",yellow:"#FFFF00",navy:"#000080",blue:"#0000FF",teal:"#008080",aqua:"#00FFFF",darkblue:"#00008B",mediumblue:"#0000CD",darkgreen:"#006400",darkcyan:"#008B8B",deepskyblue:"#00BFFF",darkturquoise:"#00CED1",mediumspringgreen:"#00FA9A",springgreen:"#00FF7F",cyan:"#00FFFF",midnightblue:"#191970",dodgerblue:"#1E90FF",lightseagreen:"#20B2AA",forestgreen:"#228B22",seagreen:"#2E8B57",darkslategray:"#2F4F4F",darkslategrey:"#2F4F4F",limegreen:"#32CD32",mediumseagreen:"#3CB371",turquoise:"#40E0D0",royalblue:"#4169E1",steelblue:"#4682B4",darkslateblue:"#483D8B",mediumturquoise:"#48D1CC",indigo:"#4B0082",darkolivegreen:"#556B2F",cadetblue:"#5F9EA0",cornflowerblue:"#6495ED",rebeccapurple:"#663399",mediumaquamarine:"#66CDAA",dimgray:"#696969",dimgrey:"#696969",slateblue:"#6A5ACD",olivedrab:"#6B8E23",slategray:"#708090",slategrey:"#708090",lightslategray:"#778899",lightslategrey:"#778899",mediumslateblue:"#7B68EE",lawngreen:"#7CFC00",chartreuse:"#7FFF00",aquamarine:"#7FFFD4",skyblue:"#87CEEB",lightskyblue:"#87CEFA",blueviolet:"#8A2BE2",darkred:"#8B0000",darkmagenta:"#8B008B",saddlebrown:"#8B4513",darkseagreen:"#8FBC8F",lightgreen:"#90EE90",mediumpurple:"#9370DB",darkviolet:"#9400D3",palegreen:"#98FB98",darkorchid:"#9932CC",yellowgreen:"#9ACD32",sienna:"#A0522D",brown:"#A52A2A",darkgray:"#A9A9A9",darkgrey:"#A9A9A9",lightblue:"#ADD8E6",greenyellow:"#ADFF2F",paleturquoise:"#AFEEEE",lightsteelblue:"#B0C4DE",powderblue:"#B0E0E6",firebrick:"#B22222",darkgoldenrod:"#B8860B",mediumorchid:"#BA55D3",rosybrown:"#BC8F8F",darkkhaki:"#BDB76B",mediumvioletred:"#C71585",indianred:"#CD5C5C",peru:"#CD853F",chocolate:"#D2691E",tan:"#D2B48C",lightgray:"#D3D3D3",lightgrey:"#D3D3D3",thistle:"#D8BFD8",orchid:"#DA70D6",goldenrod:"#DAA520",palevioletred:"#DB7093",crimson:"#DC143C",gainsboro:"#DCDCDC",plum:"#DDA0DD",burlywood:"#DEB887",lightcyan:"#E0FFFF",lavender:"#E6E6FA",darksalmon:"#E9967A",violet:"#EE82EE",palegoldenrod:"#EEE8AA",lightcoral:"#F08080",khaki:"#F0E68C",aliceblue:"#F0F8FF",honeydew:"#F0FFF0",azure:"#F0FFFF",sandybrown:"#F4A460",wheat:"#F5DEB3",beige:"#F5F5DC",whitesmoke:"#F5F5F5",mintcream:"#F5FFFA",ghostwhite:"#F8F8FF",salmon:"#FA8072",antiquewhite:"#FAEBD7",linen:"#FAF0E6",lightgoldenrodyellow:"#FAFAD2",oldlace:"#FDF5E6",magenta:"#FF00FF",deeppink:"#FF1493",orangered:"#FF4500",tomato:"#FF6347",hotpink:"#FF69B4",coral:"#FF7F50",darkorange:"#FF8C00",lightsalmon:"#FFA07A",orange:"#FFA500",lightpink:"#FFB6C1",pink:"#FFC0CB",gold:"#FFD700",peachpuff:"#FFDAB9",navajowhite:"#FFDEAD",moccasin:"#FFE4B5",bisque:"#FFE4C4",mistyrose:"#FFE4E1",blanchedalmond:"#FFEBCD",papayawhip:"#FFEFD5",lavenderblush:"#FFF0F5",seashell:"#FFF5EE",cornsilk:"#FFF8DC",lemonchiffon:"#FFFACD",floralwhite:"#FFFAF0",snow:"#FFFAFA",lightyellow:"#FFFFE0",ivory:"#FFFFF0"},ei=new Int8Array(4),Le=new Int32Array(ei.buffer,0,1),ti=new Float32Array(ei.buffer,0,1),Ir=/^\s*rgba?\s*\(/,Mr=/^\s*rgba?\s*\(\s*([0-9]*)\s*,\s*([0-9]*)\s*,\s*([0-9]*)(?:\s*,\s*(.*)?)?\)\s*$/;function Or(n){var i=0,t=0,e=0,r=1;if(n[0]==="#")n.length===4?(i=parseInt(n.charAt(1)+n.charAt(1),16),t=parseInt(n.charAt(2)+n.charAt(2),16),e=parseInt(n.charAt(3)+n.charAt(3),16)):(i=parseInt(n.charAt(1)+n.charAt(2),16),t=parseInt(n.charAt(3)+n.charAt(4),16),e=parseInt(n.charAt(5)+n.charAt(6),16)),n.length===9&&(r=parseInt(n.charAt(7)+n.charAt(8),16)/255);else if(Ir.test(n)){var a=n.match(Mr);a&&(i=+a[1],t=+a[2],e=+a[3],a[4]&&(r=+a[4]))}return{r:i,g:t,b:e,a:r}}var he={};for(var Se in $e)he[Se]=pe($e[Se]),he[$e[Se]]=he[Se];function ii(n,i,t,e,r){return Le[0]=e<<24|t<<16|i<<8|n,Le[0]=Le[0]&4278190079,ti[0]}function pe(n){if(n=n.toLowerCase(),typeof he[n]<"u")return he[n];var i=Or(n),t=i.r,e=i.g,r=i.b,a=i.a;a=a*255|0;var o=ii(t,e,r,a);return he[n]=o,o}function ra(n,i){ti[0]=pe(n);var t=Le[0],e=t&255,r=t>>8&255,a=t>>16&255,o=t>>24&255;return[e,r,a,o]}var ze={};function ri(n){if(typeof ze[n]<"u")return ze[n];var i=(n&16711680)>>>16,t=(n&65280)>>>8,e=n&255,r=255,a=ii(i,t,e,r);return ze[n]=a,a}function ft(n,i,t,e){return t+(i<<8)+(n<<16)}function gt(n,i,t,e,r,a){var o=Math.floor(t/a*r),s=Math.floor(n.drawingBufferHeight/a-e/a*r),u=new Uint8Array(4);n.bindFramebuffer(n.FRAMEBUFFER,i),n.readPixels(o,s,1,1,n.RGBA,n.UNSIGNED_BYTE,u);var h=le(u,4),d=h[0],l=h[1],c=h[2],f=h[3];return[d,l,c,f]}function m(n,i,t){return(i=_e(i))in n?Object.defineProperty(n,i,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[i]=t,n}function pt(n,i){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(n);i&&(e=e.filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable})),t.push.apply(t,e)}return t}function S(n){for(var i=1;i<arguments.length;i++){var t=arguments[i]!=null?arguments[i]:{};i%2?pt(Object(t),!0).forEach(function(e){m(n,e,t[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):pt(Object(t)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(t,e))})}return n}function Ur(n,i){for(;!{}.hasOwnProperty.call(n,i)&&(n=de(n))!==null;);return n}function Ze(){return Ze=typeof Reflect<"u"&&Reflect.get?Reflect.get.bind():function(n,i,t){var e=Ur(n,i);if(e){var r=Object.getOwnPropertyDescriptor(e,i);return r.get?r.get.call(arguments.length<3?n:t):r.value}},Ze.apply(null,arguments)}function ni(n,i,t,e){var r=Ze(de(n.prototype),i,t);return typeof r=="function"?function(a){return r.apply(t,a)}:r}function $r(n){return n.normalized?1:n.size}function Be(n){var i=0;return n.forEach(function(t){return i+=$r(t)}),i}function ai(n,i,t){var e=n==="VERTEX"?i.VERTEX_SHADER:i.FRAGMENT_SHADER,r=i.createShader(e);if(r===null)throw new Error("loadShader: error while creating the shader");i.shaderSource(r,t),i.compileShader(r);var a=i.getShaderParameter(r,i.COMPILE_STATUS);if(!a){var o=i.getShaderInfoLog(r);throw i.deleteShader(r),new Error(`loadShader: error while compiling the shader:
`.concat(o,`
`).concat(t))}return r}function zr(n,i){return ai("VERTEX",n,i)}function Br(n,i){return ai("FRAGMENT",n,i)}function Hr(n,i){var t=n.createProgram();if(t===null)throw new Error("loadProgram: error while creating the program.");var e,r;for(e=0,r=i.length;e<r;e++)n.attachShader(t,i[e]);n.linkProgram(t);var a=n.getProgramParameter(t,n.LINK_STATUS);if(!a)throw n.deleteProgram(t),new Error("loadProgram: error while linking the program.");return t}function mt(n){var i=n.gl,t=n.buffer,e=n.program,r=n.vertexShader,a=n.fragmentShader;i.deleteShader(r),i.deleteShader(a),i.deleteProgram(e),i.deleteBuffer(t)}function na(n){return n%1===0?n.toFixed(1):n.toString()}var vt=`#define PICKING_MODE
`,Wr=m(m(m(m(m(m(m(m({},WebGL2RenderingContext.BOOL,1),WebGL2RenderingContext.BYTE,1),WebGL2RenderingContext.UNSIGNED_BYTE,1),WebGL2RenderingContext.SHORT,2),WebGL2RenderingContext.UNSIGNED_SHORT,2),WebGL2RenderingContext.INT,4),WebGL2RenderingContext.UNSIGNED_INT,4),WebGL2RenderingContext.FLOAT,4),oi=function(){function n(i,t,e){U(this,n),m(this,"array",new Float32Array),m(this,"constantArray",new Float32Array),m(this,"capacity",0),m(this,"verticesCount",0);var r=this.getDefinition();if(this.VERTICES=r.VERTICES,this.VERTEX_SHADER_SOURCE=r.VERTEX_SHADER_SOURCE,this.FRAGMENT_SHADER_SOURCE=r.FRAGMENT_SHADER_SOURCE,this.UNIFORMS=r.UNIFORMS,this.ATTRIBUTES=r.ATTRIBUTES,this.METHOD=r.METHOD,this.CONSTANT_ATTRIBUTES="CONSTANT_ATTRIBUTES"in r?r.CONSTANT_ATTRIBUTES:[],this.CONSTANT_DATA="CONSTANT_DATA"in r?r.CONSTANT_DATA:[],this.isInstanced="CONSTANT_ATTRIBUTES"in r,this.ATTRIBUTES_ITEMS_COUNT=Be(this.ATTRIBUTES),this.STRIDE=this.VERTICES*this.ATTRIBUTES_ITEMS_COUNT,this.renderer=e,this.normalProgram=this.getProgramInfo("normal",i,r.VERTEX_SHADER_SOURCE,r.FRAGMENT_SHADER_SOURCE,null),this.pickProgram=t?this.getProgramInfo("pick",i,vt+r.VERTEX_SHADER_SOURCE,vt+r.FRAGMENT_SHADER_SOURCE,t):null,this.isInstanced){var a=Be(this.CONSTANT_ATTRIBUTES);if(this.CONSTANT_DATA.length!==this.VERTICES)throw new Error("Program: error while getting constant data (expected ".concat(this.VERTICES," items, received ").concat(this.CONSTANT_DATA.length," instead)"));this.constantArray=new Float32Array(this.CONSTANT_DATA.length*a);for(var o=0;o<this.CONSTANT_DATA.length;o++){var s=this.CONSTANT_DATA[o];if(s.length!==a)throw new Error("Program: error while getting constant data (one vector has ".concat(s.length," items instead of ").concat(a,")"));for(var u=0;u<s.length;u++)this.constantArray[o*a+u]=s[u]}this.STRIDE=this.ATTRIBUTES_ITEMS_COUNT}}return $(n,[{key:"kill",value:function(){mt(this.normalProgram),this.pickProgram&&(mt(this.pickProgram),this.pickProgram=null)}},{key:"getProgramInfo",value:function(t,e,r,a,o){var s=this.getDefinition(),u=e.createBuffer();if(u===null)throw new Error("Program: error while creating the WebGL buffer.");var h=zr(e,r),d=Br(e,a),l=Hr(e,[h,d]),c={};s.UNIFORMS.forEach(function(b){var E=e.getUniformLocation(l,b);E&&(c[b]=E)});var f={};s.ATTRIBUTES.forEach(function(b){f[b.name]=e.getAttribLocation(l,b.name)});var y;if("CONSTANT_ATTRIBUTES"in s&&(s.CONSTANT_ATTRIBUTES.forEach(function(b){f[b.name]=e.getAttribLocation(l,b.name)}),y=e.createBuffer(),y===null))throw new Error("Program: error while creating the WebGL constant buffer.");return{name:t,program:l,gl:e,frameBuffer:o,buffer:u,constantBuffer:y||{},uniformLocations:c,attributeLocations:f,isPicking:t==="pick",vertexShader:h,fragmentShader:d}}},{key:"bindProgram",value:function(t){var e=this,r=0,a=t.gl,o=t.buffer;this.isInstanced?(a.bindBuffer(a.ARRAY_BUFFER,t.constantBuffer),r=0,this.CONSTANT_ATTRIBUTES.forEach(function(s){return r+=e.bindAttribute(s,t,r,!1)}),a.bufferData(a.ARRAY_BUFFER,this.constantArray,a.STATIC_DRAW),a.bindBuffer(a.ARRAY_BUFFER,t.buffer),r=0,this.ATTRIBUTES.forEach(function(s){return r+=e.bindAttribute(s,t,r,!0)}),a.bufferData(a.ARRAY_BUFFER,this.array,a.DYNAMIC_DRAW)):(a.bindBuffer(a.ARRAY_BUFFER,o),r=0,this.ATTRIBUTES.forEach(function(s){return r+=e.bindAttribute(s,t,r)}),a.bufferData(a.ARRAY_BUFFER,this.array,a.DYNAMIC_DRAW)),a.bindBuffer(a.ARRAY_BUFFER,null)}},{key:"unbindProgram",value:function(t){var e=this;this.isInstanced?(this.CONSTANT_ATTRIBUTES.forEach(function(r){return e.unbindAttribute(r,t,!1)}),this.ATTRIBUTES.forEach(function(r){return e.unbindAttribute(r,t,!0)})):this.ATTRIBUTES.forEach(function(r){return e.unbindAttribute(r,t)})}},{key:"bindAttribute",value:function(t,e,r,a){var o=Wr[t.type];if(typeof o!="number")throw new Error('Program.bind: yet unsupported attribute type "'.concat(t.type,'"'));var s=e.attributeLocations[t.name],u=e.gl;if(s!==-1){u.enableVertexAttribArray(s);var h=this.isInstanced?(a?this.ATTRIBUTES_ITEMS_COUNT:Be(this.CONSTANT_ATTRIBUTES))*Float32Array.BYTES_PER_ELEMENT:this.ATTRIBUTES_ITEMS_COUNT*Float32Array.BYTES_PER_ELEMENT;if(u.vertexAttribPointer(s,t.size,t.type,t.normalized||!1,h,r),this.isInstanced&&a)if(u instanceof WebGL2RenderingContext)u.vertexAttribDivisor(s,1);else{var d=u.getExtension("ANGLE_instanced_arrays");d&&d.vertexAttribDivisorANGLE(s,1)}}return t.size*o}},{key:"unbindAttribute",value:function(t,e,r){var a=e.attributeLocations[t.name],o=e.gl;if(a!==-1&&(o.disableVertexAttribArray(a),this.isInstanced&&r))if(o instanceof WebGL2RenderingContext)o.vertexAttribDivisor(a,0);else{var s=o.getExtension("ANGLE_instanced_arrays");s&&s.vertexAttribDivisorANGLE(a,0)}}},{key:"reallocate",value:function(t){t!==this.capacity&&(this.capacity=t,this.verticesCount=this.VERTICES*t,this.array=new Float32Array(this.isInstanced?this.capacity*this.ATTRIBUTES_ITEMS_COUNT:this.verticesCount*this.ATTRIBUTES_ITEMS_COUNT))}},{key:"hasNothingToRender",value:function(){return this.verticesCount===0}},{key:"renderProgram",value:function(t,e){var r=e.gl,a=e.program;r.enable(r.BLEND),r.useProgram(a),this.setUniforms(t,e),this.drawWebGL(this.METHOD,e)}},{key:"render",value:function(t){this.hasNothingToRender()||(this.pickProgram&&(this.pickProgram.gl.viewport(0,0,t.width*t.pixelRatio/t.downSizingRatio,t.height*t.pixelRatio/t.downSizingRatio),this.bindProgram(this.pickProgram),this.renderProgram(S(S({},t),{},{pixelRatio:t.pixelRatio/t.downSizingRatio}),this.pickProgram),this.unbindProgram(this.pickProgram)),this.normalProgram.gl.viewport(0,0,t.width*t.pixelRatio,t.height*t.pixelRatio),this.bindProgram(this.normalProgram),this.renderProgram(t,this.normalProgram),this.unbindProgram(this.normalProgram))}},{key:"drawWebGL",value:function(t,e){var r=e.gl,a=e.frameBuffer;if(r.bindFramebuffer(r.FRAMEBUFFER,a),!this.isInstanced)r.drawArrays(t,0,this.verticesCount);else if(r instanceof WebGL2RenderingContext)r.drawArraysInstanced(t,0,this.VERTICES,this.capacity);else{var o=r.getExtension("ANGLE_instanced_arrays");o&&o.drawArraysInstancedANGLE(t,0,this.VERTICES,this.capacity)}}}])}(),jr=function(n){function i(){return U(this,i),H(this,i,arguments)}return W(i,n),$(i,[{key:"kill",value:function(){ni(i,"kill",this)([])}},{key:"process",value:function(e,r,a){var o=r*this.STRIDE;if(a.hidden){for(var s=o+this.STRIDE;o<s;o++)this.array[o]=0;return}return this.processVisibleItem(ri(e),o,a)}}])}(oi),rt=function(n){function i(){var t;U(this,i);for(var e=arguments.length,r=new Array(e),a=0;a<e;a++)r[a]=arguments[a];return t=H(this,i,[].concat(r)),m(t,"drawLabel",void 0),t}return W(i,n),$(i,[{key:"kill",value:function(){ni(i,"kill",this)([])}},{key:"process",value:function(e,r,a,o,s){var u=r*this.STRIDE;if(s.hidden||a.hidden||o.hidden){for(var h=u+this.STRIDE;u<h;u++)this.array[u]=0;return}return this.processVisibleItem(ri(e),u,a,o,s)}}])}(oi);function Vr(n,i){return function(){function t(e,r,a){U(this,t),m(this,"drawLabel",i),this.programs=n.map(function(o){return new o(e,r,a)})}return $(t,[{key:"reallocate",value:function(r){this.programs.forEach(function(a){return a.reallocate(r)})}},{key:"process",value:function(r,a,o,s,u){this.programs.forEach(function(h){return h.process(r,a,o,s,u)})}},{key:"render",value:function(r){this.programs.forEach(function(a){return a.render(r)})}},{key:"kill",value:function(){this.programs.forEach(function(r){return r.kill()})}}])}()}function Kr(n,i,t,e,r){var a=r.edgeLabelSize,o=r.edgeLabelFont,s=r.edgeLabelWeight,u=r.edgeLabelColor.attribute?i[r.edgeLabelColor.attribute]||r.edgeLabelColor.color||"#000":r.edgeLabelColor.color,h=i.label;if(h){n.fillStyle=u,n.font="".concat(s," ").concat(a,"px ").concat(o);var d=t.size,l=e.size,c=t.x,f=t.y,y=e.x,b=e.y,E=(c+y)/2,k=(f+b)/2,x=y-c,T=b-f,g=Math.sqrt(x*x+T*T);if(!(g<d+l)){c+=x*d/g,f+=T*d/g,y-=x*l/g,b-=T*l/g,E=(c+y)/2,k=(f+b)/2,x=y-c,T=b-f,g=Math.sqrt(x*x+T*T);var p=n.measureText(h).width;if(p>g){var v="…";for(h=h+v,p=n.measureText(h).width;p>g&&h.length>1;)h=h.slice(0,-2)+v,p=n.measureText(h).width;if(h.length<4)return}var _;x>0?T>0?_=Math.acos(x/g):_=Math.asin(T/g):T>0?_=Math.acos(x/g)+Math.PI:_=Math.asin(x/g)+Math.PI/2,n.save(),n.translate(E,k),n.rotate(_),n.fillText(h,-p/2,i.size/2+a),n.restore()}}}function si(n,i,t){if(i.label){var e=t.labelSize,r=t.labelFont,a=t.labelWeight,o=t.labelColor.attribute?i[t.labelColor.attribute]||t.labelColor.color||"#000":t.labelColor.color;n.fillStyle=o,n.font="".concat(a," ").concat(e,"px ").concat(r),n.fillText(i.label,i.x+i.size+3,i.y+e/3)}}function Yr(n,i,t){var e=t.labelSize,r=t.labelFont,a=t.labelWeight;n.font="".concat(a," ").concat(e,"px ").concat(r),n.fillStyle="#FFF",n.shadowOffsetX=0,n.shadowOffsetY=0,n.shadowBlur=8,n.shadowColor="#000";var o=2;if(typeof i.label=="string"){var s=n.measureText(i.label).width,u=Math.round(s+5),h=Math.round(e+2*o),d=Math.max(i.size,e/2)+o,l=Math.asin(h/2/d),c=Math.sqrt(Math.abs(Math.pow(d,2)-Math.pow(h/2,2)));n.beginPath(),n.moveTo(i.x+c,i.y+h/2),n.lineTo(i.x+d+u,i.y+h/2),n.lineTo(i.x+d+u,i.y-h/2),n.lineTo(i.x+c,i.y-h/2),n.arc(i.x,i.y,d,l,-l),n.closePath(),n.fill()}else n.beginPath(),n.arc(i.x,i.y,i.size+o,0,Math.PI*2),n.closePath(),n.fill();n.shadowOffsetX=0,n.shadowOffsetY=0,n.shadowBlur=0,si(n,i,t)}var qr=`
precision highp float;

varying vec4 v_color;
varying vec2 v_diffVector;
varying float v_radius;

uniform float u_correctionRatio;

const vec4 transparent = vec4(0.0, 0.0, 0.0, 0.0);

void main(void) {
  float border = u_correctionRatio * 2.0;
  float dist = length(v_diffVector) - v_radius + border;

  // No antialiasing for picking mode:
  #ifdef PICKING_MODE
  if (dist > border)
    gl_FragColor = transparent;
  else
    gl_FragColor = v_color;

  #else
  float t = 0.0;
  if (dist > border)
    t = 1.0;
  else if (dist > 0.0)
    t = dist / border;

  gl_FragColor = mix(v_color, transparent, t);
  #endif
}
`,Zr=qr,Xr=`
attribute vec4 a_id;
attribute vec4 a_color;
attribute vec2 a_position;
attribute float a_size;
attribute float a_angle;

uniform mat3 u_matrix;
uniform float u_sizeRatio;
uniform float u_correctionRatio;

varying vec4 v_color;
varying vec2 v_diffVector;
varying float v_radius;
varying float v_border;

const float bias = 255.0 / 254.0;

void main() {
  float size = a_size * u_correctionRatio / u_sizeRatio * 4.0;
  vec2 diffVector = size * vec2(cos(a_angle), sin(a_angle));
  vec2 position = a_position + diffVector;
  gl_Position = vec4(
    (u_matrix * vec3(position, 1)).xy,
    0,
    1
  );

  v_diffVector = diffVector;
  v_radius = size / 2.0;

  #ifdef PICKING_MODE
  // For picking mode, we use the ID as the color:
  v_color = a_id;
  #else
  // For normal mode, we use the color:
  v_color = a_color;
  #endif

  v_color.a *= bias;
}
`,Jr=Xr,ui=WebGLRenderingContext,yt=ui.UNSIGNED_BYTE,He=ui.FLOAT,Qr=["u_sizeRatio","u_correctionRatio","u_matrix"],Ge=function(n){function i(){return U(this,i),H(this,i,arguments)}return W(i,n),$(i,[{key:"getDefinition",value:function(){return{VERTICES:3,VERTEX_SHADER_SOURCE:Jr,FRAGMENT_SHADER_SOURCE:Zr,METHOD:WebGLRenderingContext.TRIANGLES,UNIFORMS:Qr,ATTRIBUTES:[{name:"a_position",size:2,type:He},{name:"a_size",size:1,type:He},{name:"a_color",size:4,type:yt,normalized:!0},{name:"a_id",size:4,type:yt,normalized:!0}],CONSTANT_ATTRIBUTES:[{name:"a_angle",size:1,type:He}],CONSTANT_DATA:[[i.ANGLE_1],[i.ANGLE_2],[i.ANGLE_3]]}}},{key:"processVisibleItem",value:function(e,r,a){var o=this.array,s=pe(a.color);o[r++]=a.x,o[r++]=a.y,o[r++]=a.size,o[r++]=s,o[r++]=e}},{key:"setUniforms",value:function(e,r){var a=r.gl,o=r.uniformLocations,s=o.u_sizeRatio,u=o.u_correctionRatio,h=o.u_matrix;a.uniform1f(u,e.correctionRatio),a.uniform1f(s,e.sizeRatio),a.uniformMatrix3fv(h,!1,e.matrix)}}])}(jr);m(Ge,"ANGLE_1",0);m(Ge,"ANGLE_2",2*Math.PI/3);m(Ge,"ANGLE_3",4*Math.PI/3);var en=`
precision mediump float;

varying vec4 v_color;

void main(void) {
  gl_FragColor = v_color;
}
`,tn=en,rn=`
attribute vec2 a_position;
attribute vec2 a_normal;
attribute float a_radius;
attribute vec3 a_barycentric;

#ifdef PICKING_MODE
attribute vec4 a_id;
#else
attribute vec4 a_color;
#endif

uniform mat3 u_matrix;
uniform float u_sizeRatio;
uniform float u_correctionRatio;
uniform float u_minEdgeThickness;
uniform float u_lengthToThicknessRatio;
uniform float u_widenessToThicknessRatio;

varying vec4 v_color;

const float bias = 255.0 / 254.0;

void main() {
  float minThickness = u_minEdgeThickness;

  float normalLength = length(a_normal);
  vec2 unitNormal = a_normal / normalLength;

  // These first computations are taken from edge.vert.glsl and
  // edge.clamped.vert.glsl. Please read it to get better comments on what's
  // happening:
  float pixelsThickness = max(normalLength / u_sizeRatio, minThickness);
  float webGLThickness = pixelsThickness * u_correctionRatio;
  float webGLNodeRadius = a_radius * 2.0 * u_correctionRatio / u_sizeRatio;
  float webGLArrowHeadLength = webGLThickness * u_lengthToThicknessRatio * 2.0;
  float webGLArrowHeadThickness = webGLThickness * u_widenessToThicknessRatio;

  float da = a_barycentric.x;
  float db = a_barycentric.y;
  float dc = a_barycentric.z;

  vec2 delta = vec2(
      da * (webGLNodeRadius * unitNormal.y)
    + db * ((webGLNodeRadius + webGLArrowHeadLength) * unitNormal.y + webGLArrowHeadThickness * unitNormal.x)
    + dc * ((webGLNodeRadius + webGLArrowHeadLength) * unitNormal.y - webGLArrowHeadThickness * unitNormal.x),

      da * (-webGLNodeRadius * unitNormal.x)
    + db * (-(webGLNodeRadius + webGLArrowHeadLength) * unitNormal.x + webGLArrowHeadThickness * unitNormal.y)
    + dc * (-(webGLNodeRadius + webGLArrowHeadLength) * unitNormal.x - webGLArrowHeadThickness * unitNormal.y)
  );

  vec2 position = (u_matrix * vec3(a_position + delta, 1)).xy;

  gl_Position = vec4(position, 0, 1);

  #ifdef PICKING_MODE
  // For picking mode, we use the ID as the color:
  v_color = a_id;
  #else
  // For normal mode, we use the color:
  v_color = a_color;
  #endif

  v_color.a *= bias;
}
`,nn=rn,hi=WebGLRenderingContext,bt=hi.UNSIGNED_BYTE,ke=hi.FLOAT,an=["u_matrix","u_sizeRatio","u_correctionRatio","u_minEdgeThickness","u_lengthToThicknessRatio","u_widenessToThicknessRatio"],di={extremity:"target",lengthToThicknessRatio:2.5,widenessToThicknessRatio:2};function li(n){var i=S(S({},di),n||{});return function(t){function e(){return U(this,e),H(this,e,arguments)}return W(e,t),$(e,[{key:"getDefinition",value:function(){return{VERTICES:3,VERTEX_SHADER_SOURCE:nn,FRAGMENT_SHADER_SOURCE:tn,METHOD:WebGLRenderingContext.TRIANGLES,UNIFORMS:an,ATTRIBUTES:[{name:"a_position",size:2,type:ke},{name:"a_normal",size:2,type:ke},{name:"a_radius",size:1,type:ke},{name:"a_color",size:4,type:bt,normalized:!0},{name:"a_id",size:4,type:bt,normalized:!0}],CONSTANT_ATTRIBUTES:[{name:"a_barycentric",size:3,type:ke}],CONSTANT_DATA:[[1,0,0],[0,1,0],[0,0,1]]}}},{key:"processVisibleItem",value:function(a,o,s,u,h){if(i.extremity==="source"){var d=[u,s];s=d[0],u=d[1]}var l=h.size||1,c=u.size||1,f=s.x,y=s.y,b=u.x,E=u.y,k=pe(h.color),x=b-f,T=E-y,g=x*x+T*T,p=0,v=0;g&&(g=1/Math.sqrt(g),p=-T*g*l,v=x*g*l);var _=this.array;_[o++]=b,_[o++]=E,_[o++]=-p,_[o++]=-v,_[o++]=c,_[o++]=k,_[o++]=a}},{key:"setUniforms",value:function(a,o){var s=o.gl,u=o.uniformLocations,h=u.u_matrix,d=u.u_sizeRatio,l=u.u_correctionRatio,c=u.u_minEdgeThickness,f=u.u_lengthToThicknessRatio,y=u.u_widenessToThicknessRatio;s.uniformMatrix3fv(h,!1,a.matrix),s.uniform1f(d,a.sizeRatio),s.uniform1f(l,a.correctionRatio),s.uniform1f(c,a.minEdgeThickness),s.uniform1f(f,i.lengthToThicknessRatio),s.uniform1f(y,i.widenessToThicknessRatio)}}])}(rt)}li();var on=`
precision mediump float;

varying vec4 v_color;
varying vec2 v_normal;
varying float v_thickness;
varying float v_feather;

const vec4 transparent = vec4(0.0, 0.0, 0.0, 0.0);

void main(void) {
  // We only handle antialiasing for normal mode:
  #ifdef PICKING_MODE
  gl_FragColor = v_color;
  #else
  float dist = length(v_normal) * v_thickness;

  float t = smoothstep(
    v_thickness - v_feather,
    v_thickness,
    dist
  );

  gl_FragColor = mix(v_color, transparent, t);
  #endif
}
`,ci=on,sn=`
attribute vec4 a_id;
attribute vec4 a_color;
attribute vec2 a_normal;
attribute float a_normalCoef;
attribute vec2 a_positionStart;
attribute vec2 a_positionEnd;
attribute float a_positionCoef;
attribute float a_radius;
attribute float a_radiusCoef;

uniform mat3 u_matrix;
uniform float u_zoomRatio;
uniform float u_sizeRatio;
uniform float u_pixelRatio;
uniform float u_correctionRatio;
uniform float u_minEdgeThickness;
uniform float u_lengthToThicknessRatio;
uniform float u_feather;

varying vec4 v_color;
varying vec2 v_normal;
varying float v_thickness;
varying float v_feather;

const float bias = 255.0 / 254.0;

void main() {
  float minThickness = u_minEdgeThickness;

  float radius = a_radius * a_radiusCoef;
  vec2 normal = a_normal * a_normalCoef;
  vec2 position = a_positionStart * (1.0 - a_positionCoef) + a_positionEnd * a_positionCoef;

  float normalLength = length(normal);
  vec2 unitNormal = normal / normalLength;

  // These first computations are taken from edge.vert.glsl. Please read it to
  // get better comments on what's happening:
  float pixelsThickness = max(normalLength, minThickness * u_sizeRatio);
  float webGLThickness = pixelsThickness * u_correctionRatio / u_sizeRatio;

  // Here, we move the point to leave space for the arrow head:
  float direction = sign(radius);
  float webGLNodeRadius = direction * radius * 2.0 * u_correctionRatio / u_sizeRatio;
  float webGLArrowHeadLength = webGLThickness * u_lengthToThicknessRatio * 2.0;

  vec2 compensationVector = vec2(-direction * unitNormal.y, direction * unitNormal.x) * (webGLNodeRadius + webGLArrowHeadLength);

  // Here is the proper position of the vertex
  gl_Position = vec4((u_matrix * vec3(position + unitNormal * webGLThickness + compensationVector, 1)).xy, 0, 1);

  v_thickness = webGLThickness / u_zoomRatio;

  v_normal = unitNormal;

  v_feather = u_feather * u_correctionRatio / u_zoomRatio / u_pixelRatio * 2.0;

  #ifdef PICKING_MODE
  // For picking mode, we use the ID as the color:
  v_color = a_id;
  #else
  // For normal mode, we use the color:
  v_color = a_color;
  #endif

  v_color.a *= bias;
}
`,un=sn,fi=WebGLRenderingContext,wt=fi.UNSIGNED_BYTE,oe=fi.FLOAT,hn=["u_matrix","u_zoomRatio","u_sizeRatio","u_correctionRatio","u_pixelRatio","u_feather","u_minEdgeThickness","u_lengthToThicknessRatio"],dn={lengthToThicknessRatio:di.lengthToThicknessRatio};function gi(n){var i=S(S({},dn),{});return function(t){function e(){return U(this,e),H(this,e,arguments)}return W(e,t),$(e,[{key:"getDefinition",value:function(){return{VERTICES:6,VERTEX_SHADER_SOURCE:un,FRAGMENT_SHADER_SOURCE:ci,METHOD:WebGLRenderingContext.TRIANGLES,UNIFORMS:hn,ATTRIBUTES:[{name:"a_positionStart",size:2,type:oe},{name:"a_positionEnd",size:2,type:oe},{name:"a_normal",size:2,type:oe},{name:"a_color",size:4,type:wt,normalized:!0},{name:"a_id",size:4,type:wt,normalized:!0},{name:"a_radius",size:1,type:oe}],CONSTANT_ATTRIBUTES:[{name:"a_positionCoef",size:1,type:oe},{name:"a_normalCoef",size:1,type:oe},{name:"a_radiusCoef",size:1,type:oe}],CONSTANT_DATA:[[0,1,0],[0,-1,0],[1,1,1],[1,1,1],[0,-1,0],[1,-1,-1]]}}},{key:"processVisibleItem",value:function(a,o,s,u,h){var d=h.size||1,l=s.x,c=s.y,f=u.x,y=u.y,b=pe(h.color),E=f-l,k=y-c,x=u.size||1,T=E*E+k*k,g=0,p=0;T&&(T=1/Math.sqrt(T),g=-k*T*d,p=E*T*d);var v=this.array;v[o++]=l,v[o++]=c,v[o++]=f,v[o++]=y,v[o++]=g,v[o++]=p,v[o++]=b,v[o++]=a,v[o++]=x}},{key:"setUniforms",value:function(a,o){var s=o.gl,u=o.uniformLocations,h=u.u_matrix,d=u.u_zoomRatio,l=u.u_feather,c=u.u_pixelRatio,f=u.u_correctionRatio,y=u.u_sizeRatio,b=u.u_minEdgeThickness,E=u.u_lengthToThicknessRatio;s.uniformMatrix3fv(h,!1,a.matrix),s.uniform1f(d,a.zoomRatio),s.uniform1f(y,a.sizeRatio),s.uniform1f(f,a.correctionRatio),s.uniform1f(c,a.pixelRatio),s.uniform1f(l,a.antiAliasingFeather),s.uniform1f(b,a.minEdgeThickness),s.uniform1f(E,i.lengthToThicknessRatio)}}])}(rt)}gi();function ln(n){return Vr([gi(),li(n)])}var cn=ln(),fn=cn,gn=`
attribute vec4 a_id;
attribute vec4 a_color;
attribute vec2 a_normal;
attribute float a_normalCoef;
attribute vec2 a_positionStart;
attribute vec2 a_positionEnd;
attribute float a_positionCoef;

uniform mat3 u_matrix;
uniform float u_sizeRatio;
uniform float u_zoomRatio;
uniform float u_pixelRatio;
uniform float u_correctionRatio;
uniform float u_minEdgeThickness;
uniform float u_feather;

varying vec4 v_color;
varying vec2 v_normal;
varying float v_thickness;
varying float v_feather;

const float bias = 255.0 / 254.0;

void main() {
  float minThickness = u_minEdgeThickness;

  vec2 normal = a_normal * a_normalCoef;
  vec2 position = a_positionStart * (1.0 - a_positionCoef) + a_positionEnd * a_positionCoef;

  float normalLength = length(normal);
  vec2 unitNormal = normal / normalLength;

  // We require edges to be at least "minThickness" pixels thick *on screen*
  // (so we need to compensate the size ratio):
  float pixelsThickness = max(normalLength, minThickness * u_sizeRatio);

  // Then, we need to retrieve the normalized thickness of the edge in the WebGL
  // referential (in a ([0, 1], [0, 1]) space), using our "magic" correction
  // ratio:
  float webGLThickness = pixelsThickness * u_correctionRatio / u_sizeRatio;

  // Here is the proper position of the vertex
  gl_Position = vec4((u_matrix * vec3(position + unitNormal * webGLThickness, 1)).xy, 0, 1);

  // For the fragment shader though, we need a thickness that takes the "magic"
  // correction ratio into account (as in webGLThickness), but so that the
  // antialiasing effect does not depend on the zoom level. So here's yet
  // another thickness version:
  v_thickness = webGLThickness / u_zoomRatio;

  v_normal = unitNormal;

  v_feather = u_feather * u_correctionRatio / u_zoomRatio / u_pixelRatio * 2.0;

  #ifdef PICKING_MODE
  // For picking mode, we use the ID as the color:
  v_color = a_id;
  #else
  // For normal mode, we use the color:
  v_color = a_color;
  #endif

  v_color.a *= bias;
}
`,pn=gn,pi=WebGLRenderingContext,Et=pi.UNSIGNED_BYTE,ye=pi.FLOAT,mn=["u_matrix","u_zoomRatio","u_sizeRatio","u_correctionRatio","u_pixelRatio","u_feather","u_minEdgeThickness"],vn=function(n){function i(){return U(this,i),H(this,i,arguments)}return W(i,n),$(i,[{key:"getDefinition",value:function(){return{VERTICES:6,VERTEX_SHADER_SOURCE:pn,FRAGMENT_SHADER_SOURCE:ci,METHOD:WebGLRenderingContext.TRIANGLES,UNIFORMS:mn,ATTRIBUTES:[{name:"a_positionStart",size:2,type:ye},{name:"a_positionEnd",size:2,type:ye},{name:"a_normal",size:2,type:ye},{name:"a_color",size:4,type:Et,normalized:!0},{name:"a_id",size:4,type:Et,normalized:!0}],CONSTANT_ATTRIBUTES:[{name:"a_positionCoef",size:1,type:ye},{name:"a_normalCoef",size:1,type:ye}],CONSTANT_DATA:[[0,1],[0,-1],[1,1],[1,1],[0,-1],[1,-1]]}}},{key:"processVisibleItem",value:function(e,r,a,o,s){var u=s.size||1,h=a.x,d=a.y,l=o.x,c=o.y,f=pe(s.color),y=l-h,b=c-d,E=y*y+b*b,k=0,x=0;E&&(E=1/Math.sqrt(E),k=-b*E*u,x=y*E*u);var T=this.array;T[r++]=h,T[r++]=d,T[r++]=l,T[r++]=c,T[r++]=k,T[r++]=x,T[r++]=f,T[r++]=e}},{key:"setUniforms",value:function(e,r){var a=r.gl,o=r.uniformLocations,s=o.u_matrix,u=o.u_zoomRatio,h=o.u_feather,d=o.u_pixelRatio,l=o.u_correctionRatio,c=o.u_sizeRatio,f=o.u_minEdgeThickness;a.uniformMatrix3fv(s,!1,e.matrix),a.uniform1f(u,e.zoomRatio),a.uniform1f(c,e.sizeRatio),a.uniform1f(l,e.correctionRatio),a.uniform1f(d,e.pixelRatio),a.uniform1f(h,e.antiAliasingFeather),a.uniform1f(f,e.minEdgeThickness)}}])}(rt),nt=function(n){function i(){var t;return U(this,i),t=H(this,i),t.rawEmitter=t,t}return W(i,n),$(i)}(Mt.EventEmitter),We,_t;function yn(){return _t||(_t=1,We=function(i){return i!==null&&typeof i=="object"&&typeof i.addUndirectedEdgeWithKey=="function"&&typeof i.dropNode=="function"&&typeof i.multi=="boolean"}),We}var bn=yn();const wn=xi(bn);var En=function(i){return i},_n=function(i){return i*i},xn=function(i){return i*(2-i)},Tn=function(i){return(i*=2)<1?.5*i*i:-.5*(--i*(i-2)-1)},Cn=function(i){return i*i*i},Sn=function(i){return--i*i*i+1},kn=function(i){return(i*=2)<1?.5*i*i*i:.5*((i-=2)*i*i+2)},mi={linear:En,quadraticIn:_n,quadraticOut:xn,quadraticInOut:Tn,cubicIn:Cn,cubicOut:Sn,cubicInOut:kn},vi={easing:"quadraticInOut",duration:150};function aa(n,i,t,e){var r=Object.assign({},vi,t),a=typeof r.easing=="function"?r.easing:mi[r.easing],o=Date.now(),s={};for(var u in i){var h=i[u];s[u]={};for(var d in h)s[u][d]=n.getNodeAttribute(u,d)}var l=null,c=function(){l=null;var y=(Date.now()-o)/r.duration;if(y>=1){for(var b in i){var E=i[b];for(var k in E)n.setNodeAttribute(b,k,E[k])}return}y=a(y);for(var x in i){var T=i[x],g=s[x];for(var p in T)n.setNodeAttribute(x,p,T[p]*y+g[p]*(1-y))}l=requestAnimationFrame(c)};return c(),function(){l&&cancelAnimationFrame(l)}}function K(){return Float32Array.of(1,0,0,0,1,0,0,0,1)}function Ae(n,i,t){return n[0]=i,n[4]=typeof t=="number"?t:i,n}function xt(n,i){var t=Math.sin(i),e=Math.cos(i);return n[0]=e,n[1]=t,n[3]=-t,n[4]=e,n}function Tt(n,i,t){return n[6]=i,n[7]=t,n}function ne(n,i){var t=n[0],e=n[1],r=n[2],a=n[3],o=n[4],s=n[5],u=n[6],h=n[7],d=n[8],l=i[0],c=i[1],f=i[2],y=i[3],b=i[4],E=i[5],k=i[6],x=i[7],T=i[8];return n[0]=l*t+c*a+f*u,n[1]=l*e+c*o+f*h,n[2]=l*r+c*s+f*d,n[3]=y*t+b*a+E*u,n[4]=y*e+b*o+E*h,n[5]=y*r+b*s+E*d,n[6]=k*t+x*a+T*u,n[7]=k*e+x*o+T*h,n[8]=k*r+x*s+T*d,n}function Xe(n,i){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,e=n[0],r=n[1],a=n[3],o=n[4],s=n[6],u=n[7],h=i.x,d=i.y;return{x:h*e+d*a+s*t,y:h*r+d*o+u*t}}function An(n,i){var t=n.height/n.width,e=i.height/i.width;return t<1&&e>1||t>1&&e<1?1:Math.min(Math.max(e,1/e),Math.max(1/t,t))}function be(n,i,t,e,r){var a=n.angle,o=n.ratio,s=n.x,u=n.y,h=i.width,d=i.height,l=K(),c=Math.min(h,d)-2*e,f=An(i,t);return r?(ne(l,Tt(K(),s,u)),ne(l,Ae(K(),o)),ne(l,xt(K(),a)),ne(l,Ae(K(),h/c/2/f,d/c/2/f))):(ne(l,Ae(K(),2*(c/h)*f,2*(c/d)*f)),ne(l,xt(K(),-a)),ne(l,Ae(K(),1/o)),ne(l,Tt(K(),-s,-u))),l}function Rn(n,i,t){var e=Xe(n,{x:Math.cos(i.angle),y:Math.sin(i.angle)},0),r=e.x,a=e.y;return 1/Math.sqrt(Math.pow(r,2)+Math.pow(a,2))/t.width}function Ln(n){if(!n.order)return{x:[0,1],y:[0,1]};var i=1/0,t=-1/0,e=1/0,r=-1/0;return n.forEachNode(function(a,o){var s=o.x,u=o.y;s<i&&(i=s),s>t&&(t=s),u<e&&(e=u),u>r&&(r=u)}),{x:[i,t],y:[e,r]}}function Dn(n){if(!wn(n))throw new Error("Sigma: invalid graph instance.");n.forEachNode(function(i,t){if(!Number.isFinite(t.x)||!Number.isFinite(t.y))throw new Error("Sigma: Coordinates of node ".concat(i," are invalid. A node must have a numeric 'x' and 'y' attribute."))})}function Nn(n,i,t){var e=document.createElement(n);if(i)for(var r in i)e.style[r]=i[r];if(t)for(var a in t)e.setAttribute(a,t[a]);return e}function Ct(){return typeof window.devicePixelRatio<"u"?window.devicePixelRatio:1}function St(n,i,t){return t.sort(function(e,r){var a=i(e)||0,o=i(r)||0;return a<o?-1:a>o?1:0})}function kt(n){var i=le(n.x,2),t=i[0],e=i[1],r=le(n.y,2),a=r[0],o=r[1],s=Math.max(e-t,o-a),u=(e+t)/2,h=(o+a)/2;(s===0||Math.abs(s)===1/0||isNaN(s))&&(s=1),isNaN(u)&&(u=0),isNaN(h)&&(h=0);var d=function(c){return{x:.5+(c.x-u)/s,y:.5+(c.y-h)/s}};return d.applyTo=function(l){l.x=.5+(l.x-u)/s,l.y=.5+(l.y-h)/s},d.inverse=function(l){return{x:u+s*(l.x-.5),y:h+s*(l.y-.5)}},d.ratio=s,d}function Je(n){"@babel/helpers - typeof";return Je=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(i){return typeof i}:function(i){return i&&typeof Symbol=="function"&&i.constructor===Symbol&&i!==Symbol.prototype?"symbol":typeof i},Je(n)}function At(n,i){var t=i.size;if(t!==0){var e=n.length;n.length+=t;var r=0;i.forEach(function(a){n[e+r]=a,r++})}}function je(n){n=n||{};for(var i=0,t=arguments.length<=1?0:arguments.length-1;i<t;i++){var e=i+1<1||arguments.length<=i+1?void 0:arguments[i+1];e&&Object.assign(n,e)}return n}var at={hideEdgesOnMove:!1,hideLabelsOnMove:!1,renderLabels:!0,renderEdgeLabels:!1,enableEdgeEvents:!1,defaultNodeColor:"#999",defaultNodeType:"circle",defaultEdgeColor:"#ccc",defaultEdgeType:"line",labelFont:"Arial",labelSize:14,labelWeight:"normal",labelColor:{color:"#000"},edgeLabelFont:"Arial",edgeLabelSize:14,edgeLabelWeight:"normal",edgeLabelColor:{attribute:"color"},stagePadding:30,defaultDrawEdgeLabel:Kr,defaultDrawNodeLabel:si,defaultDrawNodeHover:Yr,minEdgeThickness:1.7,antiAliasingFeather:1,dragTimeout:100,draggedEventsTolerance:3,inertiaDuration:200,inertiaRatio:3,zoomDuration:250,zoomingRatio:1.7,doubleClickTimeout:300,doubleClickZoomingRatio:2.2,doubleClickZoomingDuration:200,tapMoveTolerance:10,zoomToSizeRatioFunction:Math.sqrt,itemSizesReference:"screen",autoRescale:!0,autoCenter:!0,labelDensity:1,labelGridCellSize:100,labelRenderedSizeThreshold:6,nodeReducer:null,edgeReducer:null,zIndex:!1,minCameraRatio:null,maxCameraRatio:null,enableCameraZooming:!0,enableCameraPanning:!0,enableCameraRotation:!0,cameraPanBoundaries:null,allowInvalidContainer:!1,nodeProgramClasses:{},nodeHoverProgramClasses:{},edgeProgramClasses:{}},Gn={circle:Ge},Fn={arrow:fn,line:vn};function Ve(n){if(typeof n.labelDensity!="number"||n.labelDensity<0)throw new Error("Settings: invalid `labelDensity`. Expecting a positive number.");var i=n.minCameraRatio,t=n.maxCameraRatio;if(typeof i=="number"&&typeof t=="number"&&t<i)throw new Error("Settings: invalid camera ratio boundaries. Expecting `maxCameraRatio` to be greater than `minCameraRatio`.")}function Pn(n){var i=je({},at,n);return i.nodeProgramClasses=je({},Gn,i.nodeProgramClasses),i.edgeProgramClasses=je({},Fn,i.edgeProgramClasses),i}var Re=1.5,Rt=function(n){function i(){var t;return U(this,i),t=H(this,i),m(t,"x",.5),m(t,"y",.5),m(t,"angle",0),m(t,"ratio",1),m(t,"minRatio",null),m(t,"maxRatio",null),m(t,"enabledZooming",!0),m(t,"enabledPanning",!0),m(t,"enabledRotation",!0),m(t,"clean",null),m(t,"nextFrame",null),m(t,"previousState",null),m(t,"enabled",!0),t.previousState=t.getState(),t}return W(i,n),$(i,[{key:"enable",value:function(){return this.enabled=!0,this}},{key:"disable",value:function(){return this.enabled=!1,this}},{key:"getState",value:function(){return{x:this.x,y:this.y,angle:this.angle,ratio:this.ratio}}},{key:"hasState",value:function(e){return this.x===e.x&&this.y===e.y&&this.ratio===e.ratio&&this.angle===e.angle}},{key:"getPreviousState",value:function(){var e=this.previousState;return e?{x:e.x,y:e.y,angle:e.angle,ratio:e.ratio}:null}},{key:"getBoundedRatio",value:function(e){var r=e;return typeof this.minRatio=="number"&&(r=Math.max(r,this.minRatio)),typeof this.maxRatio=="number"&&(r=Math.min(r,this.maxRatio)),r}},{key:"validateState",value:function(e){var r={};return this.enabledPanning&&typeof e.x=="number"&&(r.x=e.x),this.enabledPanning&&typeof e.y=="number"&&(r.y=e.y),this.enabledZooming&&typeof e.ratio=="number"&&(r.ratio=this.getBoundedRatio(e.ratio)),this.enabledRotation&&typeof e.angle=="number"&&(r.angle=e.angle),this.clean?this.clean(S(S({},this.getState()),r)):r}},{key:"isAnimated",value:function(){return!!this.nextFrame}},{key:"setState",value:function(e){if(!this.enabled)return this;this.previousState=this.getState();var r=this.validateState(e);return typeof r.x=="number"&&(this.x=r.x),typeof r.y=="number"&&(this.y=r.y),typeof r.ratio=="number"&&(this.ratio=r.ratio),typeof r.angle=="number"&&(this.angle=r.angle),this.hasState(this.previousState)||this.emit("updated",this.getState()),this}},{key:"updateState",value:function(e){return this.setState(e(this.getState())),this}},{key:"animate",value:function(e){var r=this,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=arguments.length>2?arguments[2]:void 0;if(!o)return new Promise(function(f){return r.animate(e,a,f)});if(this.enabled){var s=S(S({},vi),a),u=this.validateState(e),h=typeof s.easing=="function"?s.easing:mi[s.easing],d=Date.now(),l=this.getState(),c=function(){var y=(Date.now()-d)/s.duration;if(y>=1){r.nextFrame=null,r.setState(u),r.animationCallback&&(r.animationCallback.call(null),r.animationCallback=void 0);return}var b=h(y),E={};typeof u.x=="number"&&(E.x=l.x+(u.x-l.x)*b),typeof u.y=="number"&&(E.y=l.y+(u.y-l.y)*b),r.enabledRotation&&typeof u.angle=="number"&&(E.angle=l.angle+(u.angle-l.angle)*b),typeof u.ratio=="number"&&(E.ratio=l.ratio+(u.ratio-l.ratio)*b),r.setState(E),r.nextFrame=requestAnimationFrame(c)};this.nextFrame?(cancelAnimationFrame(this.nextFrame),this.animationCallback&&this.animationCallback.call(null),this.nextFrame=requestAnimationFrame(c)):c(),this.animationCallback=o}}},{key:"animatedZoom",value:function(e){return e?typeof e=="number"?this.animate({ratio:this.ratio/e}):this.animate({ratio:this.ratio/(e.factor||Re)},e):this.animate({ratio:this.ratio/Re})}},{key:"animatedUnzoom",value:function(e){return e?typeof e=="number"?this.animate({ratio:this.ratio*e}):this.animate({ratio:this.ratio*(e.factor||Re)},e):this.animate({ratio:this.ratio*Re})}},{key:"animatedReset",value:function(e){return this.animate({x:.5,y:.5,ratio:1,angle:0},e)}},{key:"copy",value:function(){return i.from(this.getState())}}],[{key:"from",value:function(e){var r=new i;return r.setState(e)}}])}(nt);function Y(n,i){var t=i.getBoundingClientRect();return{x:n.clientX-t.left,y:n.clientY-t.top}}function X(n,i){var t=S(S({},Y(n,i)),{},{sigmaDefaultPrevented:!1,preventSigmaDefault:function(){t.sigmaDefaultPrevented=!0},original:n});return t}function we(n){var i="x"in n?n:S(S({},n.touches[0]||n.previousTouches[0]),{},{original:n.original,sigmaDefaultPrevented:n.sigmaDefaultPrevented,preventSigmaDefault:function(){n.sigmaDefaultPrevented=!0,i.sigmaDefaultPrevented=!0}});return i}function In(n,i){return S(S({},X(n,i)),{},{delta:yi(n)})}var Mn=2;function De(n){for(var i=[],t=0,e=Math.min(n.length,Mn);t<e;t++)i.push(n[t]);return i}function Ee(n,i,t){var e={touches:De(n.touches).map(function(r){return Y(r,t)}),previousTouches:i.map(function(r){return Y(r,t)}),sigmaDefaultPrevented:!1,preventSigmaDefault:function(){e.sigmaDefaultPrevented=!0},original:n};return e}function yi(n){if(typeof n.deltaY<"u")return n.deltaY*-3/360;if(typeof n.detail<"u")return n.detail/-9;throw new Error("Captor: could not extract delta from event.")}var bi=function(n){function i(t,e){var r;return U(this,i),r=H(this,i),r.container=t,r.renderer=e,r}return W(i,n),$(i)}(nt),On=["doubleClickTimeout","doubleClickZoomingDuration","doubleClickZoomingRatio","dragTimeout","draggedEventsTolerance","inertiaDuration","inertiaRatio","zoomDuration","zoomingRatio"],Un=On.reduce(function(n,i){return S(S({},n),{},m({},i,at[i]))},{}),$n=function(n){function i(t,e){var r;return U(this,i),r=H(this,i,[t,e]),m(r,"enabled",!0),m(r,"draggedEvents",0),m(r,"downStartTime",null),m(r,"lastMouseX",null),m(r,"lastMouseY",null),m(r,"isMouseDown",!1),m(r,"isMoving",!1),m(r,"movingTimeout",null),m(r,"startCameraState",null),m(r,"clicks",0),m(r,"doubleClickTimeout",null),m(r,"currentWheelDirection",0),m(r,"settings",Un),r.handleClick=r.handleClick.bind(r),r.handleRightClick=r.handleRightClick.bind(r),r.handleDown=r.handleDown.bind(r),r.handleUp=r.handleUp.bind(r),r.handleMove=r.handleMove.bind(r),r.handleWheel=r.handleWheel.bind(r),r.handleLeave=r.handleLeave.bind(r),r.handleEnter=r.handleEnter.bind(r),t.addEventListener("click",r.handleClick,{capture:!1}),t.addEventListener("contextmenu",r.handleRightClick,{capture:!1}),t.addEventListener("mousedown",r.handleDown,{capture:!1}),t.addEventListener("wheel",r.handleWheel,{capture:!1}),t.addEventListener("mouseleave",r.handleLeave,{capture:!1}),t.addEventListener("mouseenter",r.handleEnter,{capture:!1}),document.addEventListener("mousemove",r.handleMove,{capture:!1}),document.addEventListener("mouseup",r.handleUp,{capture:!1}),r}return W(i,n),$(i,[{key:"kill",value:function(){var e=this.container;e.removeEventListener("click",this.handleClick),e.removeEventListener("contextmenu",this.handleRightClick),e.removeEventListener("mousedown",this.handleDown),e.removeEventListener("wheel",this.handleWheel),e.removeEventListener("mouseleave",this.handleLeave),e.removeEventListener("mouseenter",this.handleEnter),document.removeEventListener("mousemove",this.handleMove),document.removeEventListener("mouseup",this.handleUp)}},{key:"handleClick",value:function(e){var r=this;if(this.enabled){if(this.clicks++,this.clicks===2)return this.clicks=0,typeof this.doubleClickTimeout=="number"&&(clearTimeout(this.doubleClickTimeout),this.doubleClickTimeout=null),this.handleDoubleClick(e);setTimeout(function(){r.clicks=0,r.doubleClickTimeout=null},this.settings.doubleClickTimeout),this.draggedEvents<this.settings.draggedEventsTolerance&&this.emit("click",X(e,this.container))}}},{key:"handleRightClick",value:function(e){this.enabled&&this.emit("rightClick",X(e,this.container))}},{key:"handleDoubleClick",value:function(e){if(this.enabled){e.preventDefault(),e.stopPropagation();var r=X(e,this.container);if(this.emit("doubleClick",r),!r.sigmaDefaultPrevented){var a=this.renderer.getCamera(),o=a.getBoundedRatio(a.getState().ratio/this.settings.doubleClickZoomingRatio);a.animate(this.renderer.getViewportZoomedState(Y(e,this.container),o),{easing:"quadraticInOut",duration:this.settings.doubleClickZoomingDuration})}}}},{key:"handleDown",value:function(e){if(this.enabled){if(e.button===0){this.startCameraState=this.renderer.getCamera().getState();var r=Y(e,this.container),a=r.x,o=r.y;this.lastMouseX=a,this.lastMouseY=o,this.draggedEvents=0,this.downStartTime=Date.now(),this.isMouseDown=!0}this.emit("mousedown",X(e,this.container))}}},{key:"handleUp",value:function(e){var r=this;if(!(!this.enabled||!this.isMouseDown)){var a=this.renderer.getCamera();this.isMouseDown=!1,typeof this.movingTimeout=="number"&&(clearTimeout(this.movingTimeout),this.movingTimeout=null);var o=Y(e,this.container),s=o.x,u=o.y,h=a.getState(),d=a.getPreviousState()||{x:0,y:0};this.isMoving?a.animate({x:h.x+this.settings.inertiaRatio*(h.x-d.x),y:h.y+this.settings.inertiaRatio*(h.y-d.y)},{duration:this.settings.inertiaDuration,easing:"quadraticOut"}):(this.lastMouseX!==s||this.lastMouseY!==u)&&a.setState({x:h.x,y:h.y}),this.isMoving=!1,setTimeout(function(){var l=r.draggedEvents>0;r.draggedEvents=0,l&&r.renderer.getSetting("hideEdgesOnMove")&&r.renderer.refresh()},0),this.emit("mouseup",X(e,this.container))}}},{key:"handleMove",value:function(e){var r=this;if(this.enabled){var a=X(e,this.container);if(this.emit("mousemovebody",a),(e.target===this.container||e.composedPath()[0]===this.container)&&this.emit("mousemove",a),!a.sigmaDefaultPrevented&&this.isMouseDown){this.isMoving=!0,this.draggedEvents++,typeof this.movingTimeout=="number"&&clearTimeout(this.movingTimeout),this.movingTimeout=window.setTimeout(function(){r.movingTimeout=null,r.isMoving=!1},this.settings.dragTimeout);var o=this.renderer.getCamera(),s=Y(e,this.container),u=s.x,h=s.y,d=this.renderer.viewportToFramedGraph({x:this.lastMouseX,y:this.lastMouseY}),l=this.renderer.viewportToFramedGraph({x:u,y:h}),c=d.x-l.x,f=d.y-l.y,y=o.getState(),b=y.x+c,E=y.y+f;o.setState({x:b,y:E}),this.lastMouseX=u,this.lastMouseY=h,e.preventDefault(),e.stopPropagation()}}}},{key:"handleLeave",value:function(e){this.emit("mouseleave",X(e,this.container))}},{key:"handleEnter",value:function(e){this.emit("mouseenter",X(e,this.container))}},{key:"handleWheel",value:function(e){var r=this,a=this.renderer.getCamera();if(!(!this.enabled||!a.enabledZooming)){var o=yi(e);if(o){var s=In(e,this.container);if(this.emit("wheel",s),s.sigmaDefaultPrevented){e.preventDefault(),e.stopPropagation();return}var u=a.getState().ratio,h=o>0?1/this.settings.zoomingRatio:this.settings.zoomingRatio,d=a.getBoundedRatio(u*h),l=o>0?1:-1,c=Date.now();u!==d&&(e.preventDefault(),e.stopPropagation(),!(this.currentWheelDirection===l&&this.lastWheelTriggerTime&&c-this.lastWheelTriggerTime<this.settings.zoomDuration/5)&&(a.animate(this.renderer.getViewportZoomedState(Y(e,this.container),d),{easing:"quadraticOut",duration:this.settings.zoomDuration},function(){r.currentWheelDirection=0}),this.currentWheelDirection=l,this.lastWheelTriggerTime=c))}}}},{key:"setSettings",value:function(e){this.settings=e}}])}(bi),zn=["dragTimeout","inertiaDuration","inertiaRatio","doubleClickTimeout","doubleClickZoomingRatio","doubleClickZoomingDuration","tapMoveTolerance"],Bn=zn.reduce(function(n,i){return S(S({},n),{},m({},i,at[i]))},{}),Hn=function(n){function i(t,e){var r;return U(this,i),r=H(this,i,[t,e]),m(r,"enabled",!0),m(r,"isMoving",!1),m(r,"hasMoved",!1),m(r,"touchMode",0),m(r,"startTouchesPositions",[]),m(r,"lastTouches",[]),m(r,"lastTap",null),m(r,"settings",Bn),r.handleStart=r.handleStart.bind(r),r.handleLeave=r.handleLeave.bind(r),r.handleMove=r.handleMove.bind(r),t.addEventListener("touchstart",r.handleStart,{capture:!1}),t.addEventListener("touchcancel",r.handleLeave,{capture:!1}),document.addEventListener("touchend",r.handleLeave,{capture:!1,passive:!1}),document.addEventListener("touchmove",r.handleMove,{capture:!1,passive:!1}),r}return W(i,n),$(i,[{key:"kill",value:function(){var e=this.container;e.removeEventListener("touchstart",this.handleStart),e.removeEventListener("touchcancel",this.handleLeave),document.removeEventListener("touchend",this.handleLeave),document.removeEventListener("touchmove",this.handleMove)}},{key:"getDimensions",value:function(){return{width:this.container.offsetWidth,height:this.container.offsetHeight}}},{key:"handleStart",value:function(e){var r=this;if(this.enabled){e.preventDefault();var a=De(e.touches);if(this.touchMode=a.length,this.startCameraState=this.renderer.getCamera().getState(),this.startTouchesPositions=a.map(function(f){return Y(f,r.container)}),this.touchMode===2){var o=le(this.startTouchesPositions,2),s=o[0],u=s.x,h=s.y,d=o[1],l=d.x,c=d.y;this.startTouchesAngle=Math.atan2(c-h,l-u),this.startTouchesDistance=Math.sqrt(Math.pow(l-u,2)+Math.pow(c-h,2))}this.emit("touchdown",Ee(e,this.lastTouches,this.container)),this.lastTouches=a,this.lastTouchesPositions=this.startTouchesPositions}}},{key:"handleLeave",value:function(e){if(!(!this.enabled||!this.startTouchesPositions.length)){switch(e.cancelable&&e.preventDefault(),this.movingTimeout&&(this.isMoving=!1,clearTimeout(this.movingTimeout)),this.touchMode){case 2:if(e.touches.length===1){this.handleStart(e),e.preventDefault();break}case 1:if(this.isMoving){var r=this.renderer.getCamera(),a=r.getState(),o=r.getPreviousState()||{x:0,y:0};r.animate({x:a.x+this.settings.inertiaRatio*(a.x-o.x),y:a.y+this.settings.inertiaRatio*(a.y-o.y)},{duration:this.settings.inertiaDuration,easing:"quadraticOut"})}this.hasMoved=!1,this.isMoving=!1,this.touchMode=0;break}if(this.emit("touchup",Ee(e,this.lastTouches,this.container)),!e.touches.length){var s=Y(this.lastTouches[0],this.container),u=this.startTouchesPositions[0],h=Math.pow(s.x-u.x,2)+Math.pow(s.y-u.y,2);if(!e.touches.length&&h<Math.pow(this.settings.tapMoveTolerance,2))if(this.lastTap&&Date.now()-this.lastTap.time<this.settings.doubleClickTimeout){var d=Ee(e,this.lastTouches,this.container);if(this.emit("doubletap",d),this.lastTap=null,!d.sigmaDefaultPrevented){var l=this.renderer.getCamera(),c=l.getBoundedRatio(l.getState().ratio/this.settings.doubleClickZoomingRatio);l.animate(this.renderer.getViewportZoomedState(s,c),{easing:"quadraticInOut",duration:this.settings.doubleClickZoomingDuration})}}else{var f=Ee(e,this.lastTouches,this.container);this.emit("tap",f),this.lastTap={time:Date.now(),position:f.touches[0]||f.previousTouches[0]}}}this.lastTouches=De(e.touches),this.startTouchesPositions=[]}}},{key:"handleMove",value:function(e){var r=this;if(!(!this.enabled||!this.startTouchesPositions.length)){e.preventDefault();var a=De(e.touches),o=a.map(function(re){return Y(re,r.container)}),s=this.lastTouches;this.lastTouches=a,this.lastTouchesPositions=o;var u=Ee(e,s,this.container);if(this.emit("touchmove",u),!u.sigmaDefaultPrevented&&(this.hasMoved||(this.hasMoved=o.some(function(re,Pe){var Z=r.startTouchesPositions[Pe];return Z&&(re.x!==Z.x||re.y!==Z.y)})),!!this.hasMoved)){this.isMoving=!0,this.movingTimeout&&clearTimeout(this.movingTimeout),this.movingTimeout=window.setTimeout(function(){r.isMoving=!1},this.settings.dragTimeout);var h=this.renderer.getCamera(),d=this.startCameraState,l=this.renderer.getSetting("stagePadding");switch(this.touchMode){case 1:{var c=this.renderer.viewportToFramedGraph((this.startTouchesPositions||[])[0]),f=c.x,y=c.y,b=this.renderer.viewportToFramedGraph(o[0]),E=b.x,k=b.y;h.setState({x:d.x+f-E,y:d.y+y-k});break}case 2:{var x={x:.5,y:.5,angle:0,ratio:1},T=o[0],g=T.x,p=T.y,v=o[1],_=v.x,A=v.y,D=Math.atan2(A-p,_-g)-this.startTouchesAngle,L=Math.hypot(A-p,_-g)/this.startTouchesDistance,F=h.getBoundedRatio(d.ratio/L);x.ratio=F,x.angle=d.angle+D;var P=this.getDimensions(),q=this.renderer.viewportToFramedGraph((this.startTouchesPositions||[])[0],{cameraState:d}),O=Math.min(P.width,P.height)-2*l,j=O/P.width,te=O/P.height,xe=F/O,se=g-O/2/j,ae=p-O/2/te,ie=[se*Math.cos(-x.angle)-ae*Math.sin(-x.angle),ae*Math.cos(-x.angle)+se*Math.sin(-x.angle)];se=ie[0],ae=ie[1],x.x=q.x-se*xe,x.y=q.y+ae*xe,h.setState(x);break}}}}}},{key:"setSettings",value:function(e){this.settings=e}}])}(bi);function Wn(n){if(Array.isArray(n))return qe(n)}function jn(n){if(typeof Symbol<"u"&&n[Symbol.iterator]!=null||n["@@iterator"]!=null)return Array.from(n)}function Vn(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Lt(n){return Wn(n)||jn(n)||Qt(n)||Vn()}function Kn(n,i){if(n==null)return{};var t={};for(var e in n)if({}.hasOwnProperty.call(n,e)){if(i.includes(e))continue;t[e]=n[e]}return t}function Ke(n,i){if(n==null)return{};var t,e,r=Kn(n,i);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(n);for(e=0;e<a.length;e++)t=a[e],i.includes(t)||{}.propertyIsEnumerable.call(n,t)&&(r[t]=n[t])}return r}var Dt=function(){function n(i,t){U(this,n),this.key=i,this.size=t}return $(n,null,[{key:"compare",value:function(t,e){return t.size>e.size?-1:t.size<e.size||t.key>e.key?1:-1}}])}(),Nt=function(){function n(){U(this,n),m(this,"width",0),m(this,"height",0),m(this,"cellSize",0),m(this,"columns",0),m(this,"rows",0),m(this,"cells",{})}return $(n,[{key:"resizeAndClear",value:function(t,e){this.width=t.width,this.height=t.height,this.cellSize=e,this.columns=Math.ceil(t.width/e),this.rows=Math.ceil(t.height/e),this.cells={}}},{key:"getIndex",value:function(t){var e=Math.floor(t.x/this.cellSize),r=Math.floor(t.y/this.cellSize);return r*this.columns+e}},{key:"add",value:function(t,e,r){var a=new Dt(t,e),o=this.getIndex(r),s=this.cells[o];s||(s=[],this.cells[o]=s),s.push(a)}},{key:"organize",value:function(){for(var t in this.cells){var e=this.cells[t];e.sort(Dt.compare)}}},{key:"getLabelsToDisplay",value:function(t,e){var r=this.cellSize*this.cellSize,a=r/t/t,o=a*e/r,s=Math.ceil(o),u=[];for(var h in this.cells)for(var d=this.cells[h],l=0;l<Math.min(s,d.length);l++)u.push(d[l].key);return u}}])}();function Yn(n){var i=n.graph,t=n.hoveredNode,e=n.highlightedNodes,r=n.displayedNodeLabels,a=[];return i.forEachEdge(function(o,s,u,h){(u===t||h===t||e.has(u)||e.has(h)||r.has(u)&&r.has(h))&&a.push(o)}),a}var qn=150,Zn=50,J=Object.prototype.hasOwnProperty;function Xn(n,i,t){if(!J.call(t,"x")||!J.call(t,"y"))throw new Error('Sigma: could not find a valid position (x, y) for node "'.concat(i,'". All your nodes must have a number "x" and "y". Maybe your forgot to apply a layout or your "nodeReducer" is not returning the correct data?'));return t.color||(t.color=n.defaultNodeColor),!t.label&&t.label!==""&&(t.label=null),t.label!==void 0&&t.label!==null?t.label=""+t.label:t.label=null,t.size||(t.size=2),J.call(t,"hidden")||(t.hidden=!1),J.call(t,"highlighted")||(t.highlighted=!1),J.call(t,"forceLabel")||(t.forceLabel=!1),(!t.type||t.type==="")&&(t.type=n.defaultNodeType),t.zIndex||(t.zIndex=0),t}function Jn(n,i,t){return t.color||(t.color=n.defaultEdgeColor),t.label||(t.label=""),t.size||(t.size=.5),J.call(t,"hidden")||(t.hidden=!1),J.call(t,"forceLabel")||(t.forceLabel=!1),(!t.type||t.type==="")&&(t.type=n.defaultEdgeType),t.zIndex||(t.zIndex=0),t}var Qn=function(n){function i(t,e){var r,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(U(this,i),r=H(this,i),m(r,"elements",{}),m(r,"canvasContexts",{}),m(r,"webGLContexts",{}),m(r,"pickingLayers",new Set),m(r,"textures",{}),m(r,"frameBuffers",{}),m(r,"activeListeners",{}),m(r,"labelGrid",new Nt),m(r,"nodeDataCache",{}),m(r,"edgeDataCache",{}),m(r,"nodeProgramIndex",{}),m(r,"edgeProgramIndex",{}),m(r,"nodesWithForcedLabels",new Set),m(r,"edgesWithForcedLabels",new Set),m(r,"nodeExtent",{x:[0,1],y:[0,1]}),m(r,"nodeZExtent",[1/0,-1/0]),m(r,"edgeZExtent",[1/0,-1/0]),m(r,"matrix",K()),m(r,"invMatrix",K()),m(r,"correctionRatio",1),m(r,"customBBox",null),m(r,"normalizationFunction",kt({x:[0,1],y:[0,1]})),m(r,"graphToViewportRatio",1),m(r,"itemIDsIndex",{}),m(r,"nodeIndices",{}),m(r,"edgeIndices",{}),m(r,"width",0),m(r,"height",0),m(r,"pixelRatio",Ct()),m(r,"pickingDownSizingRatio",2*r.pixelRatio),m(r,"displayedNodeLabels",new Set),m(r,"displayedEdgeLabels",new Set),m(r,"highlightedNodes",new Set),m(r,"hoveredNode",null),m(r,"hoveredEdge",null),m(r,"renderFrame",null),m(r,"renderHighlightedNodesFrame",null),m(r,"needToProcess",!1),m(r,"checkEdgesEventsFrame",null),m(r,"nodePrograms",{}),m(r,"nodeHoverPrograms",{}),m(r,"edgePrograms",{}),r.settings=Pn(a),Ve(r.settings),Dn(t),!(e instanceof HTMLElement))throw new Error("Sigma: container should be an html element.");r.graph=t,r.container=e,r.createWebGLContext("edges",{picking:a.enableEdgeEvents}),r.createCanvasContext("edgeLabels"),r.createWebGLContext("nodes",{picking:!0}),r.createCanvasContext("labels"),r.createCanvasContext("hovers"),r.createWebGLContext("hoverNodes"),r.createCanvasContext("mouse",{style:{touchAction:"none",userSelect:"none"}}),r.resize();for(var o in r.settings.nodeProgramClasses)r.registerNodeProgram(o,r.settings.nodeProgramClasses[o],r.settings.nodeHoverProgramClasses[o]);for(var s in r.settings.edgeProgramClasses)r.registerEdgeProgram(s,r.settings.edgeProgramClasses[s]);return r.camera=new Rt,r.bindCameraHandlers(),r.mouseCaptor=new $n(r.elements.mouse,r),r.mouseCaptor.setSettings(r.settings),r.touchCaptor=new Hn(r.elements.mouse,r),r.touchCaptor.setSettings(r.settings),r.bindEventHandlers(),r.bindGraphHandlers(),r.handleSettingsUpdate(),r.refresh(),r}return W(i,n),$(i,[{key:"registerNodeProgram",value:function(e,r,a){return this.nodePrograms[e]&&this.nodePrograms[e].kill(),this.nodeHoverPrograms[e]&&this.nodeHoverPrograms[e].kill(),this.nodePrograms[e]=new r(this.webGLContexts.nodes,this.frameBuffers.nodes,this),this.nodeHoverPrograms[e]=new(a||r)(this.webGLContexts.hoverNodes,null,this),this}},{key:"registerEdgeProgram",value:function(e,r){return this.edgePrograms[e]&&this.edgePrograms[e].kill(),this.edgePrograms[e]=new r(this.webGLContexts.edges,this.frameBuffers.edges,this),this}},{key:"unregisterNodeProgram",value:function(e){if(this.nodePrograms[e]){var r=this.nodePrograms,a=r[e],o=Ke(r,[e].map(_e));a.kill(),this.nodePrograms=o}if(this.nodeHoverPrograms[e]){var s=this.nodeHoverPrograms,u=s[e],h=Ke(s,[e].map(_e));u.kill(),this.nodePrograms=h}return this}},{key:"unregisterEdgeProgram",value:function(e){if(this.edgePrograms[e]){var r=this.edgePrograms,a=r[e],o=Ke(r,[e].map(_e));a.kill(),this.edgePrograms=o}return this}},{key:"resetWebGLTexture",value:function(e){var r=this.webGLContexts[e],a=this.frameBuffers[e],o=this.textures[e];o&&r.deleteTexture(o);var s=r.createTexture();return r.bindFramebuffer(r.FRAMEBUFFER,a),r.bindTexture(r.TEXTURE_2D,s),r.texImage2D(r.TEXTURE_2D,0,r.RGBA,this.width,this.height,0,r.RGBA,r.UNSIGNED_BYTE,null),r.framebufferTexture2D(r.FRAMEBUFFER,r.COLOR_ATTACHMENT0,r.TEXTURE_2D,s,0),this.textures[e]=s,this}},{key:"bindCameraHandlers",value:function(){var e=this;return this.activeListeners.camera=function(){e.scheduleRender()},this.camera.on("updated",this.activeListeners.camera),this}},{key:"unbindCameraHandlers",value:function(){return this.camera.removeListener("updated",this.activeListeners.camera),this}},{key:"getNodeAtPosition",value:function(e){var r=e.x,a=e.y,o=gt(this.webGLContexts.nodes,this.frameBuffers.nodes,r,a,this.pixelRatio,this.pickingDownSizingRatio),s=ft.apply(void 0,Lt(o)),u=this.itemIDsIndex[s];return u&&u.type==="node"?u.id:null}},{key:"bindEventHandlers",value:function(){var e=this;this.activeListeners.handleResize=function(){e.scheduleRefresh()},window.addEventListener("resize",this.activeListeners.handleResize),this.activeListeners.handleMove=function(a){var o=we(a),s={event:o,preventSigmaDefault:function(){o.preventSigmaDefault()}},u=e.getNodeAtPosition(o);if(u&&e.hoveredNode!==u&&!e.nodeDataCache[u].hidden){e.hoveredNode&&e.emit("leaveNode",S(S({},s),{},{node:e.hoveredNode})),e.hoveredNode=u,e.emit("enterNode",S(S({},s),{},{node:u})),e.scheduleHighlightedNodesRender();return}if(e.hoveredNode&&e.getNodeAtPosition(o)!==e.hoveredNode){var h=e.hoveredNode;e.hoveredNode=null,e.emit("leaveNode",S(S({},s),{},{node:h})),e.scheduleHighlightedNodesRender();return}if(e.settings.enableEdgeEvents){var d=e.hoveredNode?null:e.getEdgeAtPoint(s.event.x,s.event.y);d!==e.hoveredEdge&&(e.hoveredEdge&&e.emit("leaveEdge",S(S({},s),{},{edge:e.hoveredEdge})),d&&e.emit("enterEdge",S(S({},s),{},{edge:d})),e.hoveredEdge=d)}},this.activeListeners.handleMoveBody=function(a){var o=we(a);e.emit("moveBody",{event:o,preventSigmaDefault:function(){o.preventSigmaDefault()}})},this.activeListeners.handleLeave=function(a){var o=we(a),s={event:o,preventSigmaDefault:function(){o.preventSigmaDefault()}};e.hoveredNode&&(e.emit("leaveNode",S(S({},s),{},{node:e.hoveredNode})),e.scheduleHighlightedNodesRender()),e.settings.enableEdgeEvents&&e.hoveredEdge&&(e.emit("leaveEdge",S(S({},s),{},{edge:e.hoveredEdge})),e.scheduleHighlightedNodesRender()),e.emit("leaveStage",S({},s))},this.activeListeners.handleEnter=function(a){var o=we(a),s={event:o,preventSigmaDefault:function(){o.preventSigmaDefault()}};e.emit("enterStage",S({},s))};var r=function(o){return function(s){var u=we(s),h={event:u,preventSigmaDefault:function(){u.preventSigmaDefault()}},d=e.getNodeAtPosition(u);if(d)return e.emit("".concat(o,"Node"),S(S({},h),{},{node:d}));if(e.settings.enableEdgeEvents){var l=e.getEdgeAtPoint(u.x,u.y);if(l)return e.emit("".concat(o,"Edge"),S(S({},h),{},{edge:l}))}return e.emit("".concat(o,"Stage"),h)}};return this.activeListeners.handleClick=r("click"),this.activeListeners.handleRightClick=r("rightClick"),this.activeListeners.handleDoubleClick=r("doubleClick"),this.activeListeners.handleWheel=r("wheel"),this.activeListeners.handleDown=r("down"),this.activeListeners.handleUp=r("up"),this.mouseCaptor.on("mousemove",this.activeListeners.handleMove),this.mouseCaptor.on("mousemovebody",this.activeListeners.handleMoveBody),this.mouseCaptor.on("click",this.activeListeners.handleClick),this.mouseCaptor.on("rightClick",this.activeListeners.handleRightClick),this.mouseCaptor.on("doubleClick",this.activeListeners.handleDoubleClick),this.mouseCaptor.on("wheel",this.activeListeners.handleWheel),this.mouseCaptor.on("mousedown",this.activeListeners.handleDown),this.mouseCaptor.on("mouseup",this.activeListeners.handleUp),this.mouseCaptor.on("mouseleave",this.activeListeners.handleLeave),this.mouseCaptor.on("mouseenter",this.activeListeners.handleEnter),this.touchCaptor.on("touchdown",this.activeListeners.handleDown),this.touchCaptor.on("touchdown",this.activeListeners.handleMove),this.touchCaptor.on("touchup",this.activeListeners.handleUp),this.touchCaptor.on("touchmove",this.activeListeners.handleMove),this.touchCaptor.on("tap",this.activeListeners.handleClick),this.touchCaptor.on("doubletap",this.activeListeners.handleDoubleClick),this.touchCaptor.on("touchmove",this.activeListeners.handleMoveBody),this}},{key:"bindGraphHandlers",value:function(){var e=this,r=this.graph,a=new Set(["x","y","zIndex","type"]);return this.activeListeners.eachNodeAttributesUpdatedGraphUpdate=function(o){var s,u=(s=o.hints)===null||s===void 0?void 0:s.attributes;e.graph.forEachNode(function(d){return e.updateNode(d)});var h=!u||u.some(function(d){return a.has(d)});e.refresh({partialGraph:{nodes:r.nodes()},skipIndexation:!h,schedule:!0})},this.activeListeners.eachEdgeAttributesUpdatedGraphUpdate=function(o){var s,u=(s=o.hints)===null||s===void 0?void 0:s.attributes;e.graph.forEachEdge(function(d){return e.updateEdge(d)});var h=u&&["zIndex","type"].some(function(d){return u==null?void 0:u.includes(d)});e.refresh({partialGraph:{edges:r.edges()},skipIndexation:!h,schedule:!0})},this.activeListeners.addNodeGraphUpdate=function(o){var s=o.key;e.addNode(s),e.refresh({partialGraph:{nodes:[s]},skipIndexation:!1,schedule:!0})},this.activeListeners.updateNodeGraphUpdate=function(o){var s=o.key;e.refresh({partialGraph:{nodes:[s]},skipIndexation:!1,schedule:!0})},this.activeListeners.dropNodeGraphUpdate=function(o){var s=o.key;e.removeNode(s),e.refresh({schedule:!0})},this.activeListeners.addEdgeGraphUpdate=function(o){var s=o.key;e.addEdge(s),e.refresh({partialGraph:{edges:[s]},schedule:!0})},this.activeListeners.updateEdgeGraphUpdate=function(o){var s=o.key;e.refresh({partialGraph:{edges:[s]},skipIndexation:!1,schedule:!0})},this.activeListeners.dropEdgeGraphUpdate=function(o){var s=o.key;e.removeEdge(s),e.refresh({schedule:!0})},this.activeListeners.clearEdgesGraphUpdate=function(){e.clearEdgeState(),e.clearEdgeIndices(),e.refresh({schedule:!0})},this.activeListeners.clearGraphUpdate=function(){e.clearEdgeState(),e.clearNodeState(),e.clearEdgeIndices(),e.clearNodeIndices(),e.refresh({schedule:!0})},r.on("nodeAdded",this.activeListeners.addNodeGraphUpdate),r.on("nodeDropped",this.activeListeners.dropNodeGraphUpdate),r.on("nodeAttributesUpdated",this.activeListeners.updateNodeGraphUpdate),r.on("eachNodeAttributesUpdated",this.activeListeners.eachNodeAttributesUpdatedGraphUpdate),r.on("edgeAdded",this.activeListeners.addEdgeGraphUpdate),r.on("edgeDropped",this.activeListeners.dropEdgeGraphUpdate),r.on("edgeAttributesUpdated",this.activeListeners.updateEdgeGraphUpdate),r.on("eachEdgeAttributesUpdated",this.activeListeners.eachEdgeAttributesUpdatedGraphUpdate),r.on("edgesCleared",this.activeListeners.clearEdgesGraphUpdate),r.on("cleared",this.activeListeners.clearGraphUpdate),this}},{key:"unbindGraphHandlers",value:function(){var e=this.graph;e.removeListener("nodeAdded",this.activeListeners.addNodeGraphUpdate),e.removeListener("nodeDropped",this.activeListeners.dropNodeGraphUpdate),e.removeListener("nodeAttributesUpdated",this.activeListeners.updateNodeGraphUpdate),e.removeListener("eachNodeAttributesUpdated",this.activeListeners.eachNodeAttributesUpdatedGraphUpdate),e.removeListener("edgeAdded",this.activeListeners.addEdgeGraphUpdate),e.removeListener("edgeDropped",this.activeListeners.dropEdgeGraphUpdate),e.removeListener("edgeAttributesUpdated",this.activeListeners.updateEdgeGraphUpdate),e.removeListener("eachEdgeAttributesUpdated",this.activeListeners.eachEdgeAttributesUpdatedGraphUpdate),e.removeListener("edgesCleared",this.activeListeners.clearEdgesGraphUpdate),e.removeListener("cleared",this.activeListeners.clearGraphUpdate)}},{key:"getEdgeAtPoint",value:function(e,r){var a=gt(this.webGLContexts.edges,this.frameBuffers.edges,e,r,this.pixelRatio,this.pickingDownSizingRatio),o=ft.apply(void 0,Lt(a)),s=this.itemIDsIndex[o];return s&&s.type==="edge"?s.id:null}},{key:"process",value:function(){var e=this;this.emit("beforeProcess");var r=this.graph,a=this.settings,o=this.getDimensions();if(this.nodeExtent=Ln(this.graph),!this.settings.autoRescale){var s=o.width,u=o.height,h=this.nodeExtent,d=h.x,l=h.y;this.nodeExtent={x:[(d[0]+d[1])/2-s/2,(d[0]+d[1])/2+s/2],y:[(l[0]+l[1])/2-u/2,(l[0]+l[1])/2+u/2]}}this.normalizationFunction=kt(this.customBBox||this.nodeExtent);var c=new Rt,f=be(c.getState(),o,this.getGraphDimensions(),this.getStagePadding());this.labelGrid.resizeAndClear(o,a.labelGridCellSize);for(var y={},b={},E={},k={},x=1,T=r.nodes(),g=0,p=T.length;g<p;g++){var v=T[g],_=this.nodeDataCache[v],A=r.getNodeAttributes(v);_.x=A.x,_.y=A.y,this.normalizationFunction.applyTo(_),typeof _.label=="string"&&!_.hidden&&this.labelGrid.add(v,_.size,this.framedGraphToViewport(_,{matrix:f})),y[_.type]=(y[_.type]||0)+1}this.labelGrid.organize();for(var D in this.nodePrograms){if(!J.call(this.nodePrograms,D))throw new Error('Sigma: could not find a suitable program for node type "'.concat(D,'"!'));this.nodePrograms[D].reallocate(y[D]||0),y[D]=0}this.settings.zIndex&&this.nodeZExtent[0]!==this.nodeZExtent[1]&&(T=St(this.nodeZExtent,function(Ie){return e.nodeDataCache[Ie].zIndex},T));for(var L=0,F=T.length;L<F;L++){var P=T[L];b[P]=x,k[b[P]]={type:"node",id:P},x++;var q=this.nodeDataCache[P];this.addNodeToProgram(P,b[P],y[q.type]++)}for(var O={},j=r.edges(),te=0,xe=j.length;te<xe;te++){var se=j[te],ae=this.edgeDataCache[se];O[ae.type]=(O[ae.type]||0)+1}this.settings.zIndex&&this.edgeZExtent[0]!==this.edgeZExtent[1]&&(j=St(this.edgeZExtent,function(Ie){return e.edgeDataCache[Ie].zIndex},j));for(var ie in this.edgePrograms){if(!J.call(this.edgePrograms,ie))throw new Error('Sigma: could not find a suitable program for edge type "'.concat(ie,'"!'));this.edgePrograms[ie].reallocate(O[ie]||0),O[ie]=0}for(var re=0,Pe=j.length;re<Pe;re++){var Z=j[re];E[Z]=x,k[E[Z]]={type:"edge",id:Z},x++;var _i=this.edgeDataCache[Z];this.addEdgeToProgram(Z,E[Z],O[_i.type]++)}return this.itemIDsIndex=k,this.nodeIndices=b,this.edgeIndices=E,this.emit("afterProcess"),this}},{key:"handleSettingsUpdate",value:function(e){var r=this,a=this.settings;if(this.camera.minRatio=a.minCameraRatio,this.camera.maxRatio=a.maxCameraRatio,this.camera.enabledZooming=a.enableCameraZooming,this.camera.enabledPanning=a.enableCameraPanning,this.camera.enabledRotation=a.enableCameraRotation,a.cameraPanBoundaries?this.camera.clean=function(d){return r.cleanCameraState(d,a.cameraPanBoundaries&&Je(a.cameraPanBoundaries)==="object"?a.cameraPanBoundaries:{})}:this.camera.clean=null,this.camera.setState(this.camera.validateState(this.camera.getState())),e){if(e.edgeProgramClasses!==a.edgeProgramClasses){for(var o in a.edgeProgramClasses)a.edgeProgramClasses[o]!==e.edgeProgramClasses[o]&&this.registerEdgeProgram(o,a.edgeProgramClasses[o]);for(var s in e.edgeProgramClasses)a.edgeProgramClasses[s]||this.unregisterEdgeProgram(s)}if(e.nodeProgramClasses!==a.nodeProgramClasses||e.nodeHoverProgramClasses!==a.nodeHoverProgramClasses){for(var u in a.nodeProgramClasses)(a.nodeProgramClasses[u]!==e.nodeProgramClasses[u]||a.nodeHoverProgramClasses[u]!==e.nodeHoverProgramClasses[u])&&this.registerNodeProgram(u,a.nodeProgramClasses[u],a.nodeHoverProgramClasses[u]);for(var h in e.nodeProgramClasses)a.nodeProgramClasses[h]||this.unregisterNodeProgram(h)}}return this.mouseCaptor.setSettings(this.settings),this.touchCaptor.setSettings(this.settings),this}},{key:"cleanCameraState",value:function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},a=r.tolerance,o=a===void 0?0:a,s=r.boundaries,u=S({},e),h=s||this.nodeExtent,d=le(h.x,2),l=d[0],c=d[1],f=le(h.y,2),y=f[0],b=f[1],E=[this.graphToViewport({x:l,y},{cameraState:e}),this.graphToViewport({x:c,y},{cameraState:e}),this.graphToViewport({x:l,y:b},{cameraState:e}),this.graphToViewport({x:c,y:b},{cameraState:e})],k=1/0,x=-1/0,T=1/0,g=-1/0;E.forEach(function(O){var j=O.x,te=O.y;k=Math.min(k,j),x=Math.max(x,j),T=Math.min(T,te),g=Math.max(g,te)});var p=x-k,v=g-T,_=this.getDimensions(),A=_.width,D=_.height,L=0,F=0;if(p>=A?x<A-o?L=x-(A-o):k>o&&(L=k-o):x>A+o?L=x-(A+o):k<-o&&(L=k+o),v>=D?g<D-o?F=g-(D-o):T>o&&(F=T-o):g>D+o?F=g-(D+o):T<-o&&(F=T+o),L||F){var P=this.viewportToFramedGraph({x:0,y:0},{cameraState:e}),q=this.viewportToFramedGraph({x:L,y:F},{cameraState:e});L=q.x-P.x,F=q.y-P.y,u.x+=L,u.y+=F}return u}},{key:"renderLabels",value:function(){if(!this.settings.renderLabels)return this;var e=this.camera.getState(),r=this.labelGrid.getLabelsToDisplay(e.ratio,this.settings.labelDensity);At(r,this.nodesWithForcedLabels),this.displayedNodeLabels=new Set;for(var a=this.canvasContexts.labels,o=0,s=r.length;o<s;o++){var u=r[o],h=this.nodeDataCache[u];if(!this.displayedNodeLabels.has(u)&&!h.hidden){var d=this.framedGraphToViewport(h),l=d.x,c=d.y,f=this.scaleSize(h.size);if(!(!h.forceLabel&&f<this.settings.labelRenderedSizeThreshold)&&!(l<-150||l>this.width+qn||c<-50||c>this.height+Zn)){this.displayedNodeLabels.add(u);var y=this.settings.defaultDrawNodeLabel,b=this.nodePrograms[h.type],E=(b==null?void 0:b.drawLabel)||y;E(a,S(S({key:u},h),{},{size:f,x:l,y:c}),this.settings)}}}return this}},{key:"renderEdgeLabels",value:function(){if(!this.settings.renderEdgeLabels)return this;var e=this.canvasContexts.edgeLabels;e.clearRect(0,0,this.width,this.height);var r=Yn({graph:this.graph,hoveredNode:this.hoveredNode,displayedNodeLabels:this.displayedNodeLabels,highlightedNodes:this.highlightedNodes});At(r,this.edgesWithForcedLabels);for(var a=new Set,o=0,s=r.length;o<s;o++){var u=r[o],h=this.graph.extremities(u),d=this.nodeDataCache[h[0]],l=this.nodeDataCache[h[1]],c=this.edgeDataCache[u];if(!a.has(u)&&!(c.hidden||d.hidden||l.hidden)){var f=this.settings.defaultDrawEdgeLabel,y=this.edgePrograms[c.type],b=(y==null?void 0:y.drawLabel)||f;b(e,S(S({key:u},c),{},{size:this.scaleSize(c.size)}),S(S(S({key:h[0]},d),this.framedGraphToViewport(d)),{},{size:this.scaleSize(d.size)}),S(S(S({key:h[1]},l),this.framedGraphToViewport(l)),{},{size:this.scaleSize(l.size)}),this.settings),a.add(u)}}return this.displayedEdgeLabels=a,this}},{key:"renderHighlightedNodes",value:function(){var e=this,r=this.canvasContexts.hovers;r.clearRect(0,0,this.width,this.height);var a=function(f){var y=e.nodeDataCache[f],b=e.framedGraphToViewport(y),E=b.x,k=b.y,x=e.scaleSize(y.size),T=e.settings.defaultDrawNodeHover,g=e.nodePrograms[y.type],p=(g==null?void 0:g.drawHover)||T;p(r,S(S({key:f},y),{},{size:x,x:E,y:k}),e.settings)},o=[];this.hoveredNode&&!this.nodeDataCache[this.hoveredNode].hidden&&o.push(this.hoveredNode),this.highlightedNodes.forEach(function(c){c!==e.hoveredNode&&o.push(c)}),o.forEach(function(c){return a(c)});var s={};o.forEach(function(c){var f=e.nodeDataCache[c].type;s[f]=(s[f]||0)+1});for(var u in this.nodeHoverPrograms)this.nodeHoverPrograms[u].reallocate(s[u]||0),s[u]=0;o.forEach(function(c){var f=e.nodeDataCache[c];e.nodeHoverPrograms[f.type].process(0,s[f.type]++,f)}),this.webGLContexts.hoverNodes.clear(this.webGLContexts.hoverNodes.COLOR_BUFFER_BIT);var h=this.getRenderParams();for(var d in this.nodeHoverPrograms){var l=this.nodeHoverPrograms[d];l.render(h)}}},{key:"scheduleHighlightedNodesRender",value:function(){var e=this;this.renderHighlightedNodesFrame||this.renderFrame||(this.renderHighlightedNodesFrame=requestAnimationFrame(function(){e.renderHighlightedNodesFrame=null,e.renderHighlightedNodes(),e.renderEdgeLabels()}))}},{key:"render",value:function(){var e=this;this.emit("beforeRender");var r=function(){return e.emit("afterRender"),e};if(this.renderFrame&&(cancelAnimationFrame(this.renderFrame),this.renderFrame=null),this.resize(),this.needToProcess&&this.process(),this.needToProcess=!1,this.clear(),this.pickingLayers.forEach(function(E){return e.resetWebGLTexture(E)}),!this.graph.order)return r();var a=this.mouseCaptor,o=this.camera.isAnimated()||a.isMoving||a.draggedEvents||a.currentWheelDirection,s=this.camera.getState(),u=this.getDimensions(),h=this.getGraphDimensions(),d=this.getStagePadding();this.matrix=be(s,u,h,d),this.invMatrix=be(s,u,h,d,!0),this.correctionRatio=Rn(this.matrix,s,u),this.graphToViewportRatio=this.getGraphToViewportRatio();var l=this.getRenderParams();for(var c in this.nodePrograms){var f=this.nodePrograms[c];f.render(l)}if(!this.settings.hideEdgesOnMove||!o)for(var y in this.edgePrograms){var b=this.edgePrograms[y];b.render(l)}return this.settings.hideLabelsOnMove&&o||(this.renderLabels(),this.renderEdgeLabels(),this.renderHighlightedNodes()),r()}},{key:"addNode",value:function(e){var r=Object.assign({},this.graph.getNodeAttributes(e));this.settings.nodeReducer&&(r=this.settings.nodeReducer(e,r));var a=Xn(this.settings,e,r);this.nodeDataCache[e]=a,this.nodesWithForcedLabels.delete(e),a.forceLabel&&!a.hidden&&this.nodesWithForcedLabels.add(e),this.highlightedNodes.delete(e),a.highlighted&&!a.hidden&&this.highlightedNodes.add(e),this.settings.zIndex&&(a.zIndex<this.nodeZExtent[0]&&(this.nodeZExtent[0]=a.zIndex),a.zIndex>this.nodeZExtent[1]&&(this.nodeZExtent[1]=a.zIndex))}},{key:"updateNode",value:function(e){this.addNode(e);var r=this.nodeDataCache[e];this.normalizationFunction.applyTo(r)}},{key:"removeNode",value:function(e){delete this.nodeDataCache[e],delete this.nodeProgramIndex[e],this.highlightedNodes.delete(e),this.hoveredNode===e&&(this.hoveredNode=null),this.nodesWithForcedLabels.delete(e)}},{key:"addEdge",value:function(e){var r=Object.assign({},this.graph.getEdgeAttributes(e));this.settings.edgeReducer&&(r=this.settings.edgeReducer(e,r));var a=Jn(this.settings,e,r);this.edgeDataCache[e]=a,this.edgesWithForcedLabels.delete(e),a.forceLabel&&!a.hidden&&this.edgesWithForcedLabels.add(e),this.settings.zIndex&&(a.zIndex<this.edgeZExtent[0]&&(this.edgeZExtent[0]=a.zIndex),a.zIndex>this.edgeZExtent[1]&&(this.edgeZExtent[1]=a.zIndex))}},{key:"updateEdge",value:function(e){this.addEdge(e)}},{key:"removeEdge",value:function(e){delete this.edgeDataCache[e],delete this.edgeProgramIndex[e],this.hoveredEdge===e&&(this.hoveredEdge=null),this.edgesWithForcedLabels.delete(e)}},{key:"clearNodeIndices",value:function(){this.labelGrid=new Nt,this.nodeExtent={x:[0,1],y:[0,1]},this.nodeDataCache={},this.edgeProgramIndex={},this.nodesWithForcedLabels=new Set,this.nodeZExtent=[1/0,-1/0]}},{key:"clearEdgeIndices",value:function(){this.edgeDataCache={},this.edgeProgramIndex={},this.edgesWithForcedLabels=new Set,this.edgeZExtent=[1/0,-1/0]}},{key:"clearIndices",value:function(){this.clearEdgeIndices(),this.clearNodeIndices()}},{key:"clearNodeState",value:function(){this.displayedNodeLabels=new Set,this.highlightedNodes=new Set,this.hoveredNode=null}},{key:"clearEdgeState",value:function(){this.displayedEdgeLabels=new Set,this.highlightedNodes=new Set,this.hoveredEdge=null}},{key:"clearState",value:function(){this.clearEdgeState(),this.clearNodeState()}},{key:"addNodeToProgram",value:function(e,r,a){var o=this.nodeDataCache[e],s=this.nodePrograms[o.type];if(!s)throw new Error('Sigma: could not find a suitable program for node type "'.concat(o.type,'"!'));s.process(r,a,o),this.nodeProgramIndex[e]=a}},{key:"addEdgeToProgram",value:function(e,r,a){var o=this.edgeDataCache[e],s=this.edgePrograms[o.type];if(!s)throw new Error('Sigma: could not find a suitable program for edge type "'.concat(o.type,'"!'));var u=this.graph.extremities(e),h=this.nodeDataCache[u[0]],d=this.nodeDataCache[u[1]];s.process(r,a,h,d,o),this.edgeProgramIndex[e]=a}},{key:"getRenderParams",value:function(){return{matrix:this.matrix,invMatrix:this.invMatrix,width:this.width,height:this.height,pixelRatio:this.pixelRatio,zoomRatio:this.camera.ratio,cameraAngle:this.camera.angle,sizeRatio:1/this.scaleSize(),correctionRatio:this.correctionRatio,downSizingRatio:this.pickingDownSizingRatio,minEdgeThickness:this.settings.minEdgeThickness,antiAliasingFeather:this.settings.antiAliasingFeather}}},{key:"getStagePadding",value:function(){var e=this.settings,r=e.stagePadding,a=e.autoRescale;return a&&r||0}},{key:"createLayer",value:function(e,r){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(this.elements[e])throw new Error('Sigma: a layer named "'.concat(e,'" already exists'));var o=Nn(r,{position:"absolute"},{class:"sigma-".concat(e)});return a.style&&Object.assign(o.style,a.style),this.elements[e]=o,"beforeLayer"in a&&a.beforeLayer?this.elements[a.beforeLayer].before(o):"afterLayer"in a&&a.afterLayer?this.elements[a.afterLayer].after(o):this.container.appendChild(o),o}},{key:"createCanvas",value:function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return this.createLayer(e,"canvas",r)}},{key:"createCanvasContext",value:function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},a=this.createCanvas(e,r),o={preserveDrawingBuffer:!1,antialias:!1};return this.canvasContexts[e]=a.getContext("2d",o),this}},{key:"createWebGLContext",value:function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},a=(r==null?void 0:r.canvas)||this.createCanvas(e,r);r.hidden&&a.remove();var o=S({preserveDrawingBuffer:!1,antialias:!1},r),s;s=a.getContext("webgl2",o),s||(s=a.getContext("webgl",o)),s||(s=a.getContext("experimental-webgl",o));var u=s;if(this.webGLContexts[e]=u,u.blendFunc(u.ONE,u.ONE_MINUS_SRC_ALPHA),r.picking){this.pickingLayers.add(e);var h=u.createFramebuffer();if(!h)throw new Error("Sigma: cannot create a new frame buffer for layer ".concat(e));this.frameBuffers[e]=h}return u}},{key:"killLayer",value:function(e){var r=this.elements[e];if(!r)throw new Error("Sigma: cannot kill layer ".concat(e,", which does not exist"));if(this.webGLContexts[e]){var a,o=this.webGLContexts[e];(a=o.getExtension("WEBGL_lose_context"))===null||a===void 0||a.loseContext(),delete this.webGLContexts[e]}else this.canvasContexts[e]&&delete this.canvasContexts[e];return r.remove(),delete this.elements[e],this}},{key:"getCamera",value:function(){return this.camera}},{key:"setCamera",value:function(e){this.unbindCameraHandlers(),this.camera=e,this.bindCameraHandlers()}},{key:"getContainer",value:function(){return this.container}},{key:"getGraph",value:function(){return this.graph}},{key:"setGraph",value:function(e){e!==this.graph&&(this.hoveredNode&&!e.hasNode(this.hoveredNode)&&(this.hoveredNode=null),this.hoveredEdge&&!e.hasEdge(this.hoveredEdge)&&(this.hoveredEdge=null),this.unbindGraphHandlers(),this.checkEdgesEventsFrame!==null&&(cancelAnimationFrame(this.checkEdgesEventsFrame),this.checkEdgesEventsFrame=null),this.graph=e,this.bindGraphHandlers(),this.refresh())}},{key:"getMouseCaptor",value:function(){return this.mouseCaptor}},{key:"getTouchCaptor",value:function(){return this.touchCaptor}},{key:"getDimensions",value:function(){return{width:this.width,height:this.height}}},{key:"getGraphDimensions",value:function(){var e=this.customBBox||this.nodeExtent;return{width:e.x[1]-e.x[0]||1,height:e.y[1]-e.y[0]||1}}},{key:"getNodeDisplayData",value:function(e){var r=this.nodeDataCache[e];return r?Object.assign({},r):void 0}},{key:"getEdgeDisplayData",value:function(e){var r=this.edgeDataCache[e];return r?Object.assign({},r):void 0}},{key:"getNodeDisplayedLabels",value:function(){return new Set(this.displayedNodeLabels)}},{key:"getEdgeDisplayedLabels",value:function(){return new Set(this.displayedEdgeLabels)}},{key:"getSettings",value:function(){return S({},this.settings)}},{key:"getSetting",value:function(e){return this.settings[e]}},{key:"setSetting",value:function(e,r){var a=S({},this.settings);return this.settings[e]=r,Ve(this.settings),this.handleSettingsUpdate(a),this.scheduleRefresh(),this}},{key:"updateSetting",value:function(e,r){return this.setSetting(e,r(this.settings[e])),this}},{key:"setSettings",value:function(e){var r=S({},this.settings);return this.settings=S(S({},this.settings),e),Ve(this.settings),this.handleSettingsUpdate(r),this.scheduleRefresh(),this}},{key:"resize",value:function(e){var r=this.width,a=this.height;if(this.width=this.container.offsetWidth,this.height=this.container.offsetHeight,this.pixelRatio=Ct(),this.width===0)if(this.settings.allowInvalidContainer)this.width=1;else throw new Error("Sigma: Container has no width. You can set the allowInvalidContainer setting to true to stop seeing this error.");if(this.height===0)if(this.settings.allowInvalidContainer)this.height=1;else throw new Error("Sigma: Container has no height. You can set the allowInvalidContainer setting to true to stop seeing this error.");if(!e&&r===this.width&&a===this.height)return this;for(var o in this.elements){var s=this.elements[o];s.style.width=this.width+"px",s.style.height=this.height+"px"}for(var u in this.canvasContexts)this.elements[u].setAttribute("width",this.width*this.pixelRatio+"px"),this.elements[u].setAttribute("height",this.height*this.pixelRatio+"px"),this.pixelRatio!==1&&this.canvasContexts[u].scale(this.pixelRatio,this.pixelRatio);for(var h in this.webGLContexts){this.elements[h].setAttribute("width",this.width*this.pixelRatio+"px"),this.elements[h].setAttribute("height",this.height*this.pixelRatio+"px");var d=this.webGLContexts[h];if(d.viewport(0,0,this.width*this.pixelRatio,this.height*this.pixelRatio),this.pickingLayers.has(h)){var l=this.textures[h];l&&d.deleteTexture(l)}}return this.emit("resize"),this}},{key:"clear",value:function(){return this.emit("beforeClear"),this.webGLContexts.nodes.bindFramebuffer(WebGLRenderingContext.FRAMEBUFFER,null),this.webGLContexts.nodes.clear(WebGLRenderingContext.COLOR_BUFFER_BIT),this.webGLContexts.edges.bindFramebuffer(WebGLRenderingContext.FRAMEBUFFER,null),this.webGLContexts.edges.clear(WebGLRenderingContext.COLOR_BUFFER_BIT),this.webGLContexts.hoverNodes.clear(WebGLRenderingContext.COLOR_BUFFER_BIT),this.canvasContexts.labels.clearRect(0,0,this.width,this.height),this.canvasContexts.hovers.clearRect(0,0,this.width,this.height),this.canvasContexts.edgeLabels.clearRect(0,0,this.width,this.height),this.emit("afterClear"),this}},{key:"refresh",value:function(e){var r=this,a=(e==null?void 0:e.skipIndexation)!==void 0?e==null?void 0:e.skipIndexation:!1,o=(e==null?void 0:e.schedule)!==void 0?e.schedule:!1,s=!e||!e.partialGraph;if(s)this.clearEdgeIndices(),this.clearNodeIndices(),this.graph.forEachNode(function(g){return r.addNode(g)}),this.graph.forEachEdge(function(g){return r.addEdge(g)});else{for(var u,h,d=((u=e.partialGraph)===null||u===void 0?void 0:u.nodes)||[],l=0,c=(d==null?void 0:d.length)||0;l<c;l++){var f=d[l];if(this.updateNode(f),a){var y=this.nodeProgramIndex[f];if(y===void 0)throw new Error('Sigma: node "'.concat(f,`" can't be repaint`));this.addNodeToProgram(f,this.nodeIndices[f],y)}}for(var b=(e==null||(h=e.partialGraph)===null||h===void 0?void 0:h.edges)||[],E=0,k=b.length;E<k;E++){var x=b[E];if(this.updateEdge(x),a){var T=this.edgeProgramIndex[x];if(T===void 0)throw new Error('Sigma: edge "'.concat(x,`" can't be repaint`));this.addEdgeToProgram(x,this.edgeIndices[x],T)}}}return(s||!a)&&(this.needToProcess=!0),o?this.scheduleRender():this.render(),this}},{key:"scheduleRender",value:function(){var e=this;return this.renderFrame||(this.renderFrame=requestAnimationFrame(function(){e.render()})),this}},{key:"scheduleRefresh",value:function(e){return this.refresh(S(S({},e),{},{schedule:!0}))}},{key:"getViewportZoomedState",value:function(e,r){var a=this.camera.getState(),o=a.ratio,s=a.angle,u=a.x,h=a.y,d=this.settings,l=d.minCameraRatio,c=d.maxCameraRatio;typeof c=="number"&&(r=Math.min(r,c)),typeof l=="number"&&(r=Math.max(r,l));var f=r/o,y={x:this.width/2,y:this.height/2},b=this.viewportToFramedGraph(e),E=this.viewportToFramedGraph(y);return{angle:s,x:(b.x-E.x)*(1-f)+u,y:(b.y-E.y)*(1-f)+h,ratio:r}}},{key:"viewRectangle",value:function(){var e=this.viewportToFramedGraph({x:0,y:0}),r=this.viewportToFramedGraph({x:this.width,y:0}),a=this.viewportToFramedGraph({x:0,y:this.height});return{x1:e.x,y1:e.y,x2:r.x,y2:r.y,height:r.y-a.y}}},{key:"framedGraphToViewport",value:function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},a=!!r.cameraState||!!r.viewportDimensions||!!r.graphDimensions,o=r.matrix?r.matrix:a?be(r.cameraState||this.camera.getState(),r.viewportDimensions||this.getDimensions(),r.graphDimensions||this.getGraphDimensions(),r.padding||this.getStagePadding()):this.matrix,s=Xe(o,e);return{x:(1+s.x)*this.width/2,y:(1-s.y)*this.height/2}}},{key:"viewportToFramedGraph",value:function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},a=!!r.cameraState||!!r.viewportDimensions||!r.graphDimensions,o=r.matrix?r.matrix:a?be(r.cameraState||this.camera.getState(),r.viewportDimensions||this.getDimensions(),r.graphDimensions||this.getGraphDimensions(),r.padding||this.getStagePadding(),!0):this.invMatrix,s=Xe(o,{x:e.x/this.width*2-1,y:1-e.y/this.height*2});return isNaN(s.x)&&(s.x=0),isNaN(s.y)&&(s.y=0),s}},{key:"viewportToGraph",value:function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return this.normalizationFunction.inverse(this.viewportToFramedGraph(e,r))}},{key:"graphToViewport",value:function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return this.framedGraphToViewport(this.normalizationFunction(e),r)}},{key:"getGraphToViewportRatio",value:function(){var e={x:0,y:0},r={x:1,y:1},a=Math.sqrt(Math.pow(e.x-r.x,2)+Math.pow(e.y-r.y,2)),o=this.graphToViewport(e),s=this.graphToViewport(r),u=Math.sqrt(Math.pow(o.x-s.x,2)+Math.pow(o.y-s.y,2));return u/a}},{key:"getBBox",value:function(){return this.nodeExtent}},{key:"getCustomBBox",value:function(){return this.customBBox}},{key:"setCustomBBox",value:function(e){return this.customBBox=e,this.scheduleRender(),this}},{key:"kill",value:function(){this.emit("kill"),this.removeAllListeners(),this.unbindCameraHandlers(),window.removeEventListener("resize",this.activeListeners.handleResize),this.mouseCaptor.kill(),this.touchCaptor.kill(),this.unbindGraphHandlers(),this.clearIndices(),this.clearState(),this.nodeDataCache={},this.edgeDataCache={},this.highlightedNodes.clear(),this.renderFrame&&(cancelAnimationFrame(this.renderFrame),this.renderFrame=null),this.renderHighlightedNodesFrame&&(cancelAnimationFrame(this.renderHighlightedNodesFrame),this.renderHighlightedNodesFrame=null);for(var e=this.container;e.firstChild;)e.removeChild(e.firstChild);this.canvasContexts={},this.webGLContexts={},this.elements={};for(var r in this.nodePrograms)this.nodePrograms[r].kill();for(var a in this.nodeHoverPrograms)this.nodeHoverPrograms[a].kill();for(var o in this.edgePrograms)this.edgePrograms[o].kill();this.nodePrograms={},this.nodeHoverPrograms={},this.edgePrograms={};for(var s in this.elements)this.killLayer(s)}},{key:"scaleSize",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:1,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.camera.ratio;return e/this.settings.zoomToSizeRatioFunction(r)*(this.getSetting("itemSizesReference")==="positions"?r*this.graphToViewportRatio:1)}},{key:"getCanvases",value:function(){var e={};for(var r in this.elements)this.elements[r]instanceof HTMLCanvasElement&&(e[r]=this.elements[r]);return e}}])}(nt);const wi=G.createContext(null),ea=wi.Provider;function ot(){const n=G.useContext(wi);if(n==null)throw new Error("No context provided: useSigmaContext() can only be used in a descendant of <SigmaContainer>");return n}function Ei(){return ot().sigma}function ta(){const{sigma:n}=ot();return G.useCallback(i=>{n&&Object.keys(i).forEach(t=>{n.setSetting(t,i[t])})},[n])}function Fe(n){return new Set(Object.keys(n))}const Gt=Fe({clickNode:!0,rightClickNode:!0,downNode:!0,enterNode:!0,leaveNode:!0,doubleClickNode:!0,wheelNode:!0,clickEdge:!0,rightClickEdge:!0,downEdge:!0,enterEdge:!0,leaveEdge:!0,doubleClickEdge:!0,wheelEdge:!0,clickStage:!0,rightClickStage:!0,downStage:!0,doubleClickStage:!0,wheelStage:!0,beforeRender:!0,afterRender:!0,kill:!0,upStage:!0,upEdge:!0,upNode:!0,enterStage:!0,leaveStage:!0,resize:!0,afterClear:!0,afterProcess:!0,beforeClear:!0,beforeProcess:!0,moveBody:!0}),Ft=Fe({click:!0,rightClick:!0,doubleClick:!0,mouseup:!0,mousedown:!0,mousemove:!0,mousemovebody:!0,mouseleave:!0,mouseenter:!0,wheel:!0}),Pt=Fe({touchup:!0,touchdown:!0,touchmove:!0,touchmovebody:!0,tap:!0,doubletap:!0}),It=Fe({updated:!0});function oa(){const n=Ei(),i=ta(),[t,e]=G.useState({});return G.useEffect(()=>{if(!n||!t)return;const r=t,a=Object.keys(r);return a.forEach(o=>{const s=r[o];Gt.has(o)&&n.on(o,s),Ft.has(o)&&n.getMouseCaptor().on(o,s),Pt.has(o)&&n.getTouchCaptor().on(o,s),It.has(o)&&n.getCamera().on(o,s)}),()=>{n&&a.forEach(o=>{const s=r[o];Gt.has(o)&&n.off(o,s),Ft.has(o)&&n.getMouseCaptor().off(o,s),Pt.has(o)&&n.getTouchCaptor().off(o,s),It.has(o)&&n.getCamera().off(o,s)})}},[n,t,i]),e}function st(n,i){if(n===i)return!0;if(typeof n=="object"&&n!=null&&typeof i=="object"&&i!=null){if(Object.keys(n).length!=Object.keys(i).length)return!1;for(const t in n)if(!Object.hasOwn(i,t)||!st(n[t],i[t]))return!1;return!0}return!1}function sa(n){const i=Ei(),[t,e]=G.useState(n||{});G.useEffect(()=>{e(h=>st(h,n||{})?h:n||{})},[n]);const r=G.useCallback(h=>{i.getCamera().animatedZoom(Object.assign(Object.assign({},t),h))},[i,t]),a=G.useCallback(h=>{i.getCamera().animatedUnzoom(Object.assign(Object.assign({},t),h))},[i,t]),o=G.useCallback(h=>{i.getCamera().animatedReset(Object.assign(Object.assign({},t),h))},[i,t]),s=G.useCallback((h,d)=>{i.getCamera().animate(h,Object.assign(Object.assign({},t),d))},[i,t]),u=G.useCallback((h,d)=>{const l=i.getNodeDisplayData(h);l?i.getCamera().animate(l,Object.assign(Object.assign({},t),d)):console.warn(`Node ${h} not found`)},[i,t]);return{zoomIn:r,zoomOut:a,reset:o,goto:s,gotoNode:u}}function ua(n){const i=ot(),[t,e]=G.useState(!1),[r,a]=G.useState(i.container),o=G.useCallback(()=>e(s=>!s),[]);return G.useEffect(()=>(document.addEventListener("fullscreenchange",o),()=>document.removeEventListener("fullscreenchange",o)),[o]),G.useEffect(()=>{a(i.container)},[n,i.container]),{toggle:G.useCallback(()=>{var s;s=r,document.fullscreenElement!==s?s.requestFullscreen():document.exitFullscreen&&document.exitFullscreen()},[r]),isFullScreen:t}}const ha=G.forwardRef(({graph:n,id:i,className:t,style:e,settings:r={},children:a},o)=>{const s=G.useRef(null),u=G.useRef(null),h={className:`react-sigma ${t||""}`,id:i,style:e},[d,l]=G.useState(null),[c,f]=G.useState(r);G.useEffect(()=>{f(E=>st(E,r)?E:r)},[r]),G.useEffect(()=>{l(E=>{let k=null;if(u.current!==null){let x=new N;n&&(x=typeof n=="function"?new n:n);let T=null;E&&(T=E.getCamera().getState(),E.kill()),k=new Qn(x,u.current,c),T&&k.getCamera().setState(T)}return k})},[u,n,c]),G.useImperativeHandle(o,()=>d,[d]);const y=G.useMemo(()=>d&&s.current?{sigma:d,container:s.current}:null,[d,s]),b=y!==null?Me.createElement(ea,{value:y},a):null;return Me.createElement("div",Object.assign({},h,{ref:s}),Me.createElement("div",{className:"sigma-container",ref:u}),b)});export{di as D,rt as E,ci as F,jr as N,ha as S,Yt as U,W as _,$ as a,U as b,H as c,S as d,Vr as e,pe as f,li as g,ra as h,sa as i,st as j,aa as k,ua as l,Ge as m,na as n,fn as o,ta as p,yn as r,Ei as v,oa as y};
