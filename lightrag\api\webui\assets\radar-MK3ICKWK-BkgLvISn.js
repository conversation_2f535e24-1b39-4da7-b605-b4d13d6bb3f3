var Rc=Object.defineProperty;var Ac=(n,e,t)=>e in n?Rc(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var Qe=(n,e,t)=>Ac(n,typeof e!="symbol"?e+"":e,t);import{a6 as It}from"./feature-graph-Chuw_ipx.js";import{bu as Ec,bv as vc,aY as yl,be as kc,b0 as Sc,aZ as te,aq as xc,ar as ua,b4 as Ic,b7 as Tl,b8 as Rl,b5 as Cc,bj as ca,at as Tt,au as D,a_ as da,aU as $c}from"./mermaid-vendor-DGPC_TDM.js";import{k as Ht,j as Ls,g as Zt,S as Nc,w as wc,x as _c,c as Al,v as z,y as El,l as Lc,z as Oc,A as bc,B as Pc,C as Mc,a as vl,d as $,i as qe,r as oe,f as Se,D as Y}from"./_baseUniq-D-427rzS.js";import{j as Os,m as x,d as Dc,f as Ne,g as zt,h as N,i as bs,l as qt,e as Fc}from"./_basePickBy-CGXZVaMl.js";import{c as re}from"./clone-BvhE3BKU.js";var Gc=Object.prototype,Uc=Gc.hasOwnProperty,ke=Ec(function(n,e){if(vc(e)||yl(e)){kc(e,Ht(e),n);return}for(var t in e)Uc.call(e,t)&&Sc(n,t,e[t])});function kl(n,e,t){var r=-1,i=n.length;e<0&&(e=-e>i?0:i+e),t=t>i?i:t,t<0&&(t+=i),i=e>t?0:t-e>>>0,e>>>=0;for(var s=Array(i);++r<i;)s[r]=n[r+e];return s}function Jn(n){for(var e=-1,t=n==null?0:n.length,r=0,i=[];++e<t;){var s=n[e];s&&(i[r++]=s)}return i}function Bc(n,e,t,r){for(var i=-1,s=n==null?0:n.length;++i<s;){var a=n[i];e(r,a,t(a),n)}return r}function Vc(n,e,t,r){return Ls(n,function(i,s,a){e(r,i,t(i),a)}),r}function Wc(n,e){return function(t,r){var i=te(t)?Bc:Vc,s=e?e():{};return i(t,n,Zt(r),s)}}var jc=200;function Kc(n,e,t,r){var i=-1,s=wc,a=!0,o=n.length,l=[],u=e.length;if(!o)return l;e.length>=jc&&(s=_c,a=!1,e=new Nc(e));e:for(;++i<o;){var c=n[i],d=c;if(c=c!==0?c:0,a&&d===d){for(var h=u;h--;)if(e[h]===d)continue e;l.push(c)}else s(e,d,r)||l.push(c)}return l}var li=xc(function(n,e){return ua(n)?Kc(n,Al(e,1,ua,!0)):[]});function Q(n,e,t){var r=n==null?0:n.length;return r?(e=e===void 0?1:Os(e),kl(n,e<0?0:e,r)):[]}function zn(n,e,t){var r=n==null?0:n.length;return r?(e=e===void 0?1:Os(e),e=r-e,kl(n,0,e<0?0:e)):[]}function Hc(n,e){for(var t=-1,r=n==null?0:n.length;++t<r;)if(!e(n[t],t,n))return!1;return!0}function zc(n,e){var t=!0;return Ls(n,function(r,i,s){return t=!!e(r,i,s),t}),t}function be(n,e,t){var r=te(n)?Hc:zc;return r(n,Zt(e))}function Pe(n){return n&&n.length?n[0]:void 0}function ve(n,e){return Al(x(n,e))}var qc=Object.prototype,Yc=qc.hasOwnProperty,Xc=Wc(function(n,e,t){Yc.call(n,t)?n[t].push(e):Ic(n,t,[e])}),Jc="[object String]";function fe(n){return typeof n=="string"||!te(n)&&Tl(n)&&Rl(n)==Jc}var Qc=Math.max;function ce(n,e,t,r){n=yl(n)?n:z(n),t=t?Os(t):0;var i=n.length;return t<0&&(t=Qc(i+t,0)),fe(n)?t<=i&&n.indexOf(e,t)>-1:!!i&&El(n,e,t)>-1}function fa(n,e,t){var r=n==null?0:n.length;if(!r)return-1;var i=0;return El(n,e,i)}var Zc="[object RegExp]";function ed(n){return Tl(n)&&Rl(n)==Zc}var ha=ca&&ca.isRegExp,Ye=ha?Cc(ha):ed,td="Expected a function";function nd(n){if(typeof n!="function")throw new TypeError(td);return function(){var e=arguments;switch(e.length){case 0:return!n.call(this);case 1:return!n.call(this,e[0]);case 2:return!n.call(this,e[0],e[1]);case 3:return!n.call(this,e[0],e[1],e[2])}return!n.apply(this,e)}}function Me(n,e){if(n==null)return{};var t=Lc(Oc(n),function(r){return[r]});return e=Zt(e),Dc(n,t,function(r,i){return e(r,i[0])})}function ui(n,e){var t=te(n)?bc:Pc;return t(n,nd(Zt(e)))}function rd(n,e){var t;return Ls(n,function(r,i,s){return t=e(r,i,s),!t}),!!t}function Sl(n,e,t){var r=te(n)?Mc:rd;return r(n,Zt(e))}function Ps(n){return n&&n.length?vl(n):[]}function id(n,e){return n&&n.length?vl(n,Zt(e)):[]}function ae(n){return typeof n=="object"&&n!==null&&typeof n.$type=="string"}function Ue(n){return typeof n=="object"&&n!==null&&typeof n.$refText=="string"}function sd(n){return typeof n=="object"&&n!==null&&typeof n.name=="string"&&typeof n.type=="string"&&typeof n.path=="string"}function Sr(n){return typeof n=="object"&&n!==null&&ae(n.container)&&Ue(n.reference)&&typeof n.message=="string"}class xl{constructor(){this.subtypes={},this.allSubtypes={}}isInstance(e,t){return ae(e)&&this.isSubtype(e.$type,t)}isSubtype(e,t){if(e===t)return!0;let r=this.subtypes[e];r||(r=this.subtypes[e]={});const i=r[t];if(i!==void 0)return i;{const s=this.computeIsSubtype(e,t);return r[t]=s,s}}getAllSubTypes(e){const t=this.allSubtypes[e];if(t)return t;{const r=this.getAllTypes(),i=[];for(const s of r)this.isSubtype(s,e)&&i.push(s);return this.allSubtypes[e]=i,i}}}function qn(n){return typeof n=="object"&&n!==null&&Array.isArray(n.content)}function Il(n){return typeof n=="object"&&n!==null&&typeof n.tokenType=="object"}function Cl(n){return qn(n)&&typeof n.fullText=="string"}class Z{constructor(e,t){this.startFn=e,this.nextFn=t}iterator(){const e={state:this.startFn(),next:()=>this.nextFn(e.state),[Symbol.iterator]:()=>e};return e}[Symbol.iterator](){return this.iterator()}isEmpty(){return!!this.iterator().next().done}count(){const e=this.iterator();let t=0,r=e.next();for(;!r.done;)t++,r=e.next();return t}toArray(){const e=[],t=this.iterator();let r;do r=t.next(),r.value!==void 0&&e.push(r.value);while(!r.done);return e}toSet(){return new Set(this)}toMap(e,t){const r=this.map(i=>[e?e(i):i,t?t(i):i]);return new Map(r)}toString(){return this.join()}concat(e){return new Z(()=>({first:this.startFn(),firstDone:!1,iterator:e[Symbol.iterator]()}),t=>{let r;if(!t.firstDone){do if(r=this.nextFn(t.first),!r.done)return r;while(!r.done);t.firstDone=!0}do if(r=t.iterator.next(),!r.done)return r;while(!r.done);return Ae})}join(e=","){const t=this.iterator();let r="",i,s=!1;do i=t.next(),i.done||(s&&(r+=e),r+=ad(i.value)),s=!0;while(!i.done);return r}indexOf(e,t=0){const r=this.iterator();let i=0,s=r.next();for(;!s.done;){if(i>=t&&s.value===e)return i;s=r.next(),i++}return-1}every(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(!e(r.value))return!1;r=t.next()}return!0}some(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(e(r.value))return!0;r=t.next()}return!1}forEach(e){const t=this.iterator();let r=0,i=t.next();for(;!i.done;)e(i.value,r),i=t.next(),r++}map(e){return new Z(this.startFn,t=>{const{done:r,value:i}=this.nextFn(t);return r?Ae:{done:!1,value:e(i)}})}filter(e){return new Z(this.startFn,t=>{let r;do if(r=this.nextFn(t),!r.done&&e(r.value))return r;while(!r.done);return Ae})}nonNullable(){return this.filter(e=>e!=null)}reduce(e,t){const r=this.iterator();let i=t,s=r.next();for(;!s.done;)i===void 0?i=s.value:i=e(i,s.value),s=r.next();return i}reduceRight(e,t){return this.recursiveReduce(this.iterator(),e,t)}recursiveReduce(e,t,r){const i=e.next();if(i.done)return r;const s=this.recursiveReduce(e,t,r);return s===void 0?i.value:t(s,i.value)}find(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(e(r.value))return r.value;r=t.next()}}findIndex(e){const t=this.iterator();let r=0,i=t.next();for(;!i.done;){if(e(i.value))return r;i=t.next(),r++}return-1}includes(e){const t=this.iterator();let r=t.next();for(;!r.done;){if(r.value===e)return!0;r=t.next()}return!1}flatMap(e){return new Z(()=>({this:this.startFn()}),t=>{do{if(t.iterator){const s=t.iterator.next();if(s.done)t.iterator=void 0;else return s}const{done:r,value:i}=this.nextFn(t.this);if(!r){const s=e(i);if(Gr(s))t.iterator=s[Symbol.iterator]();else return{done:!1,value:s}}}while(t.iterator);return Ae})}flat(e){if(e===void 0&&(e=1),e<=0)return this;const t=e>1?this.flat(e-1):this;return new Z(()=>({this:t.startFn()}),r=>{do{if(r.iterator){const a=r.iterator.next();if(a.done)r.iterator=void 0;else return a}const{done:i,value:s}=t.nextFn(r.this);if(!i)if(Gr(s))r.iterator=s[Symbol.iterator]();else return{done:!1,value:s}}while(r.iterator);return Ae})}head(){const t=this.iterator().next();if(!t.done)return t.value}tail(e=1){return new Z(()=>{const t=this.startFn();for(let r=0;r<e;r++)if(this.nextFn(t).done)return t;return t},this.nextFn)}limit(e){return new Z(()=>({size:0,state:this.startFn()}),t=>(t.size++,t.size>e?Ae:this.nextFn(t.state)))}distinct(e){return new Z(()=>({set:new Set,internalState:this.startFn()}),t=>{let r;do if(r=this.nextFn(t.internalState),!r.done){const i=e?e(r.value):r.value;if(!t.set.has(i))return t.set.add(i),r}while(!r.done);return Ae})}exclude(e,t){const r=new Set;for(const i of e){const s=t?t(i):i;r.add(s)}return this.filter(i=>{const s=t?t(i):i;return!r.has(s)})}}function ad(n){return typeof n=="string"?n:typeof n>"u"?"undefined":typeof n.toString=="function"?n.toString():Object.prototype.toString.call(n)}function Gr(n){return!!n&&typeof n[Symbol.iterator]=="function"}const od=new Z(()=>{},()=>Ae),Ae=Object.freeze({done:!0,value:void 0});function ee(...n){if(n.length===1){const e=n[0];if(e instanceof Z)return e;if(Gr(e))return new Z(()=>e[Symbol.iterator](),t=>t.next());if(typeof e.length=="number")return new Z(()=>({index:0}),t=>t.index<e.length?{done:!1,value:e[t.index++]}:Ae)}return n.length>1?new Z(()=>({collIndex:0,arrIndex:0}),e=>{do{if(e.iterator){const t=e.iterator.next();if(!t.done)return t;e.iterator=void 0}if(e.array){if(e.arrIndex<e.array.length)return{done:!1,value:e.array[e.arrIndex++]};e.array=void 0,e.arrIndex=0}if(e.collIndex<n.length){const t=n[e.collIndex++];Gr(t)?e.iterator=t[Symbol.iterator]():t&&typeof t.length=="number"&&(e.array=t)}}while(e.iterator||e.array||e.collIndex<n.length);return Ae}):od}class Ms extends Z{constructor(e,t,r){super(()=>({iterators:r!=null&&r.includeRoot?[[e][Symbol.iterator]()]:[t(e)[Symbol.iterator]()],pruned:!1}),i=>{for(i.pruned&&(i.iterators.pop(),i.pruned=!1);i.iterators.length>0;){const a=i.iterators[i.iterators.length-1].next();if(a.done)i.iterators.pop();else return i.iterators.push(t(a.value)[Symbol.iterator]()),a}return Ae})}iterator(){const e={state:this.startFn(),next:()=>this.nextFn(e.state),prune:()=>{e.state.pruned=!0},[Symbol.iterator]:()=>e};return e}}var Xi;(function(n){function e(s){return s.reduce((a,o)=>a+o,0)}n.sum=e;function t(s){return s.reduce((a,o)=>a*o,0)}n.product=t;function r(s){return s.reduce((a,o)=>Math.min(a,o))}n.min=r;function i(s){return s.reduce((a,o)=>Math.max(a,o))}n.max=i})(Xi||(Xi={}));function Ji(n){return new Ms(n,e=>qn(e)?e.content:[],{includeRoot:!0})}function ld(n,e){for(;n.container;)if(n=n.container,n===e)return!0;return!1}function Qi(n){return{start:{character:n.startColumn-1,line:n.startLine-1},end:{character:n.endColumn,line:n.endLine-1}}}function Ur(n){if(!n)return;const{offset:e,end:t,range:r}=n;return{range:r,offset:e,end:t,length:t-e}}var Ke;(function(n){n[n.Before=0]="Before",n[n.After=1]="After",n[n.OverlapFront=2]="OverlapFront",n[n.OverlapBack=3]="OverlapBack",n[n.Inside=4]="Inside",n[n.Outside=5]="Outside"})(Ke||(Ke={}));function ud(n,e){if(n.end.line<e.start.line||n.end.line===e.start.line&&n.end.character<=e.start.character)return Ke.Before;if(n.start.line>e.end.line||n.start.line===e.end.line&&n.start.character>=e.end.character)return Ke.After;const t=n.start.line>e.start.line||n.start.line===e.start.line&&n.start.character>=e.start.character,r=n.end.line<e.end.line||n.end.line===e.end.line&&n.end.character<=e.end.character;return t&&r?Ke.Inside:t?Ke.OverlapBack:r?Ke.OverlapFront:Ke.Outside}function cd(n,e){return ud(n,e)>Ke.After}const dd=/^[\w\p{L}]$/u;function fd(n,e){if(n){const t=hd(n,!0);if(t&&pa(t,e))return t;if(Cl(n)){const r=n.content.findIndex(i=>!i.hidden);for(let i=r-1;i>=0;i--){const s=n.content[i];if(pa(s,e))return s}}}}function pa(n,e){return Il(n)&&e.includes(n.tokenType.name)}function hd(n,e=!0){for(;n.container;){const t=n.container;let r=t.content.indexOf(n);for(;r>0;){r--;const i=t.content[r];if(e||!i.hidden)return i}n=t}}class $l extends Error{constructor(e,t){super(e?`${t} at ${e.range.start.line}:${e.range.start.character}`:t)}}function Qn(n){throw new Error("Error! The input value was not handled.")}const sr="AbstractRule",ar="AbstractType",Si="Condition",ma="TypeDefinition",xi="ValueLiteral",cn="AbstractElement";function pd(n){return M.isInstance(n,cn)}const or="ArrayLiteral",lr="ArrayType",dn="BooleanLiteral";function md(n){return M.isInstance(n,dn)}const fn="Conjunction";function gd(n){return M.isInstance(n,fn)}const hn="Disjunction";function yd(n){return M.isInstance(n,hn)}const ur="Grammar",Ii="GrammarImport",pn="InferredType";function Nl(n){return M.isInstance(n,pn)}const mn="Interface";function wl(n){return M.isInstance(n,mn)}const Ci="NamedArgument",gn="Negation";function Td(n){return M.isInstance(n,gn)}const cr="NumberLiteral",dr="Parameter",yn="ParameterReference";function Rd(n){return M.isInstance(n,yn)}const Tn="ParserRule";function we(n){return M.isInstance(n,Tn)}const fr="ReferenceType",xr="ReturnType";function Ad(n){return M.isInstance(n,xr)}const Rn="SimpleType";function Ed(n){return M.isInstance(n,Rn)}const hr="StringLiteral",$t="TerminalRule";function Rt(n){return M.isInstance(n,$t)}const An="Type";function _l(n){return M.isInstance(n,An)}const $i="TypeAttribute",pr="UnionType",En="Action";function ci(n){return M.isInstance(n,En)}const vn="Alternatives";function Ll(n){return M.isInstance(n,vn)}const kn="Assignment";function ft(n){return M.isInstance(n,kn)}const Sn="CharacterRange";function vd(n){return M.isInstance(n,Sn)}const xn="CrossReference";function Ds(n){return M.isInstance(n,xn)}const In="EndOfFile";function kd(n){return M.isInstance(n,In)}const Cn="Group";function Fs(n){return M.isInstance(n,Cn)}const $n="Keyword";function ht(n){return M.isInstance(n,$n)}const Nn="NegatedToken";function Sd(n){return M.isInstance(n,Nn)}const wn="RegexToken";function xd(n){return M.isInstance(n,wn)}const _n="RuleCall";function pt(n){return M.isInstance(n,_n)}const Ln="TerminalAlternatives";function Id(n){return M.isInstance(n,Ln)}const On="TerminalGroup";function Cd(n){return M.isInstance(n,On)}const bn="TerminalRuleCall";function $d(n){return M.isInstance(n,bn)}const Pn="UnorderedGroup";function Ol(n){return M.isInstance(n,Pn)}const Mn="UntilToken";function Nd(n){return M.isInstance(n,Mn)}const Dn="Wildcard";function wd(n){return M.isInstance(n,Dn)}class bl extends xl{getAllTypes(){return[cn,sr,ar,En,vn,or,lr,kn,dn,Sn,Si,fn,xn,hn,In,ur,Ii,Cn,pn,mn,$n,Ci,Nn,gn,cr,dr,yn,Tn,fr,wn,xr,_n,Rn,hr,Ln,On,$t,bn,An,$i,ma,pr,Pn,Mn,xi,Dn]}computeIsSubtype(e,t){switch(e){case En:case vn:case kn:case Sn:case xn:case In:case Cn:case $n:case Nn:case wn:case _n:case Ln:case On:case bn:case Pn:case Mn:case Dn:return this.isSubtype(cn,t);case or:case cr:case hr:return this.isSubtype(xi,t);case lr:case fr:case Rn:case pr:return this.isSubtype(ma,t);case dn:return this.isSubtype(Si,t)||this.isSubtype(xi,t);case fn:case hn:case gn:case yn:return this.isSubtype(Si,t);case pn:case mn:case An:return this.isSubtype(ar,t);case Tn:return this.isSubtype(sr,t)||this.isSubtype(ar,t);case $t:return this.isSubtype(sr,t);default:return!1}}getReferenceType(e){const t=`${e.container.$type}:${e.property}`;switch(t){case"Action:type":case"CrossReference:type":case"Interface:superTypes":case"ParserRule:returnType":case"SimpleType:typeRef":return ar;case"Grammar:hiddenTokens":case"ParserRule:hiddenTokens":case"RuleCall:rule":return sr;case"Grammar:usedGrammars":return ur;case"NamedArgument:parameter":case"ParameterReference:parameter":return dr;case"TerminalRuleCall:rule":return $t;default:throw new Error(`${t} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case cn:return{name:cn,properties:[{name:"cardinality"},{name:"lookahead"}]};case or:return{name:or,properties:[{name:"elements",defaultValue:[]}]};case lr:return{name:lr,properties:[{name:"elementType"}]};case dn:return{name:dn,properties:[{name:"true",defaultValue:!1}]};case fn:return{name:fn,properties:[{name:"left"},{name:"right"}]};case hn:return{name:hn,properties:[{name:"left"},{name:"right"}]};case ur:return{name:ur,properties:[{name:"definesHiddenTokens",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"imports",defaultValue:[]},{name:"interfaces",defaultValue:[]},{name:"isDeclared",defaultValue:!1},{name:"name"},{name:"rules",defaultValue:[]},{name:"types",defaultValue:[]},{name:"usedGrammars",defaultValue:[]}]};case Ii:return{name:Ii,properties:[{name:"path"}]};case pn:return{name:pn,properties:[{name:"name"}]};case mn:return{name:mn,properties:[{name:"attributes",defaultValue:[]},{name:"name"},{name:"superTypes",defaultValue:[]}]};case Ci:return{name:Ci,properties:[{name:"calledByName",defaultValue:!1},{name:"parameter"},{name:"value"}]};case gn:return{name:gn,properties:[{name:"value"}]};case cr:return{name:cr,properties:[{name:"value"}]};case dr:return{name:dr,properties:[{name:"name"}]};case yn:return{name:yn,properties:[{name:"parameter"}]};case Tn:return{name:Tn,properties:[{name:"dataType"},{name:"definesHiddenTokens",defaultValue:!1},{name:"definition"},{name:"entry",defaultValue:!1},{name:"fragment",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"inferredType"},{name:"name"},{name:"parameters",defaultValue:[]},{name:"returnType"},{name:"wildcard",defaultValue:!1}]};case fr:return{name:fr,properties:[{name:"referenceType"}]};case xr:return{name:xr,properties:[{name:"name"}]};case Rn:return{name:Rn,properties:[{name:"primitiveType"},{name:"stringType"},{name:"typeRef"}]};case hr:return{name:hr,properties:[{name:"value"}]};case $t:return{name:$t,properties:[{name:"definition"},{name:"fragment",defaultValue:!1},{name:"hidden",defaultValue:!1},{name:"name"},{name:"type"}]};case An:return{name:An,properties:[{name:"name"},{name:"type"}]};case $i:return{name:$i,properties:[{name:"defaultValue"},{name:"isOptional",defaultValue:!1},{name:"name"},{name:"type"}]};case pr:return{name:pr,properties:[{name:"types",defaultValue:[]}]};case En:return{name:En,properties:[{name:"cardinality"},{name:"feature"},{name:"inferredType"},{name:"lookahead"},{name:"operator"},{name:"type"}]};case vn:return{name:vn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case kn:return{name:kn,properties:[{name:"cardinality"},{name:"feature"},{name:"lookahead"},{name:"operator"},{name:"terminal"}]};case Sn:return{name:Sn,properties:[{name:"cardinality"},{name:"left"},{name:"lookahead"},{name:"right"}]};case xn:return{name:xn,properties:[{name:"cardinality"},{name:"deprecatedSyntax",defaultValue:!1},{name:"lookahead"},{name:"terminal"},{name:"type"}]};case In:return{name:In,properties:[{name:"cardinality"},{name:"lookahead"}]};case Cn:return{name:Cn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"guardCondition"},{name:"lookahead"}]};case $n:return{name:$n,properties:[{name:"cardinality"},{name:"lookahead"},{name:"value"}]};case Nn:return{name:Nn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case wn:return{name:wn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"regex"}]};case _n:return{name:_n,properties:[{name:"arguments",defaultValue:[]},{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case Ln:return{name:Ln,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case On:return{name:On,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case bn:return{name:bn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case Pn:return{name:Pn,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Mn:return{name:Mn,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case Dn:return{name:Dn,properties:[{name:"cardinality"},{name:"lookahead"}]};default:return{name:e,properties:[]}}}}const M=new bl;function _d(n){for(const[e,t]of Object.entries(n))e.startsWith("$")||(Array.isArray(t)?t.forEach((r,i)=>{ae(r)&&(r.$container=n,r.$containerProperty=e,r.$containerIndex=i)}):ae(t)&&(t.$container=n,t.$containerProperty=e))}function di(n,e){let t=n;for(;t;){if(e(t))return t;t=t.$container}}function et(n){const t=Zi(n).$document;if(!t)throw new Error("AST node has no document.");return t}function Zi(n){for(;n.$container;)n=n.$container;return n}function Gs(n,e){if(!n)throw new Error("Node must be an AstNode.");const t=e==null?void 0:e.range;return new Z(()=>({keys:Object.keys(n),keyIndex:0,arrayIndex:0}),r=>{for(;r.keyIndex<r.keys.length;){const i=r.keys[r.keyIndex];if(!i.startsWith("$")){const s=n[i];if(ae(s)){if(r.keyIndex++,ga(s,t))return{done:!1,value:s}}else if(Array.isArray(s)){for(;r.arrayIndex<s.length;){const a=r.arrayIndex++,o=s[a];if(ae(o)&&ga(o,t))return{done:!1,value:o}}r.arrayIndex=0}}r.keyIndex++}return Ae})}function Zn(n,e){if(!n)throw new Error("Root node must be an AstNode.");return new Ms(n,t=>Gs(t,e))}function wt(n,e){if(!n)throw new Error("Root node must be an AstNode.");return new Ms(n,t=>Gs(t,e),{includeRoot:!0})}function ga(n,e){var t;if(!e)return!0;const r=(t=n.$cstNode)===null||t===void 0?void 0:t.range;return r?cd(r,e):!1}function Pl(n){return new Z(()=>({keys:Object.keys(n),keyIndex:0,arrayIndex:0}),e=>{for(;e.keyIndex<e.keys.length;){const t=e.keys[e.keyIndex];if(!t.startsWith("$")){const r=n[t];if(Ue(r))return e.keyIndex++,{done:!1,value:{reference:r,container:n,property:t}};if(Array.isArray(r)){for(;e.arrayIndex<r.length;){const i=e.arrayIndex++,s=r[i];if(Ue(s))return{done:!1,value:{reference:s,container:n,property:t,index:i}}}e.arrayIndex=0}}e.keyIndex++}return Ae})}function Ld(n,e){const t=n.getTypeMetaData(e.$type),r=e;for(const i of t.properties)i.defaultValue!==void 0&&r[i.name]===void 0&&(r[i.name]=Ml(i.defaultValue))}function Ml(n){return Array.isArray(n)?[...n.map(Ml)]:n}function w(n){return n.charCodeAt(0)}function Ni(n,e){Array.isArray(n)?n.forEach(function(t){e.push(t)}):e.push(n)}function on(n,e){if(n[e]===!0)throw"duplicate flag "+e;n[e],n[e]=!0}function Ct(n){if(n===void 0)throw Error("Internal Error - Should never get here!");return!0}function Od(){throw Error("Internal Error - Should never get here!")}function ya(n){return n.type==="Character"}const Br=[];for(let n=w("0");n<=w("9");n++)Br.push(n);const Vr=[w("_")].concat(Br);for(let n=w("a");n<=w("z");n++)Vr.push(n);for(let n=w("A");n<=w("Z");n++)Vr.push(n);const Ta=[w(" "),w("\f"),w(`
`),w("\r"),w("	"),w("\v"),w("	"),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w(" "),w("\u2028"),w("\u2029"),w(" "),w(" "),w("　"),w("\uFEFF")],bd=/[0-9a-fA-F]/,mr=/[0-9]/,Pd=/[1-9]/;class Dl{constructor(){this.idx=0,this.input="",this.groupIdx=0}saveState(){return{idx:this.idx,input:this.input,groupIdx:this.groupIdx}}restoreState(e){this.idx=e.idx,this.input=e.input,this.groupIdx=e.groupIdx}pattern(e){this.idx=0,this.input=e,this.groupIdx=0,this.consumeChar("/");const t=this.disjunction();this.consumeChar("/");const r={type:"Flags",loc:{begin:this.idx,end:e.length},global:!1,ignoreCase:!1,multiLine:!1,unicode:!1,sticky:!1};for(;this.isRegExpFlag();)switch(this.popChar()){case"g":on(r,"global");break;case"i":on(r,"ignoreCase");break;case"m":on(r,"multiLine");break;case"u":on(r,"unicode");break;case"y":on(r,"sticky");break}if(this.idx!==this.input.length)throw Error("Redundant input: "+this.input.substring(this.idx));return{type:"Pattern",flags:r,value:t,loc:this.loc(0)}}disjunction(){const e=[],t=this.idx;for(e.push(this.alternative());this.peekChar()==="|";)this.consumeChar("|"),e.push(this.alternative());return{type:"Disjunction",value:e,loc:this.loc(t)}}alternative(){const e=[],t=this.idx;for(;this.isTerm();)e.push(this.term());return{type:"Alternative",value:e,loc:this.loc(t)}}term(){return this.isAssertion()?this.assertion():this.atom()}assertion(){const e=this.idx;switch(this.popChar()){case"^":return{type:"StartAnchor",loc:this.loc(e)};case"$":return{type:"EndAnchor",loc:this.loc(e)};case"\\":switch(this.popChar()){case"b":return{type:"WordBoundary",loc:this.loc(e)};case"B":return{type:"NonWordBoundary",loc:this.loc(e)}}throw Error("Invalid Assertion Escape");case"(":this.consumeChar("?");let t;switch(this.popChar()){case"=":t="Lookahead";break;case"!":t="NegativeLookahead";break}Ct(t);const r=this.disjunction();return this.consumeChar(")"),{type:t,value:r,loc:this.loc(e)}}return Od()}quantifier(e=!1){let t;const r=this.idx;switch(this.popChar()){case"*":t={atLeast:0,atMost:1/0};break;case"+":t={atLeast:1,atMost:1/0};break;case"?":t={atLeast:0,atMost:1};break;case"{":const i=this.integerIncludingZero();switch(this.popChar()){case"}":t={atLeast:i,atMost:i};break;case",":let s;this.isDigit()?(s=this.integerIncludingZero(),t={atLeast:i,atMost:s}):t={atLeast:i,atMost:1/0},this.consumeChar("}");break}if(e===!0&&t===void 0)return;Ct(t);break}if(!(e===!0&&t===void 0)&&Ct(t))return this.peekChar(0)==="?"?(this.consumeChar("?"),t.greedy=!1):t.greedy=!0,t.type="Quantifier",t.loc=this.loc(r),t}atom(){let e;const t=this.idx;switch(this.peekChar()){case".":e=this.dotAll();break;case"\\":e=this.atomEscape();break;case"[":e=this.characterClass();break;case"(":e=this.group();break}if(e===void 0&&this.isPatternCharacter()&&(e=this.patternCharacter()),Ct(e))return e.loc=this.loc(t),this.isQuantifier()&&(e.quantifier=this.quantifier()),e}dotAll(){return this.consumeChar("."),{type:"Set",complement:!0,value:[w(`
`),w("\r"),w("\u2028"),w("\u2029")]}}atomEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return this.decimalEscapeAtom();case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}decimalEscapeAtom(){return{type:"GroupBackReference",value:this.positiveInteger()}}characterClassEscape(){let e,t=!1;switch(this.popChar()){case"d":e=Br;break;case"D":e=Br,t=!0;break;case"s":e=Ta;break;case"S":e=Ta,t=!0;break;case"w":e=Vr;break;case"W":e=Vr,t=!0;break}if(Ct(e))return{type:"Set",value:e,complement:t}}controlEscapeAtom(){let e;switch(this.popChar()){case"f":e=w("\f");break;case"n":e=w(`
`);break;case"r":e=w("\r");break;case"t":e=w("	");break;case"v":e=w("\v");break}if(Ct(e))return{type:"Character",value:e}}controlLetterEscapeAtom(){this.consumeChar("c");const e=this.popChar();if(/[a-zA-Z]/.test(e)===!1)throw Error("Invalid ");return{type:"Character",value:e.toUpperCase().charCodeAt(0)-64}}nulCharacterAtom(){return this.consumeChar("0"),{type:"Character",value:w("\0")}}hexEscapeSequenceAtom(){return this.consumeChar("x"),this.parseHexDigits(2)}regExpUnicodeEscapeSequenceAtom(){return this.consumeChar("u"),this.parseHexDigits(4)}identityEscapeAtom(){const e=this.popChar();return{type:"Character",value:w(e)}}classPatternCharacterAtom(){switch(this.peekChar()){case`
`:case"\r":case"\u2028":case"\u2029":case"\\":case"]":throw Error("TBD");default:const e=this.popChar();return{type:"Character",value:w(e)}}}characterClass(){const e=[];let t=!1;for(this.consumeChar("["),this.peekChar(0)==="^"&&(this.consumeChar("^"),t=!0);this.isClassAtom();){const r=this.classAtom();if(r.type,ya(r)&&this.isRangeDash()){this.consumeChar("-");const i=this.classAtom();if(i.type,ya(i)){if(i.value<r.value)throw Error("Range out of order in character class");e.push({from:r.value,to:i.value})}else Ni(r.value,e),e.push(w("-")),Ni(i.value,e)}else Ni(r.value,e)}return this.consumeChar("]"),{type:"Set",complement:t,value:e}}classAtom(){switch(this.peekChar()){case"]":case`
`:case"\r":case"\u2028":case"\u2029":throw Error("TBD");case"\\":return this.classEscape();default:return this.classPatternCharacterAtom()}}classEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"b":return this.consumeChar("b"),{type:"Character",value:w("\b")};case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}group(){let e=!0;switch(this.consumeChar("("),this.peekChar(0)){case"?":this.consumeChar("?"),this.consumeChar(":"),e=!1;break;default:this.groupIdx++;break}const t=this.disjunction();this.consumeChar(")");const r={type:"Group",capturing:e,value:t};return e&&(r.idx=this.groupIdx),r}positiveInteger(){let e=this.popChar();if(Pd.test(e)===!1)throw Error("Expecting a positive integer");for(;mr.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}integerIncludingZero(){let e=this.popChar();if(mr.test(e)===!1)throw Error("Expecting an integer");for(;mr.test(this.peekChar(0));)e+=this.popChar();return parseInt(e,10)}patternCharacter(){const e=this.popChar();switch(e){case`
`:case"\r":case"\u2028":case"\u2029":case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":throw Error("TBD");default:return{type:"Character",value:w(e)}}}isRegExpFlag(){switch(this.peekChar(0)){case"g":case"i":case"m":case"u":case"y":return!0;default:return!1}}isRangeDash(){return this.peekChar()==="-"&&this.isClassAtom(1)}isDigit(){return mr.test(this.peekChar(0))}isClassAtom(e=0){switch(this.peekChar(e)){case"]":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}isTerm(){return this.isAtom()||this.isAssertion()}isAtom(){if(this.isPatternCharacter())return!0;switch(this.peekChar(0)){case".":case"\\":case"[":case"(":return!0;default:return!1}}isAssertion(){switch(this.peekChar(0)){case"^":case"$":return!0;case"\\":switch(this.peekChar(1)){case"b":case"B":return!0;default:return!1}case"(":return this.peekChar(1)==="?"&&(this.peekChar(2)==="="||this.peekChar(2)==="!");default:return!1}}isQuantifier(){const e=this.saveState();try{return this.quantifier(!0)!==void 0}catch{return!1}finally{this.restoreState(e)}}isPatternCharacter(){switch(this.peekChar()){case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":case"/":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}parseHexDigits(e){let t="";for(let i=0;i<e;i++){const s=this.popChar();if(bd.test(s)===!1)throw Error("Expecting a HexDecimal digits");t+=s}return{type:"Character",value:parseInt(t,16)}}peekChar(e=0){return this.input[this.idx+e]}popChar(){const e=this.peekChar(0);return this.consumeChar(void 0),e}consumeChar(e){if(e!==void 0&&this.input[this.idx]!==e)throw Error("Expected: '"+e+"' but found: '"+this.input[this.idx]+"' at offset: "+this.idx);if(this.idx>=this.input.length)throw Error("Unexpected end of input");this.idx++}loc(e){return{begin:e,end:this.idx}}}class fi{visitChildren(e){for(const t in e){const r=e[t];e.hasOwnProperty(t)&&(r.type!==void 0?this.visit(r):Array.isArray(r)&&r.forEach(i=>{this.visit(i)},this))}}visit(e){switch(e.type){case"Pattern":this.visitPattern(e);break;case"Flags":this.visitFlags(e);break;case"Disjunction":this.visitDisjunction(e);break;case"Alternative":this.visitAlternative(e);break;case"StartAnchor":this.visitStartAnchor(e);break;case"EndAnchor":this.visitEndAnchor(e);break;case"WordBoundary":this.visitWordBoundary(e);break;case"NonWordBoundary":this.visitNonWordBoundary(e);break;case"Lookahead":this.visitLookahead(e);break;case"NegativeLookahead":this.visitNegativeLookahead(e);break;case"Character":this.visitCharacter(e);break;case"Set":this.visitSet(e);break;case"Group":this.visitGroup(e);break;case"GroupBackReference":this.visitGroupBackReference(e);break;case"Quantifier":this.visitQuantifier(e);break}this.visitChildren(e)}visitPattern(e){}visitFlags(e){}visitDisjunction(e){}visitAlternative(e){}visitStartAnchor(e){}visitEndAnchor(e){}visitWordBoundary(e){}visitNonWordBoundary(e){}visitLookahead(e){}visitNegativeLookahead(e){}visitCharacter(e){}visitSet(e){}visitGroup(e){}visitGroupBackReference(e){}visitQuantifier(e){}}const Md=/\r?\n/gm,Dd=new Dl;class Fd extends fi{constructor(){super(...arguments),this.isStarting=!0,this.endRegexpStack=[],this.multiline=!1}get endRegex(){return this.endRegexpStack.join("")}reset(e){this.multiline=!1,this.regex=e,this.startRegexp="",this.isStarting=!0,this.endRegexpStack=[]}visitGroup(e){e.quantifier&&(this.isStarting=!1,this.endRegexpStack=[])}visitCharacter(e){const t=String.fromCharCode(e.value);if(!this.multiline&&t===`
`&&(this.multiline=!0),e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{const r=hi(t);this.endRegexpStack.push(r),this.isStarting&&(this.startRegexp+=r)}}visitSet(e){if(!this.multiline){const t=this.regex.substring(e.loc.begin,e.loc.end),r=new RegExp(t);this.multiline=!!`
`.match(r)}if(e.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{const t=this.regex.substring(e.loc.begin,e.loc.end);this.endRegexpStack.push(t),this.isStarting&&(this.startRegexp+=t)}}visitChildren(e){e.type==="Group"&&e.quantifier||super.visitChildren(e)}}const wi=new Fd;function Gd(n){try{return typeof n=="string"&&(n=new RegExp(n)),n=n.toString(),wi.reset(n),wi.visit(Dd.pattern(n)),wi.multiline}catch{return!1}}const Ud=`\f
\r	\v              \u2028\u2029  　\uFEFF`.split("");function es(n){const e=typeof n=="string"?new RegExp(n):n;return Ud.some(t=>e.test(t))}function hi(n){return n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function Bd(n){return Array.prototype.map.call(n,e=>/\w/.test(e)?`[${e.toLowerCase()}${e.toUpperCase()}]`:hi(e)).join("")}function Vd(n,e){const t=Wd(n),r=e.match(t);return!!r&&r[0].length>0}function Wd(n){typeof n=="string"&&(n=new RegExp(n));const e=n,t=n.source;let r=0;function i(){let s="",a;function o(u){s+=t.substr(r,u),r+=u}function l(u){s+="(?:"+t.substr(r,u)+"|$)",r+=u}for(;r<t.length;)switch(t[r]){case"\\":switch(t[r+1]){case"c":l(3);break;case"x":l(4);break;case"u":e.unicode?t[r+2]==="{"?l(t.indexOf("}",r)-r+1):l(6):l(2);break;case"p":case"P":e.unicode?l(t.indexOf("}",r)-r+1):l(2);break;case"k":l(t.indexOf(">",r)-r+1);break;default:l(2);break}break;case"[":a=/\[(?:\\.|.)*?\]/g,a.lastIndex=r,a=a.exec(t)||[],l(a[0].length);break;case"|":case"^":case"$":case"*":case"+":case"?":o(1);break;case"{":a=/\{\d+,?\d*\}/g,a.lastIndex=r,a=a.exec(t),a?o(a[0].length):l(1);break;case"(":if(t[r+1]==="?")switch(t[r+2]){case":":s+="(?:",r+=3,s+=i()+"|$)";break;case"=":s+="(?=",r+=3,s+=i()+")";break;case"!":a=r,r+=3,i(),s+=t.substr(a,r-a);break;case"<":switch(t[r+3]){case"=":case"!":a=r,r+=4,i(),s+=t.substr(a,r-a);break;default:o(t.indexOf(">",r)-r+1),s+=i()+"|$)";break}break}else o(1),s+=i()+"|$)";break;case")":return++r,s;default:l(1);break}return s}return new RegExp(i(),n.flags)}function jd(n){return n.rules.find(e=>we(e)&&e.entry)}function Kd(n){return n.rules.filter(e=>Rt(e)&&e.hidden)}function Fl(n,e){const t=new Set,r=jd(n);if(!r)return new Set(n.rules);const i=[r].concat(Kd(n));for(const a of i)Gl(a,t,e);const s=new Set;for(const a of n.rules)(t.has(a.name)||Rt(a)&&a.hidden)&&s.add(a);return s}function Gl(n,e,t){e.add(n.name),Zn(n).forEach(r=>{if(pt(r)||t){const i=r.rule.ref;i&&!e.has(i.name)&&Gl(i,e,t)}})}function Hd(n){if(n.terminal)return n.terminal;if(n.type.ref){const e=Bl(n.type.ref);return e==null?void 0:e.terminal}}function zd(n){return n.hidden&&!es(Ws(n))}function qd(n,e){return!n||!e?[]:Us(n,e,n.astNode,!0)}function Ul(n,e,t){if(!n||!e)return;const r=Us(n,e,n.astNode,!0);if(r.length!==0)return t!==void 0?t=Math.max(0,Math.min(t,r.length-1)):t=0,r[t]}function Us(n,e,t,r){if(!r){const i=di(n.grammarSource,ft);if(i&&i.feature===e)return[n]}return qn(n)&&n.astNode===t?n.content.flatMap(i=>Us(i,e,t,!1)):[]}function Yd(n,e,t){if(!n)return;const r=Xd(n,e,n==null?void 0:n.astNode);if(r.length!==0)return t!==void 0?t=Math.max(0,Math.min(t,r.length-1)):t=0,r[t]}function Xd(n,e,t){if(n.astNode!==t)return[];if(ht(n.grammarSource)&&n.grammarSource.value===e)return[n];const r=Ji(n).iterator();let i;const s=[];do if(i=r.next(),!i.done){const a=i.value;a.astNode===t?ht(a.grammarSource)&&a.grammarSource.value===e&&s.push(a):r.prune()}while(!i.done);return s}function Jd(n){var e;const t=n.astNode;for(;t===((e=n.container)===null||e===void 0?void 0:e.astNode);){const r=di(n.grammarSource,ft);if(r)return r;n=n.container}}function Bl(n){let e=n;return Nl(e)&&(ci(e.$container)?e=e.$container.$container:we(e.$container)?e=e.$container:Qn(e.$container)),Vl(n,e,new Map)}function Vl(n,e,t){var r;function i(s,a){let o;return di(s,ft)||(o=Vl(a,a,t)),t.set(n,o),o}if(t.has(n))return t.get(n);t.set(n,void 0);for(const s of Zn(e)){if(ft(s)&&s.feature.toLowerCase()==="name")return t.set(n,s),s;if(pt(s)&&we(s.rule.ref))return i(s,s.rule.ref);if(Ed(s)&&(!((r=s.typeRef)===null||r===void 0)&&r.ref))return i(s,s.typeRef.ref)}}function Wl(n){return jl(n,new Set)}function jl(n,e){if(e.has(n))return!0;e.add(n);for(const t of Zn(n))if(pt(t)){if(!t.rule.ref||we(t.rule.ref)&&!jl(t.rule.ref,e))return!1}else{if(ft(t))return!1;if(ci(t))return!1}return!!n.definition}function Bs(n){if(n.inferredType)return n.inferredType.name;if(n.dataType)return n.dataType;if(n.returnType){const e=n.returnType.ref;if(e){if(we(e))return e.name;if(wl(e)||_l(e))return e.name}}}function Vs(n){var e;if(we(n))return Wl(n)?n.name:(e=Bs(n))!==null&&e!==void 0?e:n.name;if(wl(n)||_l(n)||Ad(n))return n.name;if(ci(n)){const t=Qd(n);if(t)return t}else if(Nl(n))return n.name;throw new Error("Cannot get name of Unknown Type")}function Qd(n){var e;if(n.inferredType)return n.inferredType.name;if(!((e=n.type)===null||e===void 0)&&e.ref)return Vs(n.type.ref)}function Zd(n){var e,t,r;return Rt(n)?(t=(e=n.type)===null||e===void 0?void 0:e.name)!==null&&t!==void 0?t:"string":(r=Bs(n))!==null&&r!==void 0?r:n.name}function Ws(n){const e={s:!1,i:!1,u:!1},t=en(n.definition,e),r=Object.entries(e).filter(([,i])=>i).map(([i])=>i).join("");return new RegExp(t,r)}const js=/[\s\S]/.source;function en(n,e){if(Id(n))return ef(n);if(Cd(n))return tf(n);if(vd(n))return sf(n);if($d(n)){const t=n.rule.ref;if(!t)throw new Error("Missing rule reference.");return ze(en(t.definition),{cardinality:n.cardinality,lookahead:n.lookahead})}else{if(Sd(n))return rf(n);if(Nd(n))return nf(n);if(xd(n)){const t=n.regex.lastIndexOf("/"),r=n.regex.substring(1,t),i=n.regex.substring(t+1);return e&&(e.i=i.includes("i"),e.s=i.includes("s"),e.u=i.includes("u")),ze(r,{cardinality:n.cardinality,lookahead:n.lookahead,wrap:!1})}else{if(wd(n))return ze(js,{cardinality:n.cardinality,lookahead:n.lookahead});throw new Error(`Invalid terminal element: ${n==null?void 0:n.$type}`)}}}function ef(n){return ze(n.elements.map(e=>en(e)).join("|"),{cardinality:n.cardinality,lookahead:n.lookahead})}function tf(n){return ze(n.elements.map(e=>en(e)).join(""),{cardinality:n.cardinality,lookahead:n.lookahead})}function nf(n){return ze(`${js}*?${en(n.terminal)}`,{cardinality:n.cardinality,lookahead:n.lookahead})}function rf(n){return ze(`(?!${en(n.terminal)})${js}*?`,{cardinality:n.cardinality,lookahead:n.lookahead})}function sf(n){return n.right?ze(`[${_i(n.left)}-${_i(n.right)}]`,{cardinality:n.cardinality,lookahead:n.lookahead,wrap:!1}):ze(_i(n.left),{cardinality:n.cardinality,lookahead:n.lookahead,wrap:!1})}function _i(n){return hi(n.value)}function ze(n,e){var t;return(e.wrap!==!1||e.lookahead)&&(n=`(${(t=e.lookahead)!==null&&t!==void 0?t:""}${n})`),e.cardinality?`${n}${e.cardinality}`:n}function af(n){const e=[],t=n.Grammar;for(const r of t.rules)Rt(r)&&zd(r)&&Gd(Ws(r))&&e.push(r.name);return{multilineCommentRules:e,nameRegexp:dd}}function ts(n){console&&console.error&&console.error(`Error: ${n}`)}function Kl(n){console&&console.warn&&console.warn(`Warning: ${n}`)}function Hl(n){const e=new Date().getTime(),t=n();return{time:new Date().getTime()-e,value:t}}function zl(n){function e(){}e.prototype=n;const t=new e;function r(){return typeof t.bar}return r(),r(),n}function of(n){return lf(n)?n.LABEL:n.name}function lf(n){return fe(n.LABEL)&&n.LABEL!==""}class Be{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){this._definition=e}accept(e){e.visit(this),$(this.definition,t=>{t.accept(e)})}}class le extends Be{constructor(e){super([]),this.idx=1,ke(this,Me(e,t=>t!==void 0))}set definition(e){}get definition(){return this.referencedRule!==void 0?this.referencedRule.definition:[]}accept(e){e.visit(this)}}class tn extends Be{constructor(e){super(e.definition),this.orgText="",ke(this,Me(e,t=>t!==void 0))}}class he extends Be{constructor(e){super(e.definition),this.ignoreAmbiguities=!1,ke(this,Me(e,t=>t!==void 0))}}let ne=class extends Be{constructor(e){super(e.definition),this.idx=1,ke(this,Me(e,t=>t!==void 0))}};class xe extends Be{constructor(e){super(e.definition),this.idx=1,ke(this,Me(e,t=>t!==void 0))}}class Ie extends Be{constructor(e){super(e.definition),this.idx=1,ke(this,Me(e,t=>t!==void 0))}}class j extends Be{constructor(e){super(e.definition),this.idx=1,ke(this,Me(e,t=>t!==void 0))}}class me extends Be{constructor(e){super(e.definition),this.idx=1,ke(this,Me(e,t=>t!==void 0))}}class ge extends Be{get definition(){return this._definition}set definition(e){this._definition=e}constructor(e){super(e.definition),this.idx=1,this.ignoreAmbiguities=!1,this.hasPredicates=!1,ke(this,Me(e,t=>t!==void 0))}}class G{constructor(e){this.idx=1,ke(this,Me(e,t=>t!==void 0))}accept(e){e.visit(this)}}function uf(n){return x(n,Ir)}function Ir(n){function e(t){return x(t,Ir)}if(n instanceof le){const t={type:"NonTerminal",name:n.nonTerminalName,idx:n.idx};return fe(n.label)&&(t.label=n.label),t}else{if(n instanceof he)return{type:"Alternative",definition:e(n.definition)};if(n instanceof ne)return{type:"Option",idx:n.idx,definition:e(n.definition)};if(n instanceof xe)return{type:"RepetitionMandatory",idx:n.idx,definition:e(n.definition)};if(n instanceof Ie)return{type:"RepetitionMandatoryWithSeparator",idx:n.idx,separator:Ir(new G({terminalType:n.separator})),definition:e(n.definition)};if(n instanceof me)return{type:"RepetitionWithSeparator",idx:n.idx,separator:Ir(new G({terminalType:n.separator})),definition:e(n.definition)};if(n instanceof j)return{type:"Repetition",idx:n.idx,definition:e(n.definition)};if(n instanceof ge)return{type:"Alternation",idx:n.idx,definition:e(n.definition)};if(n instanceof G){const t={type:"Terminal",name:n.terminalType.name,label:of(n.terminalType),idx:n.idx};fe(n.label)&&(t.terminalLabel=n.label);const r=n.terminalType.PATTERN;return n.terminalType.PATTERN&&(t.pattern=Ye(r)?r.source:r),t}else{if(n instanceof tn)return{type:"Rule",name:n.name,orgText:n.orgText,definition:e(n.definition)};throw Error("non exhaustive match")}}}class nn{visit(e){const t=e;switch(t.constructor){case le:return this.visitNonTerminal(t);case he:return this.visitAlternative(t);case ne:return this.visitOption(t);case xe:return this.visitRepetitionMandatory(t);case Ie:return this.visitRepetitionMandatoryWithSeparator(t);case me:return this.visitRepetitionWithSeparator(t);case j:return this.visitRepetition(t);case ge:return this.visitAlternation(t);case G:return this.visitTerminal(t);case tn:return this.visitRule(t);default:throw Error("non exhaustive match")}}visitNonTerminal(e){}visitAlternative(e){}visitOption(e){}visitRepetition(e){}visitRepetitionMandatory(e){}visitRepetitionMandatoryWithSeparator(e){}visitRepetitionWithSeparator(e){}visitAlternation(e){}visitTerminal(e){}visitRule(e){}}function cf(n){return n instanceof he||n instanceof ne||n instanceof j||n instanceof xe||n instanceof Ie||n instanceof me||n instanceof G||n instanceof tn}function Wr(n,e=[]){return n instanceof ne||n instanceof j||n instanceof me?!0:n instanceof ge?Sl(n.definition,r=>Wr(r,e)):n instanceof le&&ce(e,n)?!1:n instanceof Be?(n instanceof le&&e.push(n),be(n.definition,r=>Wr(r,e))):!1}function df(n){return n instanceof ge}function Ge(n){if(n instanceof le)return"SUBRULE";if(n instanceof ne)return"OPTION";if(n instanceof ge)return"OR";if(n instanceof xe)return"AT_LEAST_ONE";if(n instanceof Ie)return"AT_LEAST_ONE_SEP";if(n instanceof me)return"MANY_SEP";if(n instanceof j)return"MANY";if(n instanceof G)return"CONSUME";throw Error("non exhaustive match")}class pi{walk(e,t=[]){$(e.definition,(r,i)=>{const s=Q(e.definition,i+1);if(r instanceof le)this.walkProdRef(r,s,t);else if(r instanceof G)this.walkTerminal(r,s,t);else if(r instanceof he)this.walkFlat(r,s,t);else if(r instanceof ne)this.walkOption(r,s,t);else if(r instanceof xe)this.walkAtLeastOne(r,s,t);else if(r instanceof Ie)this.walkAtLeastOneSep(r,s,t);else if(r instanceof me)this.walkManySep(r,s,t);else if(r instanceof j)this.walkMany(r,s,t);else if(r instanceof ge)this.walkOr(r,s,t);else throw Error("non exhaustive match")})}walkTerminal(e,t,r){}walkProdRef(e,t,r){}walkFlat(e,t,r){const i=t.concat(r);this.walk(e,i)}walkOption(e,t,r){const i=t.concat(r);this.walk(e,i)}walkAtLeastOne(e,t,r){const i=[new ne({definition:e.definition})].concat(t,r);this.walk(e,i)}walkAtLeastOneSep(e,t,r){const i=Ra(e,t,r);this.walk(e,i)}walkMany(e,t,r){const i=[new ne({definition:e.definition})].concat(t,r);this.walk(e,i)}walkManySep(e,t,r){const i=Ra(e,t,r);this.walk(e,i)}walkOr(e,t,r){const i=t.concat(r);$(e.definition,s=>{const a=new he({definition:[s]});this.walk(a,i)})}}function Ra(n,e,t){return[new ne({definition:[new G({terminalType:n.separator})].concat(n.definition)})].concat(e,t)}function er(n){if(n instanceof le)return er(n.referencedRule);if(n instanceof G)return pf(n);if(cf(n))return ff(n);if(df(n))return hf(n);throw Error("non exhaustive match")}function ff(n){let e=[];const t=n.definition;let r=0,i=t.length>r,s,a=!0;for(;i&&a;)s=t[r],a=Wr(s),e=e.concat(er(s)),r=r+1,i=t.length>r;return Ps(e)}function hf(n){const e=x(n.definition,t=>er(t));return Ps(Ne(e))}function pf(n){return[n.terminalType]}const ql="_~IN~_";class mf extends pi{constructor(e){super(),this.topProd=e,this.follows={}}startWalking(){return this.walk(this.topProd),this.follows}walkTerminal(e,t,r){}walkProdRef(e,t,r){const i=yf(e.referencedRule,e.idx)+this.topProd.name,s=t.concat(r),a=new he({definition:s}),o=er(a);this.follows[i]=o}}function gf(n){const e={};return $(n,t=>{const r=new mf(t).startWalking();ke(e,r)}),e}function yf(n,e){return n.name+e+ql}let Cr={};const Tf=new Dl;function mi(n){const e=n.toString();if(Cr.hasOwnProperty(e))return Cr[e];{const t=Tf.pattern(e);return Cr[e]=t,t}}function Rf(){Cr={}}const Yl="Complement Sets are not supported for first char optimization",jr=`Unable to use "first char" lexer optimizations:
`;function Af(n,e=!1){try{const t=mi(n);return ns(t.value,{},t.flags.ignoreCase)}catch(t){if(t.message===Yl)e&&Kl(`${jr}	Unable to optimize: < ${n.toString()} >
	Complement Sets cannot be automatically optimized.
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.`);else{let r="";e&&(r=`
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details.`),ts(`${jr}
	Failed parsing: < ${n.toString()} >
	Using the @chevrotain/regexp-to-ast library
	Please open an issue at: https://github.com/chevrotain/chevrotain/issues`+r)}}return[]}function ns(n,e,t){switch(n.type){case"Disjunction":for(let i=0;i<n.value.length;i++)ns(n.value[i],e,t);break;case"Alternative":const r=n.value;for(let i=0;i<r.length;i++){const s=r[i];switch(s.type){case"EndAnchor":case"GroupBackReference":case"Lookahead":case"NegativeLookahead":case"StartAnchor":case"WordBoundary":case"NonWordBoundary":continue}const a=s;switch(a.type){case"Character":gr(a.value,e,t);break;case"Set":if(a.complement===!0)throw Error(Yl);$(a.value,l=>{if(typeof l=="number")gr(l,e,t);else{const u=l;if(t===!0)for(let c=u.from;c<=u.to;c++)gr(c,e,t);else{for(let c=u.from;c<=u.to&&c<Gn;c++)gr(c,e,t);if(u.to>=Gn){const c=u.from>=Gn?u.from:Gn,d=u.to,h=tt(c),f=tt(d);for(let m=h;m<=f;m++)e[m]=m}}}});break;case"Group":ns(a.value,e,t);break;default:throw Error("Non Exhaustive Match")}const o=a.quantifier!==void 0&&a.quantifier.atLeast===0;if(a.type==="Group"&&rs(a)===!1||a.type!=="Group"&&o===!1)break}break;default:throw Error("non exhaustive match!")}return z(e)}function gr(n,e,t){const r=tt(n);e[r]=r,t===!0&&Ef(n,e)}function Ef(n,e){const t=String.fromCharCode(n),r=t.toUpperCase();if(r!==t){const i=tt(r.charCodeAt(0));e[i]=i}else{const i=t.toLowerCase();if(i!==t){const s=tt(i.charCodeAt(0));e[s]=s}}}function Aa(n,e){return zt(n.value,t=>{if(typeof t=="number")return ce(e,t);{const r=t;return zt(e,i=>r.from<=i&&i<=r.to)!==void 0}})}function rs(n){const e=n.quantifier;return e&&e.atLeast===0?!0:n.value?te(n.value)?be(n.value,rs):rs(n.value):!1}class vf extends fi{constructor(e){super(),this.targetCharCodes=e,this.found=!1}visitChildren(e){if(this.found!==!0){switch(e.type){case"Lookahead":this.visitLookahead(e);return;case"NegativeLookahead":this.visitNegativeLookahead(e);return}super.visitChildren(e)}}visitCharacter(e){ce(this.targetCharCodes,e.value)&&(this.found=!0)}visitSet(e){e.complement?Aa(e,this.targetCharCodes)===void 0&&(this.found=!0):Aa(e,this.targetCharCodes)!==void 0&&(this.found=!0)}}function Ks(n,e){if(e instanceof RegExp){const t=mi(e),r=new vf(n);return r.visit(t),r.found}else return zt(e,t=>ce(n,t.charCodeAt(0)))!==void 0}const mt="PATTERN",Fn="defaultMode",yr="modes";let Xl=typeof new RegExp("(?:)").sticky=="boolean";function kf(n,e){e=bs(e,{useSticky:Xl,debug:!1,safeMode:!1,positionTracking:"full",lineTerminatorCharacters:["\r",`
`],tracer:(E,R)=>R()});const t=e.tracer;t("initCharCodeToOptimizedIndexMap",()=>{Hf()});let r;t("Reject Lexer.NA",()=>{r=ui(n,E=>E[mt]===de.NA)});let i=!1,s;t("Transform Patterns",()=>{i=!1,s=x(r,E=>{const R=E[mt];if(Ye(R)){const C=R.source;return C.length===1&&C!=="^"&&C!=="$"&&C!=="."&&!R.ignoreCase?C:C.length===2&&C[0]==="\\"&&!ce(["d","D","s","S","t","r","n","t","0","c","b","B","f","v","w","W"],C[1])?C[1]:e.useSticky?va(R):Ea(R)}else{if(Tt(R))return i=!0,{exec:R};if(typeof R=="object")return i=!0,R;if(typeof R=="string"){if(R.length===1)return R;{const C=R.replace(/[\\^$.*+?()[\]{}|]/g,"\\$&"),F=new RegExp(C);return e.useSticky?va(F):Ea(F)}}else throw Error("non exhaustive match")}})});let a,o,l,u,c;t("misc mapping",()=>{a=x(r,E=>E.tokenTypeIdx),o=x(r,E=>{const R=E.GROUP;if(R!==de.SKIPPED){if(fe(R))return R;if(qe(R))return!1;throw Error("non exhaustive match")}}),l=x(r,E=>{const R=E.LONGER_ALT;if(R)return te(R)?x(R,F=>fa(r,F)):[fa(r,R)]}),u=x(r,E=>E.PUSH_MODE),c=x(r,E=>N(E,"POP_MODE"))});let d;t("Line Terminator Handling",()=>{const E=Zl(e.lineTerminatorCharacters);d=x(r,R=>!1),e.positionTracking!=="onlyOffset"&&(d=x(r,R=>N(R,"LINE_BREAKS")?!!R.LINE_BREAKS:Ql(R,E)===!1&&Ks(E,R.PATTERN)))});let h,f,m,g;t("Misc Mapping #2",()=>{h=x(r,Jl),f=x(s,Wf),m=oe(r,(E,R)=>{const C=R.GROUP;return fe(C)&&C!==de.SKIPPED&&(E[C]=[]),E},{}),g=x(s,(E,R)=>({pattern:s[R],longerAlt:l[R],canLineTerminator:d[R],isCustom:h[R],short:f[R],group:o[R],push:u[R],pop:c[R],tokenTypeIdx:a[R],tokenType:r[R]}))});let A=!0,y=[];return e.safeMode||t("First Char Optimization",()=>{y=oe(r,(E,R,C)=>{if(typeof R.PATTERN=="string"){const F=R.PATTERN.charCodeAt(0),ie=tt(F);Li(E,ie,g[C])}else if(te(R.START_CHARS_HINT)){let F;$(R.START_CHARS_HINT,ie=>{const _e=typeof ie=="string"?ie.charCodeAt(0):ie,ye=tt(_e);F!==ye&&(F=ye,Li(E,ye,g[C]))})}else if(Ye(R.PATTERN))if(R.PATTERN.unicode)A=!1,e.ensureOptimizations&&ts(`${jr}	Unable to analyze < ${R.PATTERN.toString()} > pattern.
	The regexp unicode flag is not currently supported by the regexp-to-ast library.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE`);else{const F=Af(R.PATTERN,e.ensureOptimizations);D(F)&&(A=!1),$(F,ie=>{Li(E,ie,g[C])})}else e.ensureOptimizations&&ts(`${jr}	TokenType: <${R.name}> is using a custom token pattern without providing <start_chars_hint> parameter.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE`),A=!1;return E},[])}),{emptyGroups:m,patternIdxToConfig:g,charCodeToPatternIdxToConfig:y,hasCustom:i,canBeOptimized:A}}function Sf(n,e){let t=[];const r=If(n);t=t.concat(r.errors);const i=Cf(r.valid),s=i.valid;return t=t.concat(i.errors),t=t.concat(xf(s)),t=t.concat(Pf(s)),t=t.concat(Mf(s,e)),t=t.concat(Df(s)),t}function xf(n){let e=[];const t=Se(n,r=>Ye(r[mt]));return e=e.concat(Nf(t)),e=e.concat(Lf(t)),e=e.concat(Of(t)),e=e.concat(bf(t)),e=e.concat(wf(t)),e}function If(n){const e=Se(n,i=>!N(i,mt)),t=x(e,i=>({message:"Token Type: ->"+i.name+"<- missing static 'PATTERN' property",type:K.MISSING_PATTERN,tokenTypes:[i]})),r=li(n,e);return{errors:t,valid:r}}function Cf(n){const e=Se(n,i=>{const s=i[mt];return!Ye(s)&&!Tt(s)&&!N(s,"exec")&&!fe(s)}),t=x(e,i=>({message:"Token Type: ->"+i.name+"<- static 'PATTERN' can only be a RegExp, a Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.",type:K.INVALID_PATTERN,tokenTypes:[i]})),r=li(n,e);return{errors:t,valid:r}}const $f=/[^\\][$]/;function Nf(n){class e extends fi{constructor(){super(...arguments),this.found=!1}visitEndAnchor(s){this.found=!0}}const t=Se(n,i=>{const s=i.PATTERN;try{const a=mi(s),o=new e;return o.visit(a),o.found}catch{return $f.test(s.source)}});return x(t,i=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+i.name+`<- static 'PATTERN' cannot contain end of input anchor '$'
	See chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:K.EOI_ANCHOR_FOUND,tokenTypes:[i]}))}function wf(n){const e=Se(n,r=>r.PATTERN.test(""));return x(e,r=>({message:"Token Type: ->"+r.name+"<- static 'PATTERN' must not match an empty string",type:K.EMPTY_MATCH_PATTERN,tokenTypes:[r]}))}const _f=/[^\\[][\^]|^\^/;function Lf(n){class e extends fi{constructor(){super(...arguments),this.found=!1}visitStartAnchor(s){this.found=!0}}const t=Se(n,i=>{const s=i.PATTERN;try{const a=mi(s),o=new e;return o.visit(a),o.found}catch{return _f.test(s.source)}});return x(t,i=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+i.name+`<- static 'PATTERN' cannot contain start of input anchor '^'
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:K.SOI_ANCHOR_FOUND,tokenTypes:[i]}))}function Of(n){const e=Se(n,r=>{const i=r[mt];return i instanceof RegExp&&(i.multiline||i.global)});return x(e,r=>({message:"Token Type: ->"+r.name+"<- static 'PATTERN' may NOT contain global('g') or multiline('m')",type:K.UNSUPPORTED_FLAGS_FOUND,tokenTypes:[r]}))}function bf(n){const e=[];let t=x(n,s=>oe(n,(a,o)=>(s.PATTERN.source===o.PATTERN.source&&!ce(e,o)&&o.PATTERN!==de.NA&&(e.push(o),a.push(o)),a),[]));t=Jn(t);const r=Se(t,s=>s.length>1);return x(r,s=>{const a=x(s,l=>l.name);return{message:`The same RegExp pattern ->${Pe(s).PATTERN}<-has been used in all of the following Token Types: ${a.join(", ")} <-`,type:K.DUPLICATE_PATTERNS_FOUND,tokenTypes:s}})}function Pf(n){const e=Se(n,r=>{if(!N(r,"GROUP"))return!1;const i=r.GROUP;return i!==de.SKIPPED&&i!==de.NA&&!fe(i)});return x(e,r=>({message:"Token Type: ->"+r.name+"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String",type:K.INVALID_GROUP_TYPE_FOUND,tokenTypes:[r]}))}function Mf(n,e){const t=Se(n,i=>i.PUSH_MODE!==void 0&&!ce(e,i.PUSH_MODE));return x(t,i=>({message:`Token Type: ->${i.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${i.PUSH_MODE}<-which does not exist`,type:K.PUSH_MODE_DOES_NOT_EXIST,tokenTypes:[i]}))}function Df(n){const e=[],t=oe(n,(r,i,s)=>{const a=i.PATTERN;return a===de.NA||(fe(a)?r.push({str:a,idx:s,tokenType:i}):Ye(a)&&Gf(a)&&r.push({str:a.source,idx:s,tokenType:i})),r},[]);return $(n,(r,i)=>{$(t,({str:s,idx:a,tokenType:o})=>{if(i<a&&Ff(s,r.PATTERN)){const l=`Token: ->${o.name}<- can never be matched.
Because it appears AFTER the Token Type ->${r.name}<-in the lexer's definition.
See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;e.push({message:l,type:K.UNREACHABLE_PATTERN,tokenTypes:[r,o]})}})}),e}function Ff(n,e){if(Ye(e)){const t=e.exec(n);return t!==null&&t.index===0}else{if(Tt(e))return e(n,0,[],{});if(N(e,"exec"))return e.exec(n,0,[],{});if(typeof e=="string")return e===n;throw Error("non exhaustive match")}}function Gf(n){return zt([".","\\","[","]","|","^","$","(",")","?","*","+","{"],t=>n.source.indexOf(t)!==-1)===void 0}function Ea(n){const e=n.ignoreCase?"i":"";return new RegExp(`^(?:${n.source})`,e)}function va(n){const e=n.ignoreCase?"iy":"y";return new RegExp(`${n.source}`,e)}function Uf(n,e,t){const r=[];return N(n,Fn)||r.push({message:"A MultiMode Lexer cannot be initialized without a <"+Fn+`> property in its definition
`,type:K.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE}),N(n,yr)||r.push({message:"A MultiMode Lexer cannot be initialized without a <"+yr+`> property in its definition
`,type:K.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY}),N(n,yr)&&N(n,Fn)&&!N(n.modes,n.defaultMode)&&r.push({message:`A MultiMode Lexer cannot be initialized with a ${Fn}: <${n.defaultMode}>which does not exist
`,type:K.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST}),N(n,yr)&&$(n.modes,(i,s)=>{$(i,(a,o)=>{if(qe(a))r.push({message:`A Lexer cannot be initialized using an undefined Token Type. Mode:<${s}> at index: <${o}>
`,type:K.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED});else if(N(a,"LONGER_ALT")){const l=te(a.LONGER_ALT)?a.LONGER_ALT:[a.LONGER_ALT];$(l,u=>{!qe(u)&&!ce(i,u)&&r.push({message:`A MultiMode Lexer cannot be initialized with a longer_alt <${u.name}> on token <${a.name}> outside of mode <${s}>
`,type:K.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE})})}})}),r}function Bf(n,e,t){const r=[];let i=!1;const s=Jn(Ne(z(n.modes))),a=ui(s,l=>l[mt]===de.NA),o=Zl(t);return e&&$(a,l=>{const u=Ql(l,o);if(u!==!1){const d={message:Kf(l,u),type:u.issue,tokenType:l};r.push(d)}else N(l,"LINE_BREAKS")?l.LINE_BREAKS===!0&&(i=!0):Ks(o,l.PATTERN)&&(i=!0)}),e&&!i&&r.push({message:`Warning: No LINE_BREAKS Found.
	This Lexer has been defined to track line and column information,
	But none of the Token Types can be identified as matching a line terminator.
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS 
	for details.`,type:K.NO_LINE_BREAKS_FLAGS}),r}function Vf(n){const e={},t=Ht(n);return $(t,r=>{const i=n[r];if(te(i))e[r]=[];else throw Error("non exhaustive match")}),e}function Jl(n){const e=n.PATTERN;if(Ye(e))return!1;if(Tt(e))return!0;if(N(e,"exec"))return!0;if(fe(e))return!1;throw Error("non exhaustive match")}function Wf(n){return fe(n)&&n.length===1?n.charCodeAt(0):!1}const jf={test:function(n){const e=n.length;for(let t=this.lastIndex;t<e;t++){const r=n.charCodeAt(t);if(r===10)return this.lastIndex=t+1,!0;if(r===13)return n.charCodeAt(t+1)===10?this.lastIndex=t+2:this.lastIndex=t+1,!0}return!1},lastIndex:0};function Ql(n,e){if(N(n,"LINE_BREAKS"))return!1;if(Ye(n.PATTERN)){try{Ks(e,n.PATTERN)}catch(t){return{issue:K.IDENTIFY_TERMINATOR,errMsg:t.message}}return!1}else{if(fe(n.PATTERN))return!1;if(Jl(n))return{issue:K.CUSTOM_LINE_BREAK};throw Error("non exhaustive match")}}function Kf(n,e){if(e.issue===K.IDENTIFY_TERMINATOR)return`Warning: unable to identify line terminator usage in pattern.
	The problem is in the <${n.name}> Token Type
	 Root cause: ${e.errMsg}.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR`;if(e.issue===K.CUSTOM_LINE_BREAK)return`Warning: A Custom Token Pattern should specify the <line_breaks> option.
	The problem is in the <${n.name}> Token Type
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK`;throw Error("non exhaustive match")}function Zl(n){return x(n,t=>fe(t)?t.charCodeAt(0):t)}function Li(n,e,t){n[e]===void 0?n[e]=[t]:n[e].push(t)}const Gn=256;let $r=[];function tt(n){return n<Gn?n:$r[n]}function Hf(){if(D($r)){$r=new Array(65536);for(let n=0;n<65536;n++)$r[n]=n>255?255+~~(n/255):n}}function tr(n,e){const t=n.tokenTypeIdx;return t===e.tokenTypeIdx?!0:e.isParent===!0&&e.categoryMatchesMap[t]===!0}function Kr(n,e){return n.tokenTypeIdx===e.tokenTypeIdx}let ka=1;const eu={};function nr(n){const e=zf(n);qf(e),Xf(e),Yf(e),$(e,t=>{t.isParent=t.categoryMatches.length>0})}function zf(n){let e=re(n),t=n,r=!0;for(;r;){t=Jn(Ne(x(t,s=>s.CATEGORIES)));const i=li(t,e);e=e.concat(i),D(i)?r=!1:t=i}return e}function qf(n){$(n,e=>{nu(e)||(eu[ka]=e,e.tokenTypeIdx=ka++),Sa(e)&&!te(e.CATEGORIES)&&(e.CATEGORIES=[e.CATEGORIES]),Sa(e)||(e.CATEGORIES=[]),Jf(e)||(e.categoryMatches=[]),Qf(e)||(e.categoryMatchesMap={})})}function Yf(n){$(n,e=>{e.categoryMatches=[],$(e.categoryMatchesMap,(t,r)=>{e.categoryMatches.push(eu[r].tokenTypeIdx)})})}function Xf(n){$(n,e=>{tu([],e)})}function tu(n,e){$(n,t=>{e.categoryMatchesMap[t.tokenTypeIdx]=!0}),$(e.CATEGORIES,t=>{const r=n.concat(e);ce(r,t)||tu(r,t)})}function nu(n){return N(n,"tokenTypeIdx")}function Sa(n){return N(n,"CATEGORIES")}function Jf(n){return N(n,"categoryMatches")}function Qf(n){return N(n,"categoryMatchesMap")}function Zf(n){return N(n,"tokenTypeIdx")}const is={buildUnableToPopLexerModeMessage(n){return`Unable to pop Lexer Mode after encountering Token ->${n.image}<- The Mode Stack is empty`},buildUnexpectedCharactersMessage(n,e,t,r,i){return`unexpected character: ->${n.charAt(e)}<- at offset: ${e}, skipped ${t} characters.`}};var K;(function(n){n[n.MISSING_PATTERN=0]="MISSING_PATTERN",n[n.INVALID_PATTERN=1]="INVALID_PATTERN",n[n.EOI_ANCHOR_FOUND=2]="EOI_ANCHOR_FOUND",n[n.UNSUPPORTED_FLAGS_FOUND=3]="UNSUPPORTED_FLAGS_FOUND",n[n.DUPLICATE_PATTERNS_FOUND=4]="DUPLICATE_PATTERNS_FOUND",n[n.INVALID_GROUP_TYPE_FOUND=5]="INVALID_GROUP_TYPE_FOUND",n[n.PUSH_MODE_DOES_NOT_EXIST=6]="PUSH_MODE_DOES_NOT_EXIST",n[n.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE=7]="MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE",n[n.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY=8]="MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY",n[n.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST=9]="MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST",n[n.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED=10]="LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED",n[n.SOI_ANCHOR_FOUND=11]="SOI_ANCHOR_FOUND",n[n.EMPTY_MATCH_PATTERN=12]="EMPTY_MATCH_PATTERN",n[n.NO_LINE_BREAKS_FLAGS=13]="NO_LINE_BREAKS_FLAGS",n[n.UNREACHABLE_PATTERN=14]="UNREACHABLE_PATTERN",n[n.IDENTIFY_TERMINATOR=15]="IDENTIFY_TERMINATOR",n[n.CUSTOM_LINE_BREAK=16]="CUSTOM_LINE_BREAK",n[n.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE=17]="MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE"})(K||(K={}));const Un={deferDefinitionErrorsHandling:!1,positionTracking:"full",lineTerminatorsPattern:/\n|\r\n?/g,lineTerminatorCharacters:[`
`,"\r"],ensureOptimizations:!1,safeMode:!1,errorMessageProvider:is,traceInitPerf:!1,skipValidations:!1,recoveryEnabled:!0};Object.freeze(Un);class de{constructor(e,t=Un){if(this.lexerDefinition=e,this.lexerDefinitionErrors=[],this.lexerDefinitionWarning=[],this.patternIdxToConfig={},this.charCodeToPatternIdxToConfig={},this.modes=[],this.emptyGroups={},this.trackStartLines=!0,this.trackEndLines=!0,this.hasCustom=!1,this.canModeBeOptimized={},this.TRACE_INIT=(i,s)=>{if(this.traceInitPerf===!0){this.traceInitIndent++;const a=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${a}--> <${i}>`);const{time:o,value:l}=Hl(s),u=o>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&u(`${a}<-- <${i}> time: ${o}ms`),this.traceInitIndent--,l}else return s()},typeof t=="boolean")throw Error(`The second argument to the Lexer constructor is now an ILexerConfig Object.
a boolean 2nd argument is no longer supported`);this.config=ke({},Un,t);const r=this.config.traceInitPerf;r===!0?(this.traceInitMaxIdent=1/0,this.traceInitPerf=!0):typeof r=="number"&&(this.traceInitMaxIdent=r,this.traceInitPerf=!0),this.traceInitIndent=-1,this.TRACE_INIT("Lexer Constructor",()=>{let i,s=!0;this.TRACE_INIT("Lexer Config handling",()=>{if(this.config.lineTerminatorsPattern===Un.lineTerminatorsPattern)this.config.lineTerminatorsPattern=jf;else if(this.config.lineTerminatorCharacters===Un.lineTerminatorCharacters)throw Error(`Error: Missing <lineTerminatorCharacters> property on the Lexer config.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS`);if(t.safeMode&&t.ensureOptimizations)throw Error('"safeMode" and "ensureOptimizations" flags are mutually exclusive.');this.trackStartLines=/full|onlyStart/i.test(this.config.positionTracking),this.trackEndLines=/full/i.test(this.config.positionTracking),te(e)?i={modes:{defaultMode:re(e)},defaultMode:Fn}:(s=!1,i=re(e))}),this.config.skipValidations===!1&&(this.TRACE_INIT("performRuntimeChecks",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(Uf(i,this.trackStartLines,this.config.lineTerminatorCharacters))}),this.TRACE_INIT("performWarningRuntimeChecks",()=>{this.lexerDefinitionWarning=this.lexerDefinitionWarning.concat(Bf(i,this.trackStartLines,this.config.lineTerminatorCharacters))})),i.modes=i.modes?i.modes:{},$(i.modes,(o,l)=>{i.modes[l]=ui(o,u=>qe(u))});const a=Ht(i.modes);if($(i.modes,(o,l)=>{this.TRACE_INIT(`Mode: <${l}> processing`,()=>{if(this.modes.push(l),this.config.skipValidations===!1&&this.TRACE_INIT("validatePatterns",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(Sf(o,a))}),D(this.lexerDefinitionErrors)){nr(o);let u;this.TRACE_INIT("analyzeTokenTypes",()=>{u=kf(o,{lineTerminatorCharacters:this.config.lineTerminatorCharacters,positionTracking:t.positionTracking,ensureOptimizations:t.ensureOptimizations,safeMode:t.safeMode,tracer:this.TRACE_INIT})}),this.patternIdxToConfig[l]=u.patternIdxToConfig,this.charCodeToPatternIdxToConfig[l]=u.charCodeToPatternIdxToConfig,this.emptyGroups=ke({},this.emptyGroups,u.emptyGroups),this.hasCustom=u.hasCustom||this.hasCustom,this.canModeBeOptimized[l]=u.canBeOptimized}})}),this.defaultMode=i.defaultMode,!D(this.lexerDefinitionErrors)&&!this.config.deferDefinitionErrorsHandling){const l=x(this.lexerDefinitionErrors,u=>u.message).join(`-----------------------
`);throw new Error(`Errors detected in definition of Lexer:
`+l)}$(this.lexerDefinitionWarning,o=>{Kl(o.message)}),this.TRACE_INIT("Choosing sub-methods implementations",()=>{if(Xl?(this.chopInput=da,this.match=this.matchWithTest):(this.updateLastIndex=Y,this.match=this.matchWithExec),s&&(this.handleModes=Y),this.trackStartLines===!1&&(this.computeNewColumn=da),this.trackEndLines===!1&&(this.updateTokenEndLineColumnLocation=Y),/full/i.test(this.config.positionTracking))this.createTokenInstance=this.createFullToken;else if(/onlyStart/i.test(this.config.positionTracking))this.createTokenInstance=this.createStartOnlyToken;else if(/onlyOffset/i.test(this.config.positionTracking))this.createTokenInstance=this.createOffsetOnlyToken;else throw Error(`Invalid <positionTracking> config option: "${this.config.positionTracking}"`);this.hasCustom?(this.addToken=this.addTokenUsingPush,this.handlePayload=this.handlePayloadWithCustom):(this.addToken=this.addTokenUsingMemberAccess,this.handlePayload=this.handlePayloadNoCustom)}),this.TRACE_INIT("Failed Optimization Warnings",()=>{const o=oe(this.canModeBeOptimized,(l,u,c)=>(u===!1&&l.push(c),l),[]);if(t.ensureOptimizations&&!D(o))throw Error(`Lexer Modes: < ${o.join(", ")} > cannot be optimized.
	 Disable the "ensureOptimizations" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.
	 Or inspect the console log for details on how to resolve these issues.`)}),this.TRACE_INIT("clearRegExpParserCache",()=>{Rf()}),this.TRACE_INIT("toFastProperties",()=>{zl(this)})})}tokenize(e,t=this.defaultMode){if(!D(this.lexerDefinitionErrors)){const i=x(this.lexerDefinitionErrors,s=>s.message).join(`-----------------------
`);throw new Error(`Unable to Tokenize because Errors detected in definition of Lexer:
`+i)}return this.tokenizeInternal(e,t)}tokenizeInternal(e,t){let r,i,s,a,o,l,u,c,d,h,f,m,g,A,y;const E=e,R=E.length;let C=0,F=0;const ie=this.hasCustom?0:Math.floor(e.length/10),_e=new Array(ie),ye=[];let Fe=this.trackStartLines?1:void 0,Ce=this.trackStartLines?1:void 0;const S=Vf(this.emptyGroups),T=this.trackStartLines,v=this.config.lineTerminatorsPattern;let I=0,O=[],L=[];const _=[],Te=[];Object.freeze(Te);let q;function W(){return O}function ot(se){const $e=tt(se),xt=L[$e];return xt===void 0?Te:xt}const Tc=se=>{if(_.length===1&&se.tokenType.PUSH_MODE===void 0){const $e=this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(se);ye.push({offset:se.startOffset,line:se.startLine,column:se.startColumn,length:se.image.length,message:$e})}else{_.pop();const $e=qt(_);O=this.patternIdxToConfig[$e],L=this.charCodeToPatternIdxToConfig[$e],I=O.length;const xt=this.canModeBeOptimized[$e]&&this.config.safeMode===!1;L&&xt?q=ot:q=W}};function aa(se){_.push(se),L=this.charCodeToPatternIdxToConfig[se],O=this.patternIdxToConfig[se],I=O.length,I=O.length;const $e=this.canModeBeOptimized[se]&&this.config.safeMode===!1;L&&$e?q=ot:q=W}aa.call(this,t);let Le;const oa=this.config.recoveryEnabled;for(;C<R;){l=null;const se=E.charCodeAt(C),$e=q(se),xt=$e.length;for(r=0;r<xt;r++){Le=$e[r];const Re=Le.pattern;u=null;const Ve=Le.short;if(Ve!==!1?se===Ve&&(l=Re):Le.isCustom===!0?(y=Re.exec(E,C,_e,S),y!==null?(l=y[0],y.payload!==void 0&&(u=y.payload)):l=null):(this.updateLastIndex(Re,C),l=this.match(Re,e,C)),l!==null){if(o=Le.longerAlt,o!==void 0){const Je=o.length;for(s=0;s<Je;s++){const We=O[o[s]],lt=We.pattern;if(c=null,We.isCustom===!0?(y=lt.exec(E,C,_e,S),y!==null?(a=y[0],y.payload!==void 0&&(c=y.payload)):a=null):(this.updateLastIndex(lt,C),a=this.match(lt,e,C)),a&&a.length>l.length){l=a,u=c,Le=We;break}}}break}}if(l!==null){if(d=l.length,h=Le.group,h!==void 0&&(f=Le.tokenTypeIdx,m=this.createTokenInstance(l,C,f,Le.tokenType,Fe,Ce,d),this.handlePayload(m,u),h===!1?F=this.addToken(_e,F,m):S[h].push(m)),e=this.chopInput(e,d),C=C+d,Ce=this.computeNewColumn(Ce,d),T===!0&&Le.canLineTerminator===!0){let Re=0,Ve,Je;v.lastIndex=0;do Ve=v.test(l),Ve===!0&&(Je=v.lastIndex-1,Re++);while(Ve===!0);Re!==0&&(Fe=Fe+Re,Ce=d-Je,this.updateTokenEndLineColumnLocation(m,h,Je,Re,Fe,Ce,d))}this.handleModes(Le,Tc,aa,m)}else{const Re=C,Ve=Fe,Je=Ce;let We=oa===!1;for(;We===!1&&C<R;)for(e=this.chopInput(e,1),C++,i=0;i<I;i++){const lt=O[i],ki=lt.pattern,la=lt.short;if(la!==!1?E.charCodeAt(C)===la&&(We=!0):lt.isCustom===!0?We=ki.exec(E,C,_e,S)!==null:(this.updateLastIndex(ki,C),We=ki.exec(e)!==null),We===!0)break}if(g=C-Re,Ce=this.computeNewColumn(Ce,g),A=this.config.errorMessageProvider.buildUnexpectedCharactersMessage(E,Re,g,Ve,Je),ye.push({offset:Re,line:Ve,column:Je,length:g,message:A}),oa===!1)break}}return this.hasCustom||(_e.length=F),{tokens:_e,groups:S,errors:ye}}handleModes(e,t,r,i){if(e.pop===!0){const s=e.push;t(i),s!==void 0&&r.call(this,s)}else e.push!==void 0&&r.call(this,e.push)}chopInput(e,t){return e.substring(t)}updateLastIndex(e,t){e.lastIndex=t}updateTokenEndLineColumnLocation(e,t,r,i,s,a,o){let l,u;t!==void 0&&(l=r===o-1,u=l?-1:0,i===1&&l===!0||(e.endLine=s+u,e.endColumn=a-1+-u))}computeNewColumn(e,t){return e+t}createOffsetOnlyToken(e,t,r,i){return{image:e,startOffset:t,tokenTypeIdx:r,tokenType:i}}createStartOnlyToken(e,t,r,i,s,a){return{image:e,startOffset:t,startLine:s,startColumn:a,tokenTypeIdx:r,tokenType:i}}createFullToken(e,t,r,i,s,a,o){return{image:e,startOffset:t,endOffset:t+o-1,startLine:s,endLine:s,startColumn:a,endColumn:a+o-1,tokenTypeIdx:r,tokenType:i}}addTokenUsingPush(e,t,r){return e.push(r),t}addTokenUsingMemberAccess(e,t,r){return e[t]=r,t++,t}handlePayloadNoCustom(e,t){}handlePayloadWithCustom(e,t){t!==null&&(e.payload=t)}matchWithTest(e,t,r){return e.test(t)===!0?t.substring(r,e.lastIndex):null}matchWithExec(e,t){const r=e.exec(t);return r!==null?r[0]:null}}de.SKIPPED="This marks a skipped Token pattern, this means each token identified by it willbe consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.";de.NA=/NOT_APPLICABLE/;function _t(n){return ru(n)?n.LABEL:n.name}function ru(n){return fe(n.LABEL)&&n.LABEL!==""}const eh="parent",xa="categories",Ia="label",Ca="group",$a="push_mode",Na="pop_mode",wa="longer_alt",_a="line_breaks",La="start_chars_hint";function iu(n){return th(n)}function th(n){const e=n.pattern,t={};if(t.name=n.name,qe(e)||(t.PATTERN=e),N(n,eh))throw`The parent property is no longer supported.
See: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.`;return N(n,xa)&&(t.CATEGORIES=n[xa]),nr([t]),N(n,Ia)&&(t.LABEL=n[Ia]),N(n,Ca)&&(t.GROUP=n[Ca]),N(n,Na)&&(t.POP_MODE=n[Na]),N(n,$a)&&(t.PUSH_MODE=n[$a]),N(n,wa)&&(t.LONGER_ALT=n[wa]),N(n,_a)&&(t.LINE_BREAKS=n[_a]),N(n,La)&&(t.START_CHARS_HINT=n[La]),t}const nt=iu({name:"EOF",pattern:de.NA});nr([nt]);function Hs(n,e,t,r,i,s,a,o){return{image:e,startOffset:t,endOffset:r,startLine:i,endLine:s,startColumn:a,endColumn:o,tokenTypeIdx:n.tokenTypeIdx,tokenType:n}}function su(n,e){return tr(n,e)}const Nt={buildMismatchTokenMessage({expected:n,actual:e,previous:t,ruleName:r}){return`Expecting ${ru(n)?`--> ${_t(n)} <--`:`token of type --> ${n.name} <--`} but found --> '${e.image}' <--`},buildNotAllInputParsedMessage({firstRedundant:n,ruleName:e}){return"Redundant input, expecting EOF but found: "+n.image},buildNoViableAltMessage({expectedPathsPerAlt:n,actual:e,previous:t,customUserDescription:r,ruleName:i}){const s="Expecting: ",o=`
but found: '`+Pe(e).image+"'";if(r)return s+r+o;{const l=oe(n,(h,f)=>h.concat(f),[]),u=x(l,h=>`[${x(h,f=>_t(f)).join(", ")}]`),d=`one of these possible Token sequences:
${x(u,(h,f)=>`  ${f+1}. ${h}`).join(`
`)}`;return s+d+o}},buildEarlyExitMessage({expectedIterationPaths:n,actual:e,customUserDescription:t,ruleName:r}){const i="Expecting: ",a=`
but found: '`+Pe(e).image+"'";if(t)return i+t+a;{const l=`expecting at least one iteration which starts with one of these possible Token sequences::
  <${x(n,u=>`[${x(u,c=>_t(c)).join(",")}]`).join(" ,")}>`;return i+l+a}}};Object.freeze(Nt);const nh={buildRuleNotFoundError(n,e){return"Invalid grammar, reference to a rule which is not defined: ->"+e.nonTerminalName+`<-
inside top level rule: ->`+n.name+"<-"}},dt={buildDuplicateFoundError(n,e){function t(c){return c instanceof G?c.terminalType.name:c instanceof le?c.nonTerminalName:""}const r=n.name,i=Pe(e),s=i.idx,a=Ge(i),o=t(i),l=s>0;let u=`->${a}${l?s:""}<- ${o?`with argument: ->${o}<-`:""}
                  appears more than once (${e.length} times) in the top level rule: ->${r}<-.                  
                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES 
                  `;return u=u.replace(/[ \t]+/g," "),u=u.replace(/\s\s+/g,`
`),u},buildNamespaceConflictError(n){return`Namespace conflict found in grammar.
The grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${n.name}>.
To resolve this make sure each Terminal and Non-Terminal names are unique
This is easy to accomplish by using the convention that Terminal names start with an uppercase letter
and Non-Terminal names start with a lower case letter.`},buildAlternationPrefixAmbiguityError(n){const e=x(n.prefixPath,i=>_t(i)).join(", "),t=n.alternation.idx===0?"":n.alternation.idx;return`Ambiguous alternatives: <${n.ambiguityIndices.join(" ,")}> due to common lookahead prefix
in <OR${t}> inside <${n.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX
For Further details.`},buildAlternationAmbiguityError(n){const e=x(n.prefixPath,i=>_t(i)).join(", "),t=n.alternation.idx===0?"":n.alternation.idx;let r=`Ambiguous Alternatives Detected: <${n.ambiguityIndices.join(" ,")}> in <OR${t}> inside <${n.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return r=r+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,r},buildEmptyRepetitionError(n){let e=Ge(n.repetition);return n.repetition.idx!==0&&(e+=n.repetition.idx),`The repetition <${e}> within Rule <${n.topLevelRule.name}> can never consume any tokens.
This could lead to an infinite loop.`},buildTokenNameError(n){return"deprecated"},buildEmptyAlternationError(n){return`Ambiguous empty alternative: <${n.emptyChoiceIdx+1}> in <OR${n.alternation.idx}> inside <${n.topLevelRule.name}> Rule.
Only the last alternative may be an empty alternative.`},buildTooManyAlternativesError(n){return`An Alternation cannot have more than 256 alternatives:
<OR${n.alternation.idx}> inside <${n.topLevelRule.name}> Rule.
 has ${n.alternation.definition.length+1} alternatives.`},buildLeftRecursionError(n){const e=n.topLevelRule.name,t=x(n.leftRecursionPath,s=>s.name),r=`${e} --> ${t.concat([e]).join(" --> ")}`;return`Left Recursion found in grammar.
rule: <${e}> can be invoked from itself (directly or indirectly)
without consuming any Tokens. The grammar path that causes this is: 
 ${r}
 To fix this refactor your grammar to remove the left recursion.
see: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`},buildInvalidRuleNameError(n){return"deprecated"},buildDuplicateRuleNameError(n){let e;return n.topLevelRule instanceof tn?e=n.topLevelRule.name:e=n.topLevelRule,`Duplicate definition, rule: ->${e}<- is already defined in the grammar: ->${n.grammarName}<-`}};function rh(n,e){const t=new ih(n,e);return t.resolveRefs(),t.errors}class ih extends nn{constructor(e,t){super(),this.nameToTopRule=e,this.errMsgProvider=t,this.errors=[]}resolveRefs(){$(z(this.nameToTopRule),e=>{this.currTopLevel=e,e.accept(this)})}visitNonTerminal(e){const t=this.nameToTopRule[e.nonTerminalName];if(t)e.referencedRule=t;else{const r=this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel,e);this.errors.push({message:r,type:ue.UNRESOLVED_SUBRULE_REF,ruleName:this.currTopLevel.name,unresolvedRefName:e.nonTerminalName})}}}class sh extends pi{constructor(e,t){super(),this.topProd=e,this.path=t,this.possibleTokTypes=[],this.nextProductionName="",this.nextProductionOccurrence=0,this.found=!1,this.isAtEndOfPath=!1}startWalking(){if(this.found=!1,this.path.ruleStack[0]!==this.topProd.name)throw Error("The path does not start with the walker's top Rule!");return this.ruleStack=re(this.path.ruleStack).reverse(),this.occurrenceStack=re(this.path.occurrenceStack).reverse(),this.ruleStack.pop(),this.occurrenceStack.pop(),this.updateExpectedNext(),this.walk(this.topProd),this.possibleTokTypes}walk(e,t=[]){this.found||super.walk(e,t)}walkProdRef(e,t,r){if(e.referencedRule.name===this.nextProductionName&&e.idx===this.nextProductionOccurrence){const i=t.concat(r);this.updateExpectedNext(),this.walk(e.referencedRule,i)}}updateExpectedNext(){D(this.ruleStack)?(this.nextProductionName="",this.nextProductionOccurrence=0,this.isAtEndOfPath=!0):(this.nextProductionName=this.ruleStack.pop(),this.nextProductionOccurrence=this.occurrenceStack.pop())}}class ah extends sh{constructor(e,t){super(e,t),this.path=t,this.nextTerminalName="",this.nextTerminalOccurrence=0,this.nextTerminalName=this.path.lastTok.name,this.nextTerminalOccurrence=this.path.lastTokOccurrence}walkTerminal(e,t,r){if(this.isAtEndOfPath&&e.terminalType.name===this.nextTerminalName&&e.idx===this.nextTerminalOccurrence&&!this.found){const i=t.concat(r),s=new he({definition:i});this.possibleTokTypes=er(s),this.found=!0}}}class gi extends pi{constructor(e,t){super(),this.topRule=e,this.occurrence=t,this.result={token:void 0,occurrence:void 0,isEndOfRule:void 0}}startWalking(){return this.walk(this.topRule),this.result}}class oh extends gi{walkMany(e,t,r){if(e.idx===this.occurrence){const i=Pe(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof G&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkMany(e,t,r)}}class Oa extends gi{walkManySep(e,t,r){if(e.idx===this.occurrence){const i=Pe(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof G&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkManySep(e,t,r)}}class lh extends gi{walkAtLeastOne(e,t,r){if(e.idx===this.occurrence){const i=Pe(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof G&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOne(e,t,r)}}class ba extends gi{walkAtLeastOneSep(e,t,r){if(e.idx===this.occurrence){const i=Pe(t.concat(r));this.result.isEndOfRule=i===void 0,i instanceof G&&(this.result.token=i.terminalType,this.result.occurrence=i.idx)}else super.walkAtLeastOneSep(e,t,r)}}function ss(n,e,t=[]){t=re(t);let r=[],i=0;function s(o){return o.concat(Q(n,i+1))}function a(o){const l=ss(s(o),e,t);return r.concat(l)}for(;t.length<e&&i<n.length;){const o=n[i];if(o instanceof he)return a(o.definition);if(o instanceof le)return a(o.definition);if(o instanceof ne)r=a(o.definition);else if(o instanceof xe){const l=o.definition.concat([new j({definition:o.definition})]);return a(l)}else if(o instanceof Ie){const l=[new he({definition:o.definition}),new j({definition:[new G({terminalType:o.separator})].concat(o.definition)})];return a(l)}else if(o instanceof me){const l=o.definition.concat([new j({definition:[new G({terminalType:o.separator})].concat(o.definition)})]);r=a(l)}else if(o instanceof j){const l=o.definition.concat([new j({definition:o.definition})]);r=a(l)}else{if(o instanceof ge)return $(o.definition,l=>{D(l.definition)===!1&&(r=a(l.definition))}),r;if(o instanceof G)t.push(o.terminalType);else throw Error("non exhaustive match")}i++}return r.push({partialPath:t,suffixDef:Q(n,i)}),r}function au(n,e,t,r){const i="EXIT_NONE_TERMINAL",s=[i],a="EXIT_ALTERNATIVE";let o=!1;const l=e.length,u=l-r-1,c=[],d=[];for(d.push({idx:-1,def:n,ruleStack:[],occurrenceStack:[]});!D(d);){const h=d.pop();if(h===a){o&&qt(d).idx<=u&&d.pop();continue}const f=h.def,m=h.idx,g=h.ruleStack,A=h.occurrenceStack;if(D(f))continue;const y=f[0];if(y===i){const E={idx:m,def:Q(f),ruleStack:zn(g),occurrenceStack:zn(A)};d.push(E)}else if(y instanceof G)if(m<l-1){const E=m+1,R=e[E];if(t(R,y.terminalType)){const C={idx:E,def:Q(f),ruleStack:g,occurrenceStack:A};d.push(C)}}else if(m===l-1)c.push({nextTokenType:y.terminalType,nextTokenOccurrence:y.idx,ruleStack:g,occurrenceStack:A}),o=!0;else throw Error("non exhaustive match");else if(y instanceof le){const E=re(g);E.push(y.nonTerminalName);const R=re(A);R.push(y.idx);const C={idx:m,def:y.definition.concat(s,Q(f)),ruleStack:E,occurrenceStack:R};d.push(C)}else if(y instanceof ne){const E={idx:m,def:Q(f),ruleStack:g,occurrenceStack:A};d.push(E),d.push(a);const R={idx:m,def:y.definition.concat(Q(f)),ruleStack:g,occurrenceStack:A};d.push(R)}else if(y instanceof xe){const E=new j({definition:y.definition,idx:y.idx}),R=y.definition.concat([E],Q(f)),C={idx:m,def:R,ruleStack:g,occurrenceStack:A};d.push(C)}else if(y instanceof Ie){const E=new G({terminalType:y.separator}),R=new j({definition:[E].concat(y.definition),idx:y.idx}),C=y.definition.concat([R],Q(f)),F={idx:m,def:C,ruleStack:g,occurrenceStack:A};d.push(F)}else if(y instanceof me){const E={idx:m,def:Q(f),ruleStack:g,occurrenceStack:A};d.push(E),d.push(a);const R=new G({terminalType:y.separator}),C=new j({definition:[R].concat(y.definition),idx:y.idx}),F=y.definition.concat([C],Q(f)),ie={idx:m,def:F,ruleStack:g,occurrenceStack:A};d.push(ie)}else if(y instanceof j){const E={idx:m,def:Q(f),ruleStack:g,occurrenceStack:A};d.push(E),d.push(a);const R=new j({definition:y.definition,idx:y.idx}),C=y.definition.concat([R],Q(f)),F={idx:m,def:C,ruleStack:g,occurrenceStack:A};d.push(F)}else if(y instanceof ge)for(let E=y.definition.length-1;E>=0;E--){const R=y.definition[E],C={idx:m,def:R.definition.concat(Q(f)),ruleStack:g,occurrenceStack:A};d.push(C),d.push(a)}else if(y instanceof he)d.push({idx:m,def:y.definition.concat(Q(f)),ruleStack:g,occurrenceStack:A});else if(y instanceof tn)d.push(uh(y,m,g,A));else throw Error("non exhaustive match")}return c}function uh(n,e,t,r){const i=re(t);i.push(n.name);const s=re(r);return s.push(1),{idx:e,def:n.definition,ruleStack:i,occurrenceStack:s}}var B;(function(n){n[n.OPTION=0]="OPTION",n[n.REPETITION=1]="REPETITION",n[n.REPETITION_MANDATORY=2]="REPETITION_MANDATORY",n[n.REPETITION_MANDATORY_WITH_SEPARATOR=3]="REPETITION_MANDATORY_WITH_SEPARATOR",n[n.REPETITION_WITH_SEPARATOR=4]="REPETITION_WITH_SEPARATOR",n[n.ALTERNATION=5]="ALTERNATION"})(B||(B={}));function zs(n){if(n instanceof ne||n==="Option")return B.OPTION;if(n instanceof j||n==="Repetition")return B.REPETITION;if(n instanceof xe||n==="RepetitionMandatory")return B.REPETITION_MANDATORY;if(n instanceof Ie||n==="RepetitionMandatoryWithSeparator")return B.REPETITION_MANDATORY_WITH_SEPARATOR;if(n instanceof me||n==="RepetitionWithSeparator")return B.REPETITION_WITH_SEPARATOR;if(n instanceof ge||n==="Alternation")return B.ALTERNATION;throw Error("non exhaustive match")}function Pa(n){const{occurrence:e,rule:t,prodType:r,maxLookahead:i}=n,s=zs(r);return s===B.ALTERNATION?yi(e,t,i):Ti(e,t,s,i)}function ch(n,e,t,r,i,s){const a=yi(n,e,t),o=uu(a)?Kr:tr;return s(a,r,o,i)}function dh(n,e,t,r,i,s){const a=Ti(n,e,i,t),o=uu(a)?Kr:tr;return s(a[0],o,r)}function fh(n,e,t,r){const i=n.length,s=be(n,a=>be(a,o=>o.length===1));if(e)return function(a){const o=x(a,l=>l.GATE);for(let l=0;l<i;l++){const u=n[l],c=u.length,d=o[l];if(!(d!==void 0&&d.call(this)===!1))e:for(let h=0;h<c;h++){const f=u[h],m=f.length;for(let g=0;g<m;g++){const A=this.LA(g+1);if(t(A,f[g])===!1)continue e}return l}}};if(s&&!r){const a=x(n,l=>Ne(l)),o=oe(a,(l,u,c)=>($(u,d=>{N(l,d.tokenTypeIdx)||(l[d.tokenTypeIdx]=c),$(d.categoryMatches,h=>{N(l,h)||(l[h]=c)})}),l),{});return function(){const l=this.LA(1);return o[l.tokenTypeIdx]}}else return function(){for(let a=0;a<i;a++){const o=n[a],l=o.length;e:for(let u=0;u<l;u++){const c=o[u],d=c.length;for(let h=0;h<d;h++){const f=this.LA(h+1);if(t(f,c[h])===!1)continue e}return a}}}}function hh(n,e,t){const r=be(n,s=>s.length===1),i=n.length;if(r&&!t){const s=Ne(n);if(s.length===1&&D(s[0].categoryMatches)){const o=s[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===o}}else{const a=oe(s,(o,l,u)=>(o[l.tokenTypeIdx]=!0,$(l.categoryMatches,c=>{o[c]=!0}),o),[]);return function(){const o=this.LA(1);return a[o.tokenTypeIdx]===!0}}}else return function(){e:for(let s=0;s<i;s++){const a=n[s],o=a.length;for(let l=0;l<o;l++){const u=this.LA(l+1);if(e(u,a[l])===!1)continue e}return!0}return!1}}class ph extends pi{constructor(e,t,r){super(),this.topProd=e,this.targetOccurrence=t,this.targetProdType=r}startWalking(){return this.walk(this.topProd),this.restDef}checkIsTarget(e,t,r,i){return e.idx===this.targetOccurrence&&this.targetProdType===t?(this.restDef=r.concat(i),!0):!1}walkOption(e,t,r){this.checkIsTarget(e,B.OPTION,t,r)||super.walkOption(e,t,r)}walkAtLeastOne(e,t,r){this.checkIsTarget(e,B.REPETITION_MANDATORY,t,r)||super.walkOption(e,t,r)}walkAtLeastOneSep(e,t,r){this.checkIsTarget(e,B.REPETITION_MANDATORY_WITH_SEPARATOR,t,r)||super.walkOption(e,t,r)}walkMany(e,t,r){this.checkIsTarget(e,B.REPETITION,t,r)||super.walkOption(e,t,r)}walkManySep(e,t,r){this.checkIsTarget(e,B.REPETITION_WITH_SEPARATOR,t,r)||super.walkOption(e,t,r)}}class ou extends nn{constructor(e,t,r){super(),this.targetOccurrence=e,this.targetProdType=t,this.targetRef=r,this.result=[]}checkIsTarget(e,t){e.idx===this.targetOccurrence&&this.targetProdType===t&&(this.targetRef===void 0||e===this.targetRef)&&(this.result=e.definition)}visitOption(e){this.checkIsTarget(e,B.OPTION)}visitRepetition(e){this.checkIsTarget(e,B.REPETITION)}visitRepetitionMandatory(e){this.checkIsTarget(e,B.REPETITION_MANDATORY)}visitRepetitionMandatoryWithSeparator(e){this.checkIsTarget(e,B.REPETITION_MANDATORY_WITH_SEPARATOR)}visitRepetitionWithSeparator(e){this.checkIsTarget(e,B.REPETITION_WITH_SEPARATOR)}visitAlternation(e){this.checkIsTarget(e,B.ALTERNATION)}}function Ma(n){const e=new Array(n);for(let t=0;t<n;t++)e[t]=[];return e}function Oi(n){let e=[""];for(let t=0;t<n.length;t++){const r=n[t],i=[];for(let s=0;s<e.length;s++){const a=e[s];i.push(a+"_"+r.tokenTypeIdx);for(let o=0;o<r.categoryMatches.length;o++){const l="_"+r.categoryMatches[o];i.push(a+l)}}e=i}return e}function mh(n,e,t){for(let r=0;r<n.length;r++){if(r===t)continue;const i=n[r];for(let s=0;s<e.length;s++){const a=e[s];if(i[a]===!0)return!1}}return!0}function lu(n,e){const t=x(n,a=>ss([a],1)),r=Ma(t.length),i=x(t,a=>{const o={};return $(a,l=>{const u=Oi(l.partialPath);$(u,c=>{o[c]=!0})}),o});let s=t;for(let a=1;a<=e;a++){const o=s;s=Ma(o.length);for(let l=0;l<o.length;l++){const u=o[l];for(let c=0;c<u.length;c++){const d=u[c].partialPath,h=u[c].suffixDef,f=Oi(d);if(mh(i,f,l)||D(h)||d.length===e){const g=r[l];if(as(g,d)===!1){g.push(d);for(let A=0;A<f.length;A++){const y=f[A];i[l][y]=!0}}}else{const g=ss(h,a+1,d);s[l]=s[l].concat(g),$(g,A=>{const y=Oi(A.partialPath);$(y,E=>{i[l][E]=!0})})}}}}return r}function yi(n,e,t,r){const i=new ou(n,B.ALTERNATION,r);return e.accept(i),lu(i.result,t)}function Ti(n,e,t,r){const i=new ou(n,t);e.accept(i);const s=i.result,o=new ph(e,n,t).startWalking(),l=new he({definition:s}),u=new he({definition:o});return lu([l,u],r)}function as(n,e){e:for(let t=0;t<n.length;t++){const r=n[t];if(r.length===e.length){for(let i=0;i<r.length;i++){const s=e[i],a=r[i];if((s===a||a.categoryMatchesMap[s.tokenTypeIdx]!==void 0)===!1)continue e}return!0}}return!1}function gh(n,e){return n.length<e.length&&be(n,(t,r)=>{const i=e[r];return t===i||i.categoryMatchesMap[t.tokenTypeIdx]})}function uu(n){return be(n,e=>be(e,t=>be(t,r=>D(r.categoryMatches))))}function yh(n){const e=n.lookaheadStrategy.validate({rules:n.rules,tokenTypes:n.tokenTypes,grammarName:n.grammarName});return x(e,t=>Object.assign({type:ue.CUSTOM_LOOKAHEAD_VALIDATION},t))}function Th(n,e,t,r){const i=ve(n,l=>Rh(l,t)),s=_h(n,e,t),a=ve(n,l=>Ch(l,t)),o=ve(n,l=>vh(l,n,r,t));return i.concat(s,a,o)}function Rh(n,e){const t=new Eh;n.accept(t);const r=t.allProductions,i=Xc(r,Ah),s=Me(i,o=>o.length>1);return x(z(s),o=>{const l=Pe(o),u=e.buildDuplicateFoundError(n,o),c=Ge(l),d={message:u,type:ue.DUPLICATE_PRODUCTIONS,ruleName:n.name,dslName:c,occurrence:l.idx},h=cu(l);return h&&(d.parameter=h),d})}function Ah(n){return`${Ge(n)}_#_${n.idx}_#_${cu(n)}`}function cu(n){return n instanceof G?n.terminalType.name:n instanceof le?n.nonTerminalName:""}class Eh extends nn{constructor(){super(...arguments),this.allProductions=[]}visitNonTerminal(e){this.allProductions.push(e)}visitOption(e){this.allProductions.push(e)}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}visitAlternation(e){this.allProductions.push(e)}visitTerminal(e){this.allProductions.push(e)}}function vh(n,e,t,r){const i=[];if(oe(e,(a,o)=>o.name===n.name?a+1:a,0)>1){const a=r.buildDuplicateRuleNameError({topLevelRule:n,grammarName:t});i.push({message:a,type:ue.DUPLICATE_RULE_NAME,ruleName:n.name})}return i}function kh(n,e,t){const r=[];let i;return ce(e,n)||(i=`Invalid rule override, rule: ->${n}<- cannot be overridden in the grammar: ->${t}<-as it is not defined in any of the super grammars `,r.push({message:i,type:ue.INVALID_RULE_OVERRIDE,ruleName:n})),r}function du(n,e,t,r=[]){const i=[],s=Nr(e.definition);if(D(s))return[];{const a=n.name;ce(s,n)&&i.push({message:t.buildLeftRecursionError({topLevelRule:n,leftRecursionPath:r}),type:ue.LEFT_RECURSION,ruleName:a});const l=li(s,r.concat([n])),u=ve(l,c=>{const d=re(r);return d.push(c),du(n,c,t,d)});return i.concat(u)}}function Nr(n){let e=[];if(D(n))return e;const t=Pe(n);if(t instanceof le)e.push(t.referencedRule);else if(t instanceof he||t instanceof ne||t instanceof xe||t instanceof Ie||t instanceof me||t instanceof j)e=e.concat(Nr(t.definition));else if(t instanceof ge)e=Ne(x(t.definition,s=>Nr(s.definition)));else if(!(t instanceof G))throw Error("non exhaustive match");const r=Wr(t),i=n.length>1;if(r&&i){const s=Q(n);return e.concat(Nr(s))}else return e}class qs extends nn{constructor(){super(...arguments),this.alternations=[]}visitAlternation(e){this.alternations.push(e)}}function Sh(n,e){const t=new qs;n.accept(t);const r=t.alternations;return ve(r,s=>{const a=zn(s.definition);return ve(a,(o,l)=>{const u=au([o],[],tr,1);return D(u)?[{message:e.buildEmptyAlternationError({topLevelRule:n,alternation:s,emptyChoiceIdx:l}),type:ue.NONE_LAST_EMPTY_ALT,ruleName:n.name,occurrence:s.idx,alternative:l+1}]:[]})})}function xh(n,e,t){const r=new qs;n.accept(r);let i=r.alternations;return i=ui(i,a=>a.ignoreAmbiguities===!0),ve(i,a=>{const o=a.idx,l=a.maxLookahead||e,u=yi(o,n,l,a),c=Nh(u,a,n,t),d=wh(u,a,n,t);return c.concat(d)})}class Ih extends nn{constructor(){super(...arguments),this.allProductions=[]}visitRepetitionWithSeparator(e){this.allProductions.push(e)}visitRepetitionMandatory(e){this.allProductions.push(e)}visitRepetitionMandatoryWithSeparator(e){this.allProductions.push(e)}visitRepetition(e){this.allProductions.push(e)}}function Ch(n,e){const t=new qs;n.accept(t);const r=t.alternations;return ve(r,s=>s.definition.length>255?[{message:e.buildTooManyAlternativesError({topLevelRule:n,alternation:s}),type:ue.TOO_MANY_ALTS,ruleName:n.name,occurrence:s.idx}]:[])}function $h(n,e,t){const r=[];return $(n,i=>{const s=new Ih;i.accept(s);const a=s.allProductions;$(a,o=>{const l=zs(o),u=o.maxLookahead||e,c=o.idx,h=Ti(c,i,l,u)[0];if(D(Ne(h))){const f=t.buildEmptyRepetitionError({topLevelRule:i,repetition:o});r.push({message:f,type:ue.NO_NON_EMPTY_LOOKAHEAD,ruleName:i.name})}})}),r}function Nh(n,e,t,r){const i=[],s=oe(n,(o,l,u)=>(e.definition[u].ignoreAmbiguities===!0||$(l,c=>{const d=[u];$(n,(h,f)=>{u!==f&&as(h,c)&&e.definition[f].ignoreAmbiguities!==!0&&d.push(f)}),d.length>1&&!as(i,c)&&(i.push(c),o.push({alts:d,path:c}))}),o),[]);return x(s,o=>{const l=x(o.alts,c=>c+1);return{message:r.buildAlternationAmbiguityError({topLevelRule:t,alternation:e,ambiguityIndices:l,prefixPath:o.path}),type:ue.AMBIGUOUS_ALTS,ruleName:t.name,occurrence:e.idx,alternatives:o.alts}})}function wh(n,e,t,r){const i=oe(n,(a,o,l)=>{const u=x(o,c=>({idx:l,path:c}));return a.concat(u)},[]);return Jn(ve(i,a=>{if(e.definition[a.idx].ignoreAmbiguities===!0)return[];const l=a.idx,u=a.path,c=Se(i,h=>e.definition[h.idx].ignoreAmbiguities!==!0&&h.idx<l&&gh(h.path,u));return x(c,h=>{const f=[h.idx+1,l+1],m=e.idx===0?"":e.idx;return{message:r.buildAlternationPrefixAmbiguityError({topLevelRule:t,alternation:e,ambiguityIndices:f,prefixPath:h.path}),type:ue.AMBIGUOUS_PREFIX_ALTS,ruleName:t.name,occurrence:m,alternatives:f}})}))}function _h(n,e,t){const r=[],i=x(e,s=>s.name);return $(n,s=>{const a=s.name;if(ce(i,a)){const o=t.buildNamespaceConflictError(s);r.push({message:o,type:ue.CONFLICT_TOKENS_RULES_NAMESPACE,ruleName:a})}}),r}function Lh(n){const e=bs(n,{errMsgProvider:nh}),t={};return $(n.rules,r=>{t[r.name]=r}),rh(t,e.errMsgProvider)}function Oh(n){return n=bs(n,{errMsgProvider:dt}),Th(n.rules,n.tokenTypes,n.errMsgProvider,n.grammarName)}const fu="MismatchedTokenException",hu="NoViableAltException",pu="EarlyExitException",mu="NotAllInputParsedException",gu=[fu,hu,pu,mu];Object.freeze(gu);function Hr(n){return ce(gu,n.name)}class Ri extends Error{constructor(e,t){super(e),this.token=t,this.resyncedTokens=[],Object.setPrototypeOf(this,new.target.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}}class yu extends Ri{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=fu}}class bh extends Ri{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=hu}}class Ph extends Ri{constructor(e,t){super(e,t),this.name=mu}}class Mh extends Ri{constructor(e,t,r){super(e,t),this.previousToken=r,this.name=pu}}const bi={},Tu="InRuleRecoveryException";class Dh extends Error{constructor(e){super(e),this.name=Tu}}class Fh{initRecoverable(e){this.firstAfterRepMap={},this.resyncFollows={},this.recoveryEnabled=N(e,"recoveryEnabled")?e.recoveryEnabled:Xe.recoveryEnabled,this.recoveryEnabled&&(this.attemptInRepetitionRecovery=Gh)}getTokenToInsert(e){const t=Hs(e,"",NaN,NaN,NaN,NaN,NaN,NaN);return t.isInsertedInRecovery=!0,t}canTokenTypeBeInsertedInRecovery(e){return!0}canTokenTypeBeDeletedInRecovery(e){return!0}tryInRepetitionRecovery(e,t,r,i){const s=this.findReSyncTokenType(),a=this.exportLexerState(),o=[];let l=!1;const u=this.LA(1);let c=this.LA(1);const d=()=>{const h=this.LA(0),f=this.errorMessageProvider.buildMismatchTokenMessage({expected:i,actual:u,previous:h,ruleName:this.getCurrRuleFullName()}),m=new yu(f,u,this.LA(0));m.resyncedTokens=zn(o),this.SAVE_ERROR(m)};for(;!l;)if(this.tokenMatcher(c,i)){d();return}else if(r.call(this)){d(),e.apply(this,t);return}else this.tokenMatcher(c,s)?l=!0:(c=this.SKIP_TOKEN(),this.addToResyncTokens(c,o));this.importLexerState(a)}shouldInRepetitionRecoveryBeTried(e,t,r){return!(r===!1||this.tokenMatcher(this.LA(1),e)||this.isBackTracking()||this.canPerformInRuleRecovery(e,this.getFollowsForInRuleRecovery(e,t)))}getFollowsForInRuleRecovery(e,t){const r=this.getCurrentGrammarPath(e,t);return this.getNextPossibleTokenTypes(r)}tryInRuleRecovery(e,t){if(this.canRecoverWithSingleTokenInsertion(e,t))return this.getTokenToInsert(e);if(this.canRecoverWithSingleTokenDeletion(e)){const r=this.SKIP_TOKEN();return this.consumeToken(),r}throw new Dh("sad sad panda")}canPerformInRuleRecovery(e,t){return this.canRecoverWithSingleTokenInsertion(e,t)||this.canRecoverWithSingleTokenDeletion(e)}canRecoverWithSingleTokenInsertion(e,t){if(!this.canTokenTypeBeInsertedInRecovery(e)||D(t))return!1;const r=this.LA(1);return zt(t,s=>this.tokenMatcher(r,s))!==void 0}canRecoverWithSingleTokenDeletion(e){return this.canTokenTypeBeDeletedInRecovery(e)?this.tokenMatcher(this.LA(2),e):!1}isInCurrentRuleReSyncSet(e){const t=this.getCurrFollowKey(),r=this.getFollowSetFromFollowKey(t);return ce(r,e)}findReSyncTokenType(){const e=this.flattenFollowSet();let t=this.LA(1),r=2;for(;;){const i=zt(e,s=>su(t,s));if(i!==void 0)return i;t=this.LA(r),r++}}getCurrFollowKey(){if(this.RULE_STACK.length===1)return bi;const e=this.getLastExplicitRuleShortName(),t=this.getLastExplicitRuleOccurrenceIndex(),r=this.getPreviousExplicitRuleShortName();return{ruleName:this.shortRuleNameToFullName(e),idxInCallingRule:t,inRule:this.shortRuleNameToFullName(r)}}buildFullFollowKeyStack(){const e=this.RULE_STACK,t=this.RULE_OCCURRENCE_STACK;return x(e,(r,i)=>i===0?bi:{ruleName:this.shortRuleNameToFullName(r),idxInCallingRule:t[i],inRule:this.shortRuleNameToFullName(e[i-1])})}flattenFollowSet(){const e=x(this.buildFullFollowKeyStack(),t=>this.getFollowSetFromFollowKey(t));return Ne(e)}getFollowSetFromFollowKey(e){if(e===bi)return[nt];const t=e.ruleName+e.idxInCallingRule+ql+e.inRule;return this.resyncFollows[t]}addToResyncTokens(e,t){return this.tokenMatcher(e,nt)||t.push(e),t}reSyncTo(e){const t=[];let r=this.LA(1);for(;this.tokenMatcher(r,e)===!1;)r=this.SKIP_TOKEN(),this.addToResyncTokens(r,t);return zn(t)}attemptInRepetitionRecovery(e,t,r,i,s,a,o){}getCurrentGrammarPath(e,t){const r=this.getHumanReadableRuleStack(),i=re(this.RULE_OCCURRENCE_STACK);return{ruleStack:r,occurrenceStack:i,lastTok:e,lastTokOccurrence:t}}getHumanReadableRuleStack(){return x(this.RULE_STACK,e=>this.shortRuleNameToFullName(e))}}function Gh(n,e,t,r,i,s,a){const o=this.getKeyForAutomaticLookahead(r,i);let l=this.firstAfterRepMap[o];if(l===void 0){const h=this.getCurrRuleFullName(),f=this.getGAstProductions()[h];l=new s(f,i).startWalking(),this.firstAfterRepMap[o]=l}let u=l.token,c=l.occurrence;const d=l.isEndOfRule;this.RULE_STACK.length===1&&d&&u===void 0&&(u=nt,c=1),!(u===void 0||c===void 0)&&this.shouldInRepetitionRecoveryBeTried(u,c,a)&&this.tryInRepetitionRecovery(n,e,t,u)}const Uh=4,st=8,Ru=1<<st,Au=2<<st,os=3<<st,ls=4<<st,us=5<<st,wr=6<<st;function Pi(n,e,t){return t|e|n}class Ys{constructor(e){var t;this.maxLookahead=(t=e==null?void 0:e.maxLookahead)!==null&&t!==void 0?t:Xe.maxLookahead}validate(e){const t=this.validateNoLeftRecursion(e.rules);if(D(t)){const r=this.validateEmptyOrAlternatives(e.rules),i=this.validateAmbiguousAlternationAlternatives(e.rules,this.maxLookahead),s=this.validateSomeNonEmptyLookaheadPath(e.rules,this.maxLookahead);return[...t,...r,...i,...s]}return t}validateNoLeftRecursion(e){return ve(e,t=>du(t,t,dt))}validateEmptyOrAlternatives(e){return ve(e,t=>Sh(t,dt))}validateAmbiguousAlternationAlternatives(e,t){return ve(e,r=>xh(r,t,dt))}validateSomeNonEmptyLookaheadPath(e,t){return $h(e,t,dt)}buildLookaheadForAlternation(e){return ch(e.prodOccurrence,e.rule,e.maxLookahead,e.hasPredicates,e.dynamicTokensEnabled,fh)}buildLookaheadForOptional(e){return dh(e.prodOccurrence,e.rule,e.maxLookahead,e.dynamicTokensEnabled,zs(e.prodType),hh)}}class Bh{initLooksAhead(e){this.dynamicTokensEnabled=N(e,"dynamicTokensEnabled")?e.dynamicTokensEnabled:Xe.dynamicTokensEnabled,this.maxLookahead=N(e,"maxLookahead")?e.maxLookahead:Xe.maxLookahead,this.lookaheadStrategy=N(e,"lookaheadStrategy")?e.lookaheadStrategy:new Ys({maxLookahead:this.maxLookahead}),this.lookAheadFuncsCache=new Map}preComputeLookaheadFunctions(e){$(e,t=>{this.TRACE_INIT(`${t.name} Rule Lookahead`,()=>{const{alternation:r,repetition:i,option:s,repetitionMandatory:a,repetitionMandatoryWithSeparator:o,repetitionWithSeparator:l}=Wh(t);$(r,u=>{const c=u.idx===0?"":u.idx;this.TRACE_INIT(`${Ge(u)}${c}`,()=>{const d=this.lookaheadStrategy.buildLookaheadForAlternation({prodOccurrence:u.idx,rule:t,maxLookahead:u.maxLookahead||this.maxLookahead,hasPredicates:u.hasPredicates,dynamicTokensEnabled:this.dynamicTokensEnabled}),h=Pi(this.fullRuleNameToShort[t.name],Ru,u.idx);this.setLaFuncCache(h,d)})}),$(i,u=>{this.computeLookaheadFunc(t,u.idx,os,"Repetition",u.maxLookahead,Ge(u))}),$(s,u=>{this.computeLookaheadFunc(t,u.idx,Au,"Option",u.maxLookahead,Ge(u))}),$(a,u=>{this.computeLookaheadFunc(t,u.idx,ls,"RepetitionMandatory",u.maxLookahead,Ge(u))}),$(o,u=>{this.computeLookaheadFunc(t,u.idx,wr,"RepetitionMandatoryWithSeparator",u.maxLookahead,Ge(u))}),$(l,u=>{this.computeLookaheadFunc(t,u.idx,us,"RepetitionWithSeparator",u.maxLookahead,Ge(u))})})})}computeLookaheadFunc(e,t,r,i,s,a){this.TRACE_INIT(`${a}${t===0?"":t}`,()=>{const o=this.lookaheadStrategy.buildLookaheadForOptional({prodOccurrence:t,rule:e,maxLookahead:s||this.maxLookahead,dynamicTokensEnabled:this.dynamicTokensEnabled,prodType:i}),l=Pi(this.fullRuleNameToShort[e.name],r,t);this.setLaFuncCache(l,o)})}getKeyForAutomaticLookahead(e,t){const r=this.getLastExplicitRuleShortName();return Pi(r,e,t)}getLaFuncFromCache(e){return this.lookAheadFuncsCache.get(e)}setLaFuncCache(e,t){this.lookAheadFuncsCache.set(e,t)}}class Vh extends nn{constructor(){super(...arguments),this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}reset(){this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}visitOption(e){this.dslMethods.option.push(e)}visitRepetitionWithSeparator(e){this.dslMethods.repetitionWithSeparator.push(e)}visitRepetitionMandatory(e){this.dslMethods.repetitionMandatory.push(e)}visitRepetitionMandatoryWithSeparator(e){this.dslMethods.repetitionMandatoryWithSeparator.push(e)}visitRepetition(e){this.dslMethods.repetition.push(e)}visitAlternation(e){this.dslMethods.alternation.push(e)}}const Tr=new Vh;function Wh(n){Tr.reset(),n.accept(Tr);const e=Tr.dslMethods;return Tr.reset(),e}function Da(n,e){isNaN(n.startOffset)===!0?(n.startOffset=e.startOffset,n.endOffset=e.endOffset):n.endOffset<e.endOffset&&(n.endOffset=e.endOffset)}function Fa(n,e){isNaN(n.startOffset)===!0?(n.startOffset=e.startOffset,n.startColumn=e.startColumn,n.startLine=e.startLine,n.endOffset=e.endOffset,n.endColumn=e.endColumn,n.endLine=e.endLine):n.endOffset<e.endOffset&&(n.endOffset=e.endOffset,n.endColumn=e.endColumn,n.endLine=e.endLine)}function jh(n,e,t){n.children[t]===void 0?n.children[t]=[e]:n.children[t].push(e)}function Kh(n,e,t){n.children[e]===void 0?n.children[e]=[t]:n.children[e].push(t)}const Hh="name";function Eu(n,e){Object.defineProperty(n,Hh,{enumerable:!1,configurable:!0,writable:!1,value:e})}function zh(n,e){const t=Ht(n),r=t.length;for(let i=0;i<r;i++){const s=t[i],a=n[s],o=a.length;for(let l=0;l<o;l++){const u=a[l];u.tokenTypeIdx===void 0&&this[u.name](u.children,e)}}}function qh(n,e){const t=function(){};Eu(t,n+"BaseSemantics");const r={visit:function(i,s){if(te(i)&&(i=i[0]),!qe(i))return this[i.name](i.children,s)},validateVisitor:function(){const i=Xh(this,e);if(!D(i)){const s=x(i,a=>a.msg);throw Error(`Errors Detected in CST Visitor <${this.constructor.name}>:
	${s.join(`

`).replace(/\n/g,`
	`)}`)}}};return t.prototype=r,t.prototype.constructor=t,t._RULE_NAMES=e,t}function Yh(n,e,t){const r=function(){};Eu(r,n+"BaseSemanticsWithDefaults");const i=Object.create(t.prototype);return $(e,s=>{i[s]=zh}),r.prototype=i,r.prototype.constructor=r,r}var cs;(function(n){n[n.REDUNDANT_METHOD=0]="REDUNDANT_METHOD",n[n.MISSING_METHOD=1]="MISSING_METHOD"})(cs||(cs={}));function Xh(n,e){return Jh(n,e)}function Jh(n,e){const t=Se(e,i=>Tt(n[i])===!1),r=x(t,i=>({msg:`Missing visitor method: <${i}> on ${n.constructor.name} CST Visitor.`,type:cs.MISSING_METHOD,methodName:i}));return Jn(r)}class Qh{initTreeBuilder(e){if(this.CST_STACK=[],this.outputCst=e.outputCst,this.nodeLocationTracking=N(e,"nodeLocationTracking")?e.nodeLocationTracking:Xe.nodeLocationTracking,!this.outputCst)this.cstInvocationStateUpdate=Y,this.cstFinallyStateUpdate=Y,this.cstPostTerminal=Y,this.cstPostNonTerminal=Y,this.cstPostRule=Y;else if(/full/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=Fa,this.setNodeLocationFromNode=Fa,this.cstPostRule=Y,this.setInitialNodeLocation=this.setInitialNodeLocationFullRecovery):(this.setNodeLocationFromToken=Y,this.setNodeLocationFromNode=Y,this.cstPostRule=this.cstPostRuleFull,this.setInitialNodeLocation=this.setInitialNodeLocationFullRegular);else if(/onlyOffset/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=Da,this.setNodeLocationFromNode=Da,this.cstPostRule=Y,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRecovery):(this.setNodeLocationFromToken=Y,this.setNodeLocationFromNode=Y,this.cstPostRule=this.cstPostRuleOnlyOffset,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRegular);else if(/none/i.test(this.nodeLocationTracking))this.setNodeLocationFromToken=Y,this.setNodeLocationFromNode=Y,this.cstPostRule=Y,this.setInitialNodeLocation=Y;else throw Error(`Invalid <nodeLocationTracking> config option: "${e.nodeLocationTracking}"`)}setInitialNodeLocationOnlyOffsetRecovery(e){e.location={startOffset:NaN,endOffset:NaN}}setInitialNodeLocationOnlyOffsetRegular(e){e.location={startOffset:this.LA(1).startOffset,endOffset:NaN}}setInitialNodeLocationFullRecovery(e){e.location={startOffset:NaN,startLine:NaN,startColumn:NaN,endOffset:NaN,endLine:NaN,endColumn:NaN}}setInitialNodeLocationFullRegular(e){const t=this.LA(1);e.location={startOffset:t.startOffset,startLine:t.startLine,startColumn:t.startColumn,endOffset:NaN,endLine:NaN,endColumn:NaN}}cstInvocationStateUpdate(e){const t={name:e,children:Object.create(null)};this.setInitialNodeLocation(t),this.CST_STACK.push(t)}cstFinallyStateUpdate(){this.CST_STACK.pop()}cstPostRuleFull(e){const t=this.LA(0),r=e.location;r.startOffset<=t.startOffset?(r.endOffset=t.endOffset,r.endLine=t.endLine,r.endColumn=t.endColumn):(r.startOffset=NaN,r.startLine=NaN,r.startColumn=NaN)}cstPostRuleOnlyOffset(e){const t=this.LA(0),r=e.location;r.startOffset<=t.startOffset?r.endOffset=t.endOffset:r.startOffset=NaN}cstPostTerminal(e,t){const r=this.CST_STACK[this.CST_STACK.length-1];jh(r,t,e),this.setNodeLocationFromToken(r.location,t)}cstPostNonTerminal(e,t){const r=this.CST_STACK[this.CST_STACK.length-1];Kh(r,t,e),this.setNodeLocationFromNode(r.location,e.location)}getBaseCstVisitorConstructor(){if(qe(this.baseCstVisitorConstructor)){const e=qh(this.className,Ht(this.gastProductionsCache));return this.baseCstVisitorConstructor=e,e}return this.baseCstVisitorConstructor}getBaseCstVisitorConstructorWithDefaults(){if(qe(this.baseCstVisitorWithDefaultsConstructor)){const e=Yh(this.className,Ht(this.gastProductionsCache),this.getBaseCstVisitorConstructor());return this.baseCstVisitorWithDefaultsConstructor=e,e}return this.baseCstVisitorWithDefaultsConstructor}getLastExplicitRuleShortName(){const e=this.RULE_STACK;return e[e.length-1]}getPreviousExplicitRuleShortName(){const e=this.RULE_STACK;return e[e.length-2]}getLastExplicitRuleOccurrenceIndex(){const e=this.RULE_OCCURRENCE_STACK;return e[e.length-1]}}class Zh{initLexerAdapter(){this.tokVector=[],this.tokVectorLength=0,this.currIdx=-1}set input(e){if(this.selfAnalysisDone!==!0)throw Error("Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.");this.reset(),this.tokVector=e,this.tokVectorLength=e.length}get input(){return this.tokVector}SKIP_TOKEN(){return this.currIdx<=this.tokVector.length-2?(this.consumeToken(),this.LA(1)):qr}LA(e){const t=this.currIdx+e;return t<0||this.tokVectorLength<=t?qr:this.tokVector[t]}consumeToken(){this.currIdx++}exportLexerState(){return this.currIdx}importLexerState(e){this.currIdx=e}resetLexerState(){this.currIdx=-1}moveToTerminatedState(){this.currIdx=this.tokVector.length-1}getLexerPosition(){return this.exportLexerState()}}class ep{ACTION(e){return e.call(this)}consume(e,t,r){return this.consumeInternal(t,e,r)}subrule(e,t,r){return this.subruleInternal(t,e,r)}option(e,t){return this.optionInternal(t,e)}or(e,t){return this.orInternal(t,e)}many(e,t){return this.manyInternal(e,t)}atLeastOne(e,t){return this.atLeastOneInternal(e,t)}CONSUME(e,t){return this.consumeInternal(e,0,t)}CONSUME1(e,t){return this.consumeInternal(e,1,t)}CONSUME2(e,t){return this.consumeInternal(e,2,t)}CONSUME3(e,t){return this.consumeInternal(e,3,t)}CONSUME4(e,t){return this.consumeInternal(e,4,t)}CONSUME5(e,t){return this.consumeInternal(e,5,t)}CONSUME6(e,t){return this.consumeInternal(e,6,t)}CONSUME7(e,t){return this.consumeInternal(e,7,t)}CONSUME8(e,t){return this.consumeInternal(e,8,t)}CONSUME9(e,t){return this.consumeInternal(e,9,t)}SUBRULE(e,t){return this.subruleInternal(e,0,t)}SUBRULE1(e,t){return this.subruleInternal(e,1,t)}SUBRULE2(e,t){return this.subruleInternal(e,2,t)}SUBRULE3(e,t){return this.subruleInternal(e,3,t)}SUBRULE4(e,t){return this.subruleInternal(e,4,t)}SUBRULE5(e,t){return this.subruleInternal(e,5,t)}SUBRULE6(e,t){return this.subruleInternal(e,6,t)}SUBRULE7(e,t){return this.subruleInternal(e,7,t)}SUBRULE8(e,t){return this.subruleInternal(e,8,t)}SUBRULE9(e,t){return this.subruleInternal(e,9,t)}OPTION(e){return this.optionInternal(e,0)}OPTION1(e){return this.optionInternal(e,1)}OPTION2(e){return this.optionInternal(e,2)}OPTION3(e){return this.optionInternal(e,3)}OPTION4(e){return this.optionInternal(e,4)}OPTION5(e){return this.optionInternal(e,5)}OPTION6(e){return this.optionInternal(e,6)}OPTION7(e){return this.optionInternal(e,7)}OPTION8(e){return this.optionInternal(e,8)}OPTION9(e){return this.optionInternal(e,9)}OR(e){return this.orInternal(e,0)}OR1(e){return this.orInternal(e,1)}OR2(e){return this.orInternal(e,2)}OR3(e){return this.orInternal(e,3)}OR4(e){return this.orInternal(e,4)}OR5(e){return this.orInternal(e,5)}OR6(e){return this.orInternal(e,6)}OR7(e){return this.orInternal(e,7)}OR8(e){return this.orInternal(e,8)}OR9(e){return this.orInternal(e,9)}MANY(e){this.manyInternal(0,e)}MANY1(e){this.manyInternal(1,e)}MANY2(e){this.manyInternal(2,e)}MANY3(e){this.manyInternal(3,e)}MANY4(e){this.manyInternal(4,e)}MANY5(e){this.manyInternal(5,e)}MANY6(e){this.manyInternal(6,e)}MANY7(e){this.manyInternal(7,e)}MANY8(e){this.manyInternal(8,e)}MANY9(e){this.manyInternal(9,e)}MANY_SEP(e){this.manySepFirstInternal(0,e)}MANY_SEP1(e){this.manySepFirstInternal(1,e)}MANY_SEP2(e){this.manySepFirstInternal(2,e)}MANY_SEP3(e){this.manySepFirstInternal(3,e)}MANY_SEP4(e){this.manySepFirstInternal(4,e)}MANY_SEP5(e){this.manySepFirstInternal(5,e)}MANY_SEP6(e){this.manySepFirstInternal(6,e)}MANY_SEP7(e){this.manySepFirstInternal(7,e)}MANY_SEP8(e){this.manySepFirstInternal(8,e)}MANY_SEP9(e){this.manySepFirstInternal(9,e)}AT_LEAST_ONE(e){this.atLeastOneInternal(0,e)}AT_LEAST_ONE1(e){return this.atLeastOneInternal(1,e)}AT_LEAST_ONE2(e){this.atLeastOneInternal(2,e)}AT_LEAST_ONE3(e){this.atLeastOneInternal(3,e)}AT_LEAST_ONE4(e){this.atLeastOneInternal(4,e)}AT_LEAST_ONE5(e){this.atLeastOneInternal(5,e)}AT_LEAST_ONE6(e){this.atLeastOneInternal(6,e)}AT_LEAST_ONE7(e){this.atLeastOneInternal(7,e)}AT_LEAST_ONE8(e){this.atLeastOneInternal(8,e)}AT_LEAST_ONE9(e){this.atLeastOneInternal(9,e)}AT_LEAST_ONE_SEP(e){this.atLeastOneSepFirstInternal(0,e)}AT_LEAST_ONE_SEP1(e){this.atLeastOneSepFirstInternal(1,e)}AT_LEAST_ONE_SEP2(e){this.atLeastOneSepFirstInternal(2,e)}AT_LEAST_ONE_SEP3(e){this.atLeastOneSepFirstInternal(3,e)}AT_LEAST_ONE_SEP4(e){this.atLeastOneSepFirstInternal(4,e)}AT_LEAST_ONE_SEP5(e){this.atLeastOneSepFirstInternal(5,e)}AT_LEAST_ONE_SEP6(e){this.atLeastOneSepFirstInternal(6,e)}AT_LEAST_ONE_SEP7(e){this.atLeastOneSepFirstInternal(7,e)}AT_LEAST_ONE_SEP8(e){this.atLeastOneSepFirstInternal(8,e)}AT_LEAST_ONE_SEP9(e){this.atLeastOneSepFirstInternal(9,e)}RULE(e,t,r=Yr){if(ce(this.definedRulesNames,e)){const a={message:dt.buildDuplicateRuleNameError({topLevelRule:e,grammarName:this.className}),type:ue.DUPLICATE_RULE_NAME,ruleName:e};this.definitionErrors.push(a)}this.definedRulesNames.push(e);const i=this.defineRule(e,t,r);return this[e]=i,i}OVERRIDE_RULE(e,t,r=Yr){const i=kh(e,this.definedRulesNames,this.className);this.definitionErrors=this.definitionErrors.concat(i);const s=this.defineRule(e,t,r);return this[e]=s,s}BACKTRACK(e,t){return function(){this.isBackTrackingStack.push(1);const r=this.saveRecogState();try{return e.apply(this,t),!0}catch(i){if(Hr(i))return!1;throw i}finally{this.reloadRecogState(r),this.isBackTrackingStack.pop()}}}getGAstProductions(){return this.gastProductionsCache}getSerializedGastProductions(){return uf(z(this.gastProductionsCache))}}class tp{initRecognizerEngine(e,t){if(this.className=this.constructor.name,this.shortRuleNameToFull={},this.fullRuleNameToShort={},this.ruleShortNameIdx=256,this.tokenMatcher=Kr,this.subruleIdx=0,this.definedRulesNames=[],this.tokensMap={},this.isBackTrackingStack=[],this.RULE_STACK=[],this.RULE_OCCURRENCE_STACK=[],this.gastProductionsCache={},N(t,"serializedGrammar"))throw Error(`The Parser's configuration can no longer contain a <serializedGrammar> property.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0
	For Further details.`);if(te(e)){if(D(e))throw Error(`A Token Vocabulary cannot be empty.
	Note that the first argument for the parser constructor
	is no longer a Token vector (since v4.0).`);if(typeof e[0].startOffset=="number")throw Error(`The Parser constructor no longer accepts a token vector as the first argument.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0
	For Further details.`)}if(te(e))this.tokensMap=oe(e,(s,a)=>(s[a.name]=a,s),{});else if(N(e,"modes")&&be(Ne(z(e.modes)),Zf)){const s=Ne(z(e.modes)),a=Ps(s);this.tokensMap=oe(a,(o,l)=>(o[l.name]=l,o),{})}else if($c(e))this.tokensMap=re(e);else throw new Error("<tokensDictionary> argument must be An Array of Token constructors, A dictionary of Token constructors or an IMultiModeLexerDefinition");this.tokensMap.EOF=nt;const r=N(e,"modes")?Ne(z(e.modes)):z(e),i=be(r,s=>D(s.categoryMatches));this.tokenMatcher=i?Kr:tr,nr(z(this.tokensMap))}defineRule(e,t,r){if(this.selfAnalysisDone)throw Error(`Grammar rule <${e}> may not be defined after the 'performSelfAnalysis' method has been called'
Make sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`);const i=N(r,"resyncEnabled")?r.resyncEnabled:Yr.resyncEnabled,s=N(r,"recoveryValueFunc")?r.recoveryValueFunc:Yr.recoveryValueFunc,a=this.ruleShortNameIdx<<Uh+st;this.ruleShortNameIdx++,this.shortRuleNameToFull[a]=e,this.fullRuleNameToShort[e]=a;let o;return this.outputCst===!0?o=function(...c){try{this.ruleInvocationStateUpdate(a,e,this.subruleIdx),t.apply(this,c);const d=this.CST_STACK[this.CST_STACK.length-1];return this.cstPostRule(d),d}catch(d){return this.invokeRuleCatch(d,i,s)}finally{this.ruleFinallyStateUpdate()}}:o=function(...c){try{return this.ruleInvocationStateUpdate(a,e,this.subruleIdx),t.apply(this,c)}catch(d){return this.invokeRuleCatch(d,i,s)}finally{this.ruleFinallyStateUpdate()}},Object.assign(o,{ruleName:e,originalGrammarAction:t})}invokeRuleCatch(e,t,r){const i=this.RULE_STACK.length===1,s=t&&!this.isBackTracking()&&this.recoveryEnabled;if(Hr(e)){const a=e;if(s){const o=this.findReSyncTokenType();if(this.isInCurrentRuleReSyncSet(o))if(a.resyncedTokens=this.reSyncTo(o),this.outputCst){const l=this.CST_STACK[this.CST_STACK.length-1];return l.recoveredNode=!0,l}else return r(e);else{if(this.outputCst){const l=this.CST_STACK[this.CST_STACK.length-1];l.recoveredNode=!0,a.partialCstResult=l}throw a}}else{if(i)return this.moveToTerminatedState(),r(e);throw a}}else throw e}optionInternal(e,t){const r=this.getKeyForAutomaticLookahead(Au,t);return this.optionInternalLogic(e,t,r)}optionInternalLogic(e,t,r){let i=this.getLaFuncFromCache(r),s;if(typeof e!="function"){s=e.DEF;const a=e.GATE;if(a!==void 0){const o=i;i=()=>a.call(this)&&o.call(this)}}else s=e;if(i.call(this)===!0)return s.call(this)}atLeastOneInternal(e,t){const r=this.getKeyForAutomaticLookahead(ls,e);return this.atLeastOneInternalLogic(e,t,r)}atLeastOneInternalLogic(e,t,r){let i=this.getLaFuncFromCache(r),s;if(typeof t!="function"){s=t.DEF;const a=t.GATE;if(a!==void 0){const o=i;i=()=>a.call(this)&&o.call(this)}}else s=t;if(i.call(this)===!0){let a=this.doSingleRepetition(s);for(;i.call(this)===!0&&a===!0;)a=this.doSingleRepetition(s)}else throw this.raiseEarlyExitException(e,B.REPETITION_MANDATORY,t.ERR_MSG);this.attemptInRepetitionRecovery(this.atLeastOneInternal,[e,t],i,ls,e,lh)}atLeastOneSepFirstInternal(e,t){const r=this.getKeyForAutomaticLookahead(wr,e);this.atLeastOneSepFirstInternalLogic(e,t,r)}atLeastOneSepFirstInternalLogic(e,t,r){const i=t.DEF,s=t.SEP;if(this.getLaFuncFromCache(r).call(this)===!0){i.call(this);const o=()=>this.tokenMatcher(this.LA(1),s);for(;this.tokenMatcher(this.LA(1),s)===!0;)this.CONSUME(s),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,s,o,i,ba],o,wr,e,ba)}else throw this.raiseEarlyExitException(e,B.REPETITION_MANDATORY_WITH_SEPARATOR,t.ERR_MSG)}manyInternal(e,t){const r=this.getKeyForAutomaticLookahead(os,e);return this.manyInternalLogic(e,t,r)}manyInternalLogic(e,t,r){let i=this.getLaFuncFromCache(r),s;if(typeof t!="function"){s=t.DEF;const o=t.GATE;if(o!==void 0){const l=i;i=()=>o.call(this)&&l.call(this)}}else s=t;let a=!0;for(;i.call(this)===!0&&a===!0;)a=this.doSingleRepetition(s);this.attemptInRepetitionRecovery(this.manyInternal,[e,t],i,os,e,oh,a)}manySepFirstInternal(e,t){const r=this.getKeyForAutomaticLookahead(us,e);this.manySepFirstInternalLogic(e,t,r)}manySepFirstInternalLogic(e,t,r){const i=t.DEF,s=t.SEP;if(this.getLaFuncFromCache(r).call(this)===!0){i.call(this);const o=()=>this.tokenMatcher(this.LA(1),s);for(;this.tokenMatcher(this.LA(1),s)===!0;)this.CONSUME(s),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,s,o,i,Oa],o,us,e,Oa)}}repetitionSepSecondInternal(e,t,r,i,s){for(;r();)this.CONSUME(t),i.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[e,t,r,i,s],r,wr,e,s)}doSingleRepetition(e){const t=this.getLexerPosition();return e.call(this),this.getLexerPosition()>t}orInternal(e,t){const r=this.getKeyForAutomaticLookahead(Ru,t),i=te(e)?e:e.DEF,a=this.getLaFuncFromCache(r).call(this,i);if(a!==void 0)return i[a].ALT.call(this);this.raiseNoAltException(t,e.ERR_MSG)}ruleFinallyStateUpdate(){if(this.RULE_STACK.pop(),this.RULE_OCCURRENCE_STACK.pop(),this.cstFinallyStateUpdate(),this.RULE_STACK.length===0&&this.isAtEndOfInput()===!1){const e=this.LA(1),t=this.errorMessageProvider.buildNotAllInputParsedMessage({firstRedundant:e,ruleName:this.getCurrRuleFullName()});this.SAVE_ERROR(new Ph(t,e))}}subruleInternal(e,t,r){let i;try{const s=r!==void 0?r.ARGS:void 0;return this.subruleIdx=t,i=e.apply(this,s),this.cstPostNonTerminal(i,r!==void 0&&r.LABEL!==void 0?r.LABEL:e.ruleName),i}catch(s){throw this.subruleInternalError(s,r,e.ruleName)}}subruleInternalError(e,t,r){throw Hr(e)&&e.partialCstResult!==void 0&&(this.cstPostNonTerminal(e.partialCstResult,t!==void 0&&t.LABEL!==void 0?t.LABEL:r),delete e.partialCstResult),e}consumeInternal(e,t,r){let i;try{const s=this.LA(1);this.tokenMatcher(s,e)===!0?(this.consumeToken(),i=s):this.consumeInternalError(e,s,r)}catch(s){i=this.consumeInternalRecovery(e,t,s)}return this.cstPostTerminal(r!==void 0&&r.LABEL!==void 0?r.LABEL:e.name,i),i}consumeInternalError(e,t,r){let i;const s=this.LA(0);throw r!==void 0&&r.ERR_MSG?i=r.ERR_MSG:i=this.errorMessageProvider.buildMismatchTokenMessage({expected:e,actual:t,previous:s,ruleName:this.getCurrRuleFullName()}),this.SAVE_ERROR(new yu(i,t,s))}consumeInternalRecovery(e,t,r){if(this.recoveryEnabled&&r.name==="MismatchedTokenException"&&!this.isBackTracking()){const i=this.getFollowsForInRuleRecovery(e,t);try{return this.tryInRuleRecovery(e,i)}catch(s){throw s.name===Tu?r:s}}else throw r}saveRecogState(){const e=this.errors,t=re(this.RULE_STACK);return{errors:e,lexerState:this.exportLexerState(),RULE_STACK:t,CST_STACK:this.CST_STACK}}reloadRecogState(e){this.errors=e.errors,this.importLexerState(e.lexerState),this.RULE_STACK=e.RULE_STACK}ruleInvocationStateUpdate(e,t,r){this.RULE_OCCURRENCE_STACK.push(r),this.RULE_STACK.push(e),this.cstInvocationStateUpdate(t)}isBackTracking(){return this.isBackTrackingStack.length!==0}getCurrRuleFullName(){const e=this.getLastExplicitRuleShortName();return this.shortRuleNameToFull[e]}shortRuleNameToFullName(e){return this.shortRuleNameToFull[e]}isAtEndOfInput(){return this.tokenMatcher(this.LA(1),nt)}reset(){this.resetLexerState(),this.subruleIdx=0,this.isBackTrackingStack=[],this.errors=[],this.RULE_STACK=[],this.CST_STACK=[],this.RULE_OCCURRENCE_STACK=[]}}class np{initErrorHandler(e){this._errors=[],this.errorMessageProvider=N(e,"errorMessageProvider")?e.errorMessageProvider:Xe.errorMessageProvider}SAVE_ERROR(e){if(Hr(e))return e.context={ruleStack:this.getHumanReadableRuleStack(),ruleOccurrenceStack:re(this.RULE_OCCURRENCE_STACK)},this._errors.push(e),e;throw Error("Trying to save an Error which is not a RecognitionException")}get errors(){return re(this._errors)}set errors(e){this._errors=e}raiseEarlyExitException(e,t,r){const i=this.getCurrRuleFullName(),s=this.getGAstProductions()[i],o=Ti(e,s,t,this.maxLookahead)[0],l=[];for(let c=1;c<=this.maxLookahead;c++)l.push(this.LA(c));const u=this.errorMessageProvider.buildEarlyExitMessage({expectedIterationPaths:o,actual:l,previous:this.LA(0),customUserDescription:r,ruleName:i});throw this.SAVE_ERROR(new Mh(u,this.LA(1),this.LA(0)))}raiseNoAltException(e,t){const r=this.getCurrRuleFullName(),i=this.getGAstProductions()[r],s=yi(e,i,this.maxLookahead),a=[];for(let u=1;u<=this.maxLookahead;u++)a.push(this.LA(u));const o=this.LA(0),l=this.errorMessageProvider.buildNoViableAltMessage({expectedPathsPerAlt:s,actual:a,previous:o,customUserDescription:t,ruleName:this.getCurrRuleFullName()});throw this.SAVE_ERROR(new bh(l,this.LA(1),o))}}class rp{initContentAssist(){}computeContentAssist(e,t){const r=this.gastProductionsCache[e];if(qe(r))throw Error(`Rule ->${e}<- does not exist in this grammar.`);return au([r],t,this.tokenMatcher,this.maxLookahead)}getNextPossibleTokenTypes(e){const t=Pe(e.ruleStack),i=this.getGAstProductions()[t];return new ah(i,e).startWalking()}}const Ai={description:"This Object indicates the Parser is during Recording Phase"};Object.freeze(Ai);const Ga=!0,Ua=Math.pow(2,st)-1,vu=iu({name:"RECORDING_PHASE_TOKEN",pattern:de.NA});nr([vu]);const ku=Hs(vu,`This IToken indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,-1,-1,-1,-1,-1,-1);Object.freeze(ku);const ip={name:`This CSTNode indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,children:{}};class sp{initGastRecorder(e){this.recordingProdStack=[],this.RECORDING_PHASE=!1}enableRecording(){this.RECORDING_PHASE=!0,this.TRACE_INIT("Enable Recording",()=>{for(let e=0;e<10;e++){const t=e>0?e:"";this[`CONSUME${t}`]=function(r,i){return this.consumeInternalRecord(r,e,i)},this[`SUBRULE${t}`]=function(r,i){return this.subruleInternalRecord(r,e,i)},this[`OPTION${t}`]=function(r){return this.optionInternalRecord(r,e)},this[`OR${t}`]=function(r){return this.orInternalRecord(r,e)},this[`MANY${t}`]=function(r){this.manyInternalRecord(e,r)},this[`MANY_SEP${t}`]=function(r){this.manySepFirstInternalRecord(e,r)},this[`AT_LEAST_ONE${t}`]=function(r){this.atLeastOneInternalRecord(e,r)},this[`AT_LEAST_ONE_SEP${t}`]=function(r){this.atLeastOneSepFirstInternalRecord(e,r)}}this.consume=function(e,t,r){return this.consumeInternalRecord(t,e,r)},this.subrule=function(e,t,r){return this.subruleInternalRecord(t,e,r)},this.option=function(e,t){return this.optionInternalRecord(t,e)},this.or=function(e,t){return this.orInternalRecord(t,e)},this.many=function(e,t){this.manyInternalRecord(e,t)},this.atLeastOne=function(e,t){this.atLeastOneInternalRecord(e,t)},this.ACTION=this.ACTION_RECORD,this.BACKTRACK=this.BACKTRACK_RECORD,this.LA=this.LA_RECORD})}disableRecording(){this.RECORDING_PHASE=!1,this.TRACE_INIT("Deleting Recording methods",()=>{const e=this;for(let t=0;t<10;t++){const r=t>0?t:"";delete e[`CONSUME${r}`],delete e[`SUBRULE${r}`],delete e[`OPTION${r}`],delete e[`OR${r}`],delete e[`MANY${r}`],delete e[`MANY_SEP${r}`],delete e[`AT_LEAST_ONE${r}`],delete e[`AT_LEAST_ONE_SEP${r}`]}delete e.consume,delete e.subrule,delete e.option,delete e.or,delete e.many,delete e.atLeastOne,delete e.ACTION,delete e.BACKTRACK,delete e.LA})}ACTION_RECORD(e){}BACKTRACK_RECORD(e,t){return()=>!0}LA_RECORD(e){return qr}topLevelRuleRecord(e,t){try{const r=new tn({definition:[],name:e});return r.name=e,this.recordingProdStack.push(r),t.call(this),this.recordingProdStack.pop(),r}catch(r){if(r.KNOWN_RECORDER_ERROR!==!0)try{r.message=r.message+`
	 This error was thrown during the "grammar recording phase" For more info see:
	https://chevrotain.io/docs/guide/internals.html#grammar-recording`}catch{throw r}throw r}}optionInternalRecord(e,t){return ln.call(this,ne,e,t)}atLeastOneInternalRecord(e,t){ln.call(this,xe,t,e)}atLeastOneSepFirstInternalRecord(e,t){ln.call(this,Ie,t,e,Ga)}manyInternalRecord(e,t){ln.call(this,j,t,e)}manySepFirstInternalRecord(e,t){ln.call(this,me,t,e,Ga)}orInternalRecord(e,t){return ap.call(this,e,t)}subruleInternalRecord(e,t,r){if(zr(t),!e||N(e,"ruleName")===!1){const o=new Error(`<SUBRULE${Ba(t)}> argument is invalid expecting a Parser method reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw o.KNOWN_RECORDER_ERROR=!0,o}const i=qt(this.recordingProdStack),s=e.ruleName,a=new le({idx:t,nonTerminalName:s,label:r==null?void 0:r.LABEL,referencedRule:void 0});return i.definition.push(a),this.outputCst?ip:Ai}consumeInternalRecord(e,t,r){if(zr(t),!nu(e)){const a=new Error(`<CONSUME${Ba(t)}> argument is invalid expecting a TokenType reference but got: <${JSON.stringify(e)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw a.KNOWN_RECORDER_ERROR=!0,a}const i=qt(this.recordingProdStack),s=new G({idx:t,terminalType:e,label:r==null?void 0:r.LABEL});return i.definition.push(s),ku}}function ln(n,e,t,r=!1){zr(t);const i=qt(this.recordingProdStack),s=Tt(e)?e:e.DEF,a=new n({definition:[],idx:t});return r&&(a.separator=e.SEP),N(e,"MAX_LOOKAHEAD")&&(a.maxLookahead=e.MAX_LOOKAHEAD),this.recordingProdStack.push(a),s.call(this),i.definition.push(a),this.recordingProdStack.pop(),Ai}function ap(n,e){zr(e);const t=qt(this.recordingProdStack),r=te(n)===!1,i=r===!1?n:n.DEF,s=new ge({definition:[],idx:e,ignoreAmbiguities:r&&n.IGNORE_AMBIGUITIES===!0});N(n,"MAX_LOOKAHEAD")&&(s.maxLookahead=n.MAX_LOOKAHEAD);const a=Sl(i,o=>Tt(o.GATE));return s.hasPredicates=a,t.definition.push(s),$(i,o=>{const l=new he({definition:[]});s.definition.push(l),N(o,"IGNORE_AMBIGUITIES")?l.ignoreAmbiguities=o.IGNORE_AMBIGUITIES:N(o,"GATE")&&(l.ignoreAmbiguities=!0),this.recordingProdStack.push(l),o.ALT.call(this),this.recordingProdStack.pop()}),Ai}function Ba(n){return n===0?"":`${n}`}function zr(n){if(n<0||n>Ua){const e=new Error(`Invalid DSL Method idx value: <${n}>
	Idx value must be a none negative value smaller than ${Ua+1}`);throw e.KNOWN_RECORDER_ERROR=!0,e}}class op{initPerformanceTracer(e){if(N(e,"traceInitPerf")){const t=e.traceInitPerf,r=typeof t=="number";this.traceInitMaxIdent=r?t:1/0,this.traceInitPerf=r?t>0:t}else this.traceInitMaxIdent=0,this.traceInitPerf=Xe.traceInitPerf;this.traceInitIndent=-1}TRACE_INIT(e,t){if(this.traceInitPerf===!0){this.traceInitIndent++;const r=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${r}--> <${e}>`);const{time:i,value:s}=Hl(t),a=i>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&a(`${r}<-- <${e}> time: ${i}ms`),this.traceInitIndent--,s}else return t()}}function lp(n,e){e.forEach(t=>{const r=t.prototype;Object.getOwnPropertyNames(r).forEach(i=>{if(i==="constructor")return;const s=Object.getOwnPropertyDescriptor(r,i);s&&(s.get||s.set)?Object.defineProperty(n.prototype,i,s):n.prototype[i]=t.prototype[i]})})}const qr=Hs(nt,"",NaN,NaN,NaN,NaN,NaN,NaN);Object.freeze(qr);const Xe=Object.freeze({recoveryEnabled:!1,maxLookahead:3,dynamicTokensEnabled:!1,outputCst:!0,errorMessageProvider:Nt,nodeLocationTracking:"none",traceInitPerf:!1,skipValidations:!1}),Yr=Object.freeze({recoveryValueFunc:()=>{},resyncEnabled:!0});var ue;(function(n){n[n.INVALID_RULE_NAME=0]="INVALID_RULE_NAME",n[n.DUPLICATE_RULE_NAME=1]="DUPLICATE_RULE_NAME",n[n.INVALID_RULE_OVERRIDE=2]="INVALID_RULE_OVERRIDE",n[n.DUPLICATE_PRODUCTIONS=3]="DUPLICATE_PRODUCTIONS",n[n.UNRESOLVED_SUBRULE_REF=4]="UNRESOLVED_SUBRULE_REF",n[n.LEFT_RECURSION=5]="LEFT_RECURSION",n[n.NONE_LAST_EMPTY_ALT=6]="NONE_LAST_EMPTY_ALT",n[n.AMBIGUOUS_ALTS=7]="AMBIGUOUS_ALTS",n[n.CONFLICT_TOKENS_RULES_NAMESPACE=8]="CONFLICT_TOKENS_RULES_NAMESPACE",n[n.INVALID_TOKEN_NAME=9]="INVALID_TOKEN_NAME",n[n.NO_NON_EMPTY_LOOKAHEAD=10]="NO_NON_EMPTY_LOOKAHEAD",n[n.AMBIGUOUS_PREFIX_ALTS=11]="AMBIGUOUS_PREFIX_ALTS",n[n.TOO_MANY_ALTS=12]="TOO_MANY_ALTS",n[n.CUSTOM_LOOKAHEAD_VALIDATION=13]="CUSTOM_LOOKAHEAD_VALIDATION"})(ue||(ue={}));function Va(n=void 0){return function(){return n}}class rr{static performSelfAnalysis(e){throw Error("The **static** `performSelfAnalysis` method has been deprecated.	\nUse the **instance** method with the same name instead.")}performSelfAnalysis(){this.TRACE_INIT("performSelfAnalysis",()=>{let e;this.selfAnalysisDone=!0;const t=this.className;this.TRACE_INIT("toFastProps",()=>{zl(this)}),this.TRACE_INIT("Grammar Recording",()=>{try{this.enableRecording(),$(this.definedRulesNames,i=>{const a=this[i].originalGrammarAction;let o;this.TRACE_INIT(`${i} Rule`,()=>{o=this.topLevelRuleRecord(i,a)}),this.gastProductionsCache[i]=o})}finally{this.disableRecording()}});let r=[];if(this.TRACE_INIT("Grammar Resolving",()=>{r=Lh({rules:z(this.gastProductionsCache)}),this.definitionErrors=this.definitionErrors.concat(r)}),this.TRACE_INIT("Grammar Validations",()=>{if(D(r)&&this.skipValidations===!1){const i=Oh({rules:z(this.gastProductionsCache),tokenTypes:z(this.tokensMap),errMsgProvider:dt,grammarName:t}),s=yh({lookaheadStrategy:this.lookaheadStrategy,rules:z(this.gastProductionsCache),tokenTypes:z(this.tokensMap),grammarName:t});this.definitionErrors=this.definitionErrors.concat(i,s)}}),D(this.definitionErrors)&&(this.recoveryEnabled&&this.TRACE_INIT("computeAllProdsFollows",()=>{const i=gf(z(this.gastProductionsCache));this.resyncFollows=i}),this.TRACE_INIT("ComputeLookaheadFunctions",()=>{var i,s;(s=(i=this.lookaheadStrategy).initialize)===null||s===void 0||s.call(i,{rules:z(this.gastProductionsCache)}),this.preComputeLookaheadFunctions(z(this.gastProductionsCache))})),!rr.DEFER_DEFINITION_ERRORS_HANDLING&&!D(this.definitionErrors))throw e=x(this.definitionErrors,i=>i.message),new Error(`Parser Definition Errors detected:
 ${e.join(`
-------------------------------
`)}`)})}constructor(e,t){this.definitionErrors=[],this.selfAnalysisDone=!1;const r=this;if(r.initErrorHandler(t),r.initLexerAdapter(),r.initLooksAhead(t),r.initRecognizerEngine(e,t),r.initRecoverable(t),r.initTreeBuilder(t),r.initContentAssist(),r.initGastRecorder(t),r.initPerformanceTracer(t),N(t,"ignoredIssues"))throw new Error(`The <ignoredIssues> IParserConfig property has been deprecated.
	Please use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.
	See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES
	For further details.`);this.skipValidations=N(t,"skipValidations")?t.skipValidations:Xe.skipValidations}}rr.DEFER_DEFINITION_ERRORS_HANDLING=!1;lp(rr,[Fh,Bh,Qh,Zh,tp,ep,np,rp,sp,op]);class up extends rr{constructor(e,t=Xe){const r=re(t);r.outputCst=!1,super(e,r)}}function Yt(n,e,t){return`${n.name}_${e}_${t}`}const rt=1,cp=2,Su=4,xu=5,ir=7,dp=8,fp=9,hp=10,pp=11,Iu=12;class Xs{constructor(e){this.target=e}isEpsilon(){return!1}}class Js extends Xs{constructor(e,t){super(e),this.tokenType=t}}class Cu extends Xs{constructor(e){super(e)}isEpsilon(){return!0}}class Qs extends Xs{constructor(e,t,r){super(e),this.rule=t,this.followState=r}isEpsilon(){return!0}}function mp(n){const e={decisionMap:{},decisionStates:[],ruleToStartState:new Map,ruleToStopState:new Map,states:[]};gp(e,n);const t=n.length;for(let r=0;r<t;r++){const i=n[r],s=At(e,i,i);s!==void 0&&Cp(e,i,s)}return e}function gp(n,e){const t=e.length;for(let r=0;r<t;r++){const i=e[r],s=X(n,i,void 0,{type:cp}),a=X(n,i,void 0,{type:ir});s.stop=a,n.ruleToStartState.set(i,s),n.ruleToStopState.set(i,a)}}function $u(n,e,t){return t instanceof G?Zs(n,e,t.terminalType,t):t instanceof le?Ip(n,e,t):t instanceof ge?Ep(n,e,t):t instanceof ne?vp(n,e,t):t instanceof j?yp(n,e,t):t instanceof me?Tp(n,e,t):t instanceof xe?Rp(n,e,t):t instanceof Ie?Ap(n,e,t):At(n,e,t)}function yp(n,e,t){const r=X(n,e,t,{type:xu});at(n,r);const i=rn(n,e,r,t,At(n,e,t));return wu(n,e,t,i)}function Tp(n,e,t){const r=X(n,e,t,{type:xu});at(n,r);const i=rn(n,e,r,t,At(n,e,t)),s=Zs(n,e,t.separator,t);return wu(n,e,t,i,s)}function Rp(n,e,t){const r=X(n,e,t,{type:Su});at(n,r);const i=rn(n,e,r,t,At(n,e,t));return Nu(n,e,t,i)}function Ap(n,e,t){const r=X(n,e,t,{type:Su});at(n,r);const i=rn(n,e,r,t,At(n,e,t)),s=Zs(n,e,t.separator,t);return Nu(n,e,t,i,s)}function Ep(n,e,t){const r=X(n,e,t,{type:rt});at(n,r);const i=x(t.definition,a=>$u(n,e,a));return rn(n,e,r,t,...i)}function vp(n,e,t){const r=X(n,e,t,{type:rt});at(n,r);const i=rn(n,e,r,t,At(n,e,t));return kp(n,e,t,i)}function At(n,e,t){const r=Se(x(t.definition,i=>$u(n,e,i)),i=>i!==void 0);return r.length===1?r[0]:r.length===0?void 0:xp(n,r)}function Nu(n,e,t,r,i){const s=r.left,a=r.right,o=X(n,e,t,{type:pp});at(n,o);const l=X(n,e,t,{type:Iu});return s.loopback=o,l.loopback=o,n.decisionMap[Yt(e,i?"RepetitionMandatoryWithSeparator":"RepetitionMandatory",t.idx)]=o,H(a,o),i===void 0?(H(o,s),H(o,l)):(H(o,l),H(o,i.left),H(i.right,s)),{left:s,right:l}}function wu(n,e,t,r,i){const s=r.left,a=r.right,o=X(n,e,t,{type:hp});at(n,o);const l=X(n,e,t,{type:Iu}),u=X(n,e,t,{type:fp});return o.loopback=u,l.loopback=u,H(o,s),H(o,l),H(a,u),i!==void 0?(H(u,l),H(u,i.left),H(i.right,s)):H(u,o),n.decisionMap[Yt(e,i?"RepetitionWithSeparator":"Repetition",t.idx)]=o,{left:o,right:l}}function kp(n,e,t,r){const i=r.left,s=r.right;return H(i,s),n.decisionMap[Yt(e,"Option",t.idx)]=i,r}function at(n,e){return n.decisionStates.push(e),e.decision=n.decisionStates.length-1,e.decision}function rn(n,e,t,r,...i){const s=X(n,e,r,{type:dp,start:t});t.end=s;for(const o of i)o!==void 0?(H(t,o.left),H(o.right,s)):H(t,s);const a={left:t,right:s};return n.decisionMap[Yt(e,Sp(r),r.idx)]=t,a}function Sp(n){if(n instanceof ge)return"Alternation";if(n instanceof ne)return"Option";if(n instanceof j)return"Repetition";if(n instanceof me)return"RepetitionWithSeparator";if(n instanceof xe)return"RepetitionMandatory";if(n instanceof Ie)return"RepetitionMandatoryWithSeparator";throw new Error("Invalid production type encountered")}function xp(n,e){const t=e.length;for(let s=0;s<t-1;s++){const a=e[s];let o;a.left.transitions.length===1&&(o=a.left.transitions[0]);const l=o instanceof Qs,u=o,c=e[s+1].left;a.left.type===rt&&a.right.type===rt&&o!==void 0&&(l&&u.followState===a.right||o.target===a.right)?(l?u.followState=c:o.target=c,$p(n,a.right)):H(a.right,c)}const r=e[0],i=e[t-1];return{left:r.left,right:i.right}}function Zs(n,e,t,r){const i=X(n,e,r,{type:rt}),s=X(n,e,r,{type:rt});return ea(i,new Js(s,t)),{left:i,right:s}}function Ip(n,e,t){const r=t.referencedRule,i=n.ruleToStartState.get(r),s=X(n,e,t,{type:rt}),a=X(n,e,t,{type:rt}),o=new Qs(i,r,a);return ea(s,o),{left:s,right:a}}function Cp(n,e,t){const r=n.ruleToStartState.get(e);H(r,t.left);const i=n.ruleToStopState.get(e);return H(t.right,i),{left:r,right:i}}function H(n,e){const t=new Cu(e);ea(n,t)}function X(n,e,t,r){const i=Object.assign({atn:n,production:t,epsilonOnlyTransitions:!1,rule:e,transitions:[],nextTokenWithinRule:[],stateNumber:n.states.length},r);return n.states.push(i),i}function ea(n,e){n.transitions.length===0&&(n.epsilonOnlyTransitions=e.isEpsilon()),n.transitions.push(e)}function $p(n,e){n.states.splice(n.states.indexOf(e),1)}const Xr={};class ds{constructor(){this.map={},this.configs=[]}get size(){return this.configs.length}finalize(){this.map={}}add(e){const t=_u(e);t in this.map||(this.map[t]=this.configs.length,this.configs.push(e))}get elements(){return this.configs}get alts(){return x(this.configs,e=>e.alt)}get key(){let e="";for(const t in this.map)e+=t+":";return e}}function _u(n,e=!0){return`${e?`a${n.alt}`:""}s${n.state.stateNumber}:${n.stack.map(t=>t.stateNumber.toString()).join("_")}`}function Np(n,e){const t={};return r=>{const i=r.toString();let s=t[i];return s!==void 0||(s={atnStartState:n,decision:e,states:{}},t[i]=s),s}}class Lu{constructor(){this.predicates=[]}is(e){return e>=this.predicates.length||this.predicates[e]}set(e,t){this.predicates[e]=t}toString(){let e="";const t=this.predicates.length;for(let r=0;r<t;r++)e+=this.predicates[r]===!0?"1":"0";return e}}const Wa=new Lu;class wp extends Ys{constructor(e){var t;super(),this.logging=(t=e==null?void 0:e.logging)!==null&&t!==void 0?t:r=>console.log(r)}initialize(e){this.atn=mp(e.rules),this.dfas=_p(this.atn)}validateAmbiguousAlternationAlternatives(){return[]}validateEmptyOrAlternatives(){return[]}buildLookaheadForAlternation(e){const{prodOccurrence:t,rule:r,hasPredicates:i,dynamicTokensEnabled:s}=e,a=this.dfas,o=this.logging,l=Yt(r,"Alternation",t),c=this.atn.decisionMap[l].decision,d=x(Pa({maxLookahead:1,occurrence:t,prodType:"Alternation",rule:r}),h=>x(h,f=>f[0]));if(ja(d,!1)&&!s){const h=oe(d,(f,m,g)=>($(m,A=>{A&&(f[A.tokenTypeIdx]=g,$(A.categoryMatches,y=>{f[y]=g}))}),f),{});return i?function(f){var m;const g=this.LA(1),A=h[g.tokenTypeIdx];if(f!==void 0&&A!==void 0){const y=(m=f[A])===null||m===void 0?void 0:m.GATE;if(y!==void 0&&y.call(this)===!1)return}return A}:function(){const f=this.LA(1);return h[f.tokenTypeIdx]}}else return i?function(h){const f=new Lu,m=h===void 0?0:h.length;for(let A=0;A<m;A++){const y=h==null?void 0:h[A].GATE;f.set(A,y===void 0||y.call(this))}const g=Mi.call(this,a,c,f,o);return typeof g=="number"?g:void 0}:function(){const h=Mi.call(this,a,c,Wa,o);return typeof h=="number"?h:void 0}}buildLookaheadForOptional(e){const{prodOccurrence:t,rule:r,prodType:i,dynamicTokensEnabled:s}=e,a=this.dfas,o=this.logging,l=Yt(r,i,t),c=this.atn.decisionMap[l].decision,d=x(Pa({maxLookahead:1,occurrence:t,prodType:i,rule:r}),h=>x(h,f=>f[0]));if(ja(d)&&d[0][0]&&!s){const h=d[0],f=Ne(h);if(f.length===1&&D(f[0].categoryMatches)){const g=f[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===g}}else{const m=oe(f,(g,A)=>(A!==void 0&&(g[A.tokenTypeIdx]=!0,$(A.categoryMatches,y=>{g[y]=!0})),g),{});return function(){const g=this.LA(1);return m[g.tokenTypeIdx]===!0}}}return function(){const h=Mi.call(this,a,c,Wa,o);return typeof h=="object"?!1:h===0}}}function ja(n,e=!0){const t=new Set;for(const r of n){const i=new Set;for(const s of r){if(s===void 0){if(e)break;return!1}const a=[s.tokenTypeIdx].concat(s.categoryMatches);for(const o of a)if(t.has(o)){if(!i.has(o))return!1}else t.add(o),i.add(o)}}return!0}function _p(n){const e=n.decisionStates.length,t=Array(e);for(let r=0;r<e;r++)t[r]=Np(n.decisionStates[r],r);return t}function Mi(n,e,t,r){const i=n[e](t);let s=i.start;if(s===void 0){const o=Vp(i.atnStartState);s=bu(i,Ou(o)),i.start=s}return Lp.apply(this,[i,s,t,r])}function Lp(n,e,t,r){let i=e,s=1;const a=[];let o=this.LA(s++);for(;;){let l=Fp(i,o);if(l===void 0&&(l=Op.apply(this,[n,i,o,s,t,r])),l===Xr)return Dp(a,i,o);if(l.isAcceptState===!0)return l.prediction;i=l,a.push(o),o=this.LA(s++)}}function Op(n,e,t,r,i,s){const a=Gp(e.configs,t,i);if(a.size===0)return Ka(n,e,t,Xr),Xr;let o=Ou(a);const l=Bp(a,i);if(l!==void 0)o.isAcceptState=!0,o.prediction=l,o.configs.uniqueAlt=l;else if(Hp(a)){const u=Fc(a.alts);o.isAcceptState=!0,o.prediction=u,o.configs.uniqueAlt=u,bp.apply(this,[n,r,a.alts,s])}return o=Ka(n,e,t,o),o}function bp(n,e,t,r){const i=[];for(let u=1;u<=e;u++)i.push(this.LA(u).tokenType);const s=n.atnStartState,a=s.rule,o=s.production,l=Pp({topLevelRule:a,ambiguityIndices:t,production:o,prefixPath:i});r(l)}function Pp(n){const e=x(n.prefixPath,i=>_t(i)).join(", "),t=n.production.idx===0?"":n.production.idx;let r=`Ambiguous Alternatives Detected: <${n.ambiguityIndices.join(", ")}> in <${Mp(n.production)}${t}> inside <${n.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return r=r+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,r}function Mp(n){if(n instanceof le)return"SUBRULE";if(n instanceof ne)return"OPTION";if(n instanceof ge)return"OR";if(n instanceof xe)return"AT_LEAST_ONE";if(n instanceof Ie)return"AT_LEAST_ONE_SEP";if(n instanceof me)return"MANY_SEP";if(n instanceof j)return"MANY";if(n instanceof G)return"CONSUME";throw Error("non exhaustive match")}function Dp(n,e,t){const r=ve(e.configs.elements,s=>s.state.transitions),i=id(r.filter(s=>s instanceof Js).map(s=>s.tokenType),s=>s.tokenTypeIdx);return{actualToken:t,possibleTokenTypes:i,tokenPath:n}}function Fp(n,e){return n.edges[e.tokenTypeIdx]}function Gp(n,e,t){const r=new ds,i=[];for(const a of n.elements){if(t.is(a.alt)===!1)continue;if(a.state.type===ir){i.push(a);continue}const o=a.state.transitions.length;for(let l=0;l<o;l++){const u=a.state.transitions[l],c=Up(u,e);c!==void 0&&r.add({state:c,alt:a.alt,stack:a.stack})}}let s;if(i.length===0&&r.size===1&&(s=r),s===void 0){s=new ds;for(const a of r.elements)Jr(a,s)}if(i.length>0&&!jp(s))for(const a of i)s.add(a);return s}function Up(n,e){if(n instanceof Js&&su(e,n.tokenType))return n.target}function Bp(n,e){let t;for(const r of n.elements)if(e.is(r.alt)===!0){if(t===void 0)t=r.alt;else if(t!==r.alt)return}return t}function Ou(n){return{configs:n,edges:{},isAcceptState:!1,prediction:-1}}function Ka(n,e,t,r){return r=bu(n,r),e.edges[t.tokenTypeIdx]=r,r}function bu(n,e){if(e===Xr)return e;const t=e.configs.key,r=n.states[t];return r!==void 0?r:(e.configs.finalize(),n.states[t]=e,e)}function Vp(n){const e=new ds,t=n.transitions.length;for(let r=0;r<t;r++){const s={state:n.transitions[r].target,alt:r,stack:[]};Jr(s,e)}return e}function Jr(n,e){const t=n.state;if(t.type===ir){if(n.stack.length>0){const i=[...n.stack],a={state:i.pop(),alt:n.alt,stack:i};Jr(a,e)}else e.add(n);return}t.epsilonOnlyTransitions||e.add(n);const r=t.transitions.length;for(let i=0;i<r;i++){const s=t.transitions[i],a=Wp(n,s);a!==void 0&&Jr(a,e)}}function Wp(n,e){if(e instanceof Cu)return{state:e.target,alt:n.alt,stack:n.stack};if(e instanceof Qs){const t=[...n.stack,e.followState];return{state:e.target,alt:n.alt,stack:t}}}function jp(n){for(const e of n.elements)if(e.state.type===ir)return!0;return!1}function Kp(n){for(const e of n.elements)if(e.state.type!==ir)return!1;return!0}function Hp(n){if(Kp(n))return!0;const e=zp(n.elements);return qp(e)&&!Yp(e)}function zp(n){const e=new Map;for(const t of n){const r=_u(t,!1);let i=e.get(r);i===void 0&&(i={},e.set(r,i)),i[t.alt]=!0}return e}function qp(n){for(const e of Array.from(n.values()))if(Object.keys(e).length>1)return!0;return!1}function Yp(n){for(const e of Array.from(n.values()))if(Object.keys(e).length===1)return!0;return!1}var Ha;(function(n){function e(t){return typeof t=="string"}n.is=e})(Ha||(Ha={}));var fs;(function(n){function e(t){return typeof t=="string"}n.is=e})(fs||(fs={}));var za;(function(n){n.MIN_VALUE=-2147483648,n.MAX_VALUE=2147483647;function e(t){return typeof t=="number"&&n.MIN_VALUE<=t&&t<=n.MAX_VALUE}n.is=e})(za||(za={}));var Qr;(function(n){n.MIN_VALUE=0,n.MAX_VALUE=2147483647;function e(t){return typeof t=="number"&&n.MIN_VALUE<=t&&t<=n.MAX_VALUE}n.is=e})(Qr||(Qr={}));var P;(function(n){function e(r,i){return r===Number.MAX_VALUE&&(r=Qr.MAX_VALUE),i===Number.MAX_VALUE&&(i=Qr.MAX_VALUE),{line:r,character:i}}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&p.uinteger(i.line)&&p.uinteger(i.character)}n.is=t})(P||(P={}));var b;(function(n){function e(r,i,s,a){if(p.uinteger(r)&&p.uinteger(i)&&p.uinteger(s)&&p.uinteger(a))return{start:P.create(r,i),end:P.create(s,a)};if(P.is(r)&&P.is(i))return{start:r,end:i};throw new Error(`Range#create called with invalid arguments[${r}, ${i}, ${s}, ${a}]`)}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&P.is(i.start)&&P.is(i.end)}n.is=t})(b||(b={}));var Zr;(function(n){function e(r,i){return{uri:r,range:i}}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&b.is(i.range)&&(p.string(i.uri)||p.undefined(i.uri))}n.is=t})(Zr||(Zr={}));var qa;(function(n){function e(r,i,s,a){return{targetUri:r,targetRange:i,targetSelectionRange:s,originSelectionRange:a}}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&b.is(i.targetRange)&&p.string(i.targetUri)&&b.is(i.targetSelectionRange)&&(b.is(i.originSelectionRange)||p.undefined(i.originSelectionRange))}n.is=t})(qa||(qa={}));var hs;(function(n){function e(r,i,s,a){return{red:r,green:i,blue:s,alpha:a}}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&p.numberRange(i.red,0,1)&&p.numberRange(i.green,0,1)&&p.numberRange(i.blue,0,1)&&p.numberRange(i.alpha,0,1)}n.is=t})(hs||(hs={}));var Ya;(function(n){function e(r,i){return{range:r,color:i}}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&b.is(i.range)&&hs.is(i.color)}n.is=t})(Ya||(Ya={}));var Xa;(function(n){function e(r,i,s){return{label:r,textEdit:i,additionalTextEdits:s}}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&p.string(i.label)&&(p.undefined(i.textEdit)||Jt.is(i))&&(p.undefined(i.additionalTextEdits)||p.typedArray(i.additionalTextEdits,Jt.is))}n.is=t})(Xa||(Xa={}));var Ja;(function(n){n.Comment="comment",n.Imports="imports",n.Region="region"})(Ja||(Ja={}));var Qa;(function(n){function e(r,i,s,a,o,l){const u={startLine:r,endLine:i};return p.defined(s)&&(u.startCharacter=s),p.defined(a)&&(u.endCharacter=a),p.defined(o)&&(u.kind=o),p.defined(l)&&(u.collapsedText=l),u}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&p.uinteger(i.startLine)&&p.uinteger(i.startLine)&&(p.undefined(i.startCharacter)||p.uinteger(i.startCharacter))&&(p.undefined(i.endCharacter)||p.uinteger(i.endCharacter))&&(p.undefined(i.kind)||p.string(i.kind))}n.is=t})(Qa||(Qa={}));var ps;(function(n){function e(r,i){return{location:r,message:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&Zr.is(i.location)&&p.string(i.message)}n.is=t})(ps||(ps={}));var Za;(function(n){n.Error=1,n.Warning=2,n.Information=3,n.Hint=4})(Za||(Za={}));var eo;(function(n){n.Unnecessary=1,n.Deprecated=2})(eo||(eo={}));var to;(function(n){function e(t){const r=t;return p.objectLiteral(r)&&p.string(r.href)}n.is=e})(to||(to={}));var ei;(function(n){function e(r,i,s,a,o,l){let u={range:r,message:i};return p.defined(s)&&(u.severity=s),p.defined(a)&&(u.code=a),p.defined(o)&&(u.source=o),p.defined(l)&&(u.relatedInformation=l),u}n.create=e;function t(r){var i;let s=r;return p.defined(s)&&b.is(s.range)&&p.string(s.message)&&(p.number(s.severity)||p.undefined(s.severity))&&(p.integer(s.code)||p.string(s.code)||p.undefined(s.code))&&(p.undefined(s.codeDescription)||p.string((i=s.codeDescription)===null||i===void 0?void 0:i.href))&&(p.string(s.source)||p.undefined(s.source))&&(p.undefined(s.relatedInformation)||p.typedArray(s.relatedInformation,ps.is))}n.is=t})(ei||(ei={}));var Xt;(function(n){function e(r,i,...s){let a={title:r,command:i};return p.defined(s)&&s.length>0&&(a.arguments=s),a}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.title)&&p.string(i.command)}n.is=t})(Xt||(Xt={}));var Jt;(function(n){function e(s,a){return{range:s,newText:a}}n.replace=e;function t(s,a){return{range:{start:s,end:s},newText:a}}n.insert=t;function r(s){return{range:s,newText:""}}n.del=r;function i(s){const a=s;return p.objectLiteral(a)&&p.string(a.newText)&&b.is(a.range)}n.is=i})(Jt||(Jt={}));var ms;(function(n){function e(r,i,s){const a={label:r};return i!==void 0&&(a.needsConfirmation=i),s!==void 0&&(a.description=s),a}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&p.string(i.label)&&(p.boolean(i.needsConfirmation)||i.needsConfirmation===void 0)&&(p.string(i.description)||i.description===void 0)}n.is=t})(ms||(ms={}));var Qt;(function(n){function e(t){const r=t;return p.string(r)}n.is=e})(Qt||(Qt={}));var no;(function(n){function e(s,a,o){return{range:s,newText:a,annotationId:o}}n.replace=e;function t(s,a,o){return{range:{start:s,end:s},newText:a,annotationId:o}}n.insert=t;function r(s,a){return{range:s,newText:"",annotationId:a}}n.del=r;function i(s){const a=s;return Jt.is(a)&&(ms.is(a.annotationId)||Qt.is(a.annotationId))}n.is=i})(no||(no={}));var gs;(function(n){function e(r,i){return{textDocument:r,edits:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&Es.is(i.textDocument)&&Array.isArray(i.edits)}n.is=t})(gs||(gs={}));var ys;(function(n){function e(r,i,s){let a={kind:"create",uri:r};return i!==void 0&&(i.overwrite!==void 0||i.ignoreIfExists!==void 0)&&(a.options=i),s!==void 0&&(a.annotationId=s),a}n.create=e;function t(r){let i=r;return i&&i.kind==="create"&&p.string(i.uri)&&(i.options===void 0||(i.options.overwrite===void 0||p.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||p.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||Qt.is(i.annotationId))}n.is=t})(ys||(ys={}));var Ts;(function(n){function e(r,i,s,a){let o={kind:"rename",oldUri:r,newUri:i};return s!==void 0&&(s.overwrite!==void 0||s.ignoreIfExists!==void 0)&&(o.options=s),a!==void 0&&(o.annotationId=a),o}n.create=e;function t(r){let i=r;return i&&i.kind==="rename"&&p.string(i.oldUri)&&p.string(i.newUri)&&(i.options===void 0||(i.options.overwrite===void 0||p.boolean(i.options.overwrite))&&(i.options.ignoreIfExists===void 0||p.boolean(i.options.ignoreIfExists)))&&(i.annotationId===void 0||Qt.is(i.annotationId))}n.is=t})(Ts||(Ts={}));var Rs;(function(n){function e(r,i,s){let a={kind:"delete",uri:r};return i!==void 0&&(i.recursive!==void 0||i.ignoreIfNotExists!==void 0)&&(a.options=i),s!==void 0&&(a.annotationId=s),a}n.create=e;function t(r){let i=r;return i&&i.kind==="delete"&&p.string(i.uri)&&(i.options===void 0||(i.options.recursive===void 0||p.boolean(i.options.recursive))&&(i.options.ignoreIfNotExists===void 0||p.boolean(i.options.ignoreIfNotExists)))&&(i.annotationId===void 0||Qt.is(i.annotationId))}n.is=t})(Rs||(Rs={}));var As;(function(n){function e(t){let r=t;return r&&(r.changes!==void 0||r.documentChanges!==void 0)&&(r.documentChanges===void 0||r.documentChanges.every(i=>p.string(i.kind)?ys.is(i)||Ts.is(i)||Rs.is(i):gs.is(i)))}n.is=e})(As||(As={}));var ro;(function(n){function e(r){return{uri:r}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.uri)}n.is=t})(ro||(ro={}));var io;(function(n){function e(r,i){return{uri:r,version:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.uri)&&p.integer(i.version)}n.is=t})(io||(io={}));var Es;(function(n){function e(r,i){return{uri:r,version:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.uri)&&(i.version===null||p.integer(i.version))}n.is=t})(Es||(Es={}));var so;(function(n){function e(r,i,s,a){return{uri:r,languageId:i,version:s,text:a}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.string(i.uri)&&p.string(i.languageId)&&p.integer(i.version)&&p.string(i.text)}n.is=t})(so||(so={}));var vs;(function(n){n.PlainText="plaintext",n.Markdown="markdown";function e(t){const r=t;return r===n.PlainText||r===n.Markdown}n.is=e})(vs||(vs={}));var Yn;(function(n){function e(t){const r=t;return p.objectLiteral(t)&&vs.is(r.kind)&&p.string(r.value)}n.is=e})(Yn||(Yn={}));var ao;(function(n){n.Text=1,n.Method=2,n.Function=3,n.Constructor=4,n.Field=5,n.Variable=6,n.Class=7,n.Interface=8,n.Module=9,n.Property=10,n.Unit=11,n.Value=12,n.Enum=13,n.Keyword=14,n.Snippet=15,n.Color=16,n.File=17,n.Reference=18,n.Folder=19,n.EnumMember=20,n.Constant=21,n.Struct=22,n.Event=23,n.Operator=24,n.TypeParameter=25})(ao||(ao={}));var oo;(function(n){n.PlainText=1,n.Snippet=2})(oo||(oo={}));var lo;(function(n){n.Deprecated=1})(lo||(lo={}));var uo;(function(n){function e(r,i,s){return{newText:r,insert:i,replace:s}}n.create=e;function t(r){const i=r;return i&&p.string(i.newText)&&b.is(i.insert)&&b.is(i.replace)}n.is=t})(uo||(uo={}));var co;(function(n){n.asIs=1,n.adjustIndentation=2})(co||(co={}));var fo;(function(n){function e(t){const r=t;return r&&(p.string(r.detail)||r.detail===void 0)&&(p.string(r.description)||r.description===void 0)}n.is=e})(fo||(fo={}));var ho;(function(n){function e(t){return{label:t}}n.create=e})(ho||(ho={}));var po;(function(n){function e(t,r){return{items:t||[],isIncomplete:!!r}}n.create=e})(po||(po={}));var ti;(function(n){function e(r){return r.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}n.fromPlainText=e;function t(r){const i=r;return p.string(i)||p.objectLiteral(i)&&p.string(i.language)&&p.string(i.value)}n.is=t})(ti||(ti={}));var mo;(function(n){function e(t){let r=t;return!!r&&p.objectLiteral(r)&&(Yn.is(r.contents)||ti.is(r.contents)||p.typedArray(r.contents,ti.is))&&(t.range===void 0||b.is(t.range))}n.is=e})(mo||(mo={}));var go;(function(n){function e(t,r){return r?{label:t,documentation:r}:{label:t}}n.create=e})(go||(go={}));var yo;(function(n){function e(t,r,...i){let s={label:t};return p.defined(r)&&(s.documentation=r),p.defined(i)?s.parameters=i:s.parameters=[],s}n.create=e})(yo||(yo={}));var To;(function(n){n.Text=1,n.Read=2,n.Write=3})(To||(To={}));var Ro;(function(n){function e(t,r){let i={range:t};return p.number(r)&&(i.kind=r),i}n.create=e})(Ro||(Ro={}));var Ao;(function(n){n.File=1,n.Module=2,n.Namespace=3,n.Package=4,n.Class=5,n.Method=6,n.Property=7,n.Field=8,n.Constructor=9,n.Enum=10,n.Interface=11,n.Function=12,n.Variable=13,n.Constant=14,n.String=15,n.Number=16,n.Boolean=17,n.Array=18,n.Object=19,n.Key=20,n.Null=21,n.EnumMember=22,n.Struct=23,n.Event=24,n.Operator=25,n.TypeParameter=26})(Ao||(Ao={}));var Eo;(function(n){n.Deprecated=1})(Eo||(Eo={}));var vo;(function(n){function e(t,r,i,s,a){let o={name:t,kind:r,location:{uri:s,range:i}};return a&&(o.containerName=a),o}n.create=e})(vo||(vo={}));var ko;(function(n){function e(t,r,i,s){return s!==void 0?{name:t,kind:r,location:{uri:i,range:s}}:{name:t,kind:r,location:{uri:i}}}n.create=e})(ko||(ko={}));var So;(function(n){function e(r,i,s,a,o,l){let u={name:r,detail:i,kind:s,range:a,selectionRange:o};return l!==void 0&&(u.children=l),u}n.create=e;function t(r){let i=r;return i&&p.string(i.name)&&p.number(i.kind)&&b.is(i.range)&&b.is(i.selectionRange)&&(i.detail===void 0||p.string(i.detail))&&(i.deprecated===void 0||p.boolean(i.deprecated))&&(i.children===void 0||Array.isArray(i.children))&&(i.tags===void 0||Array.isArray(i.tags))}n.is=t})(So||(So={}));var xo;(function(n){n.Empty="",n.QuickFix="quickfix",n.Refactor="refactor",n.RefactorExtract="refactor.extract",n.RefactorInline="refactor.inline",n.RefactorRewrite="refactor.rewrite",n.Source="source",n.SourceOrganizeImports="source.organizeImports",n.SourceFixAll="source.fixAll"})(xo||(xo={}));var ni;(function(n){n.Invoked=1,n.Automatic=2})(ni||(ni={}));var Io;(function(n){function e(r,i,s){let a={diagnostics:r};return i!=null&&(a.only=i),s!=null&&(a.triggerKind=s),a}n.create=e;function t(r){let i=r;return p.defined(i)&&p.typedArray(i.diagnostics,ei.is)&&(i.only===void 0||p.typedArray(i.only,p.string))&&(i.triggerKind===void 0||i.triggerKind===ni.Invoked||i.triggerKind===ni.Automatic)}n.is=t})(Io||(Io={}));var Co;(function(n){function e(r,i,s){let a={title:r},o=!0;return typeof i=="string"?(o=!1,a.kind=i):Xt.is(i)?a.command=i:a.edit=i,o&&s!==void 0&&(a.kind=s),a}n.create=e;function t(r){let i=r;return i&&p.string(i.title)&&(i.diagnostics===void 0||p.typedArray(i.diagnostics,ei.is))&&(i.kind===void 0||p.string(i.kind))&&(i.edit!==void 0||i.command!==void 0)&&(i.command===void 0||Xt.is(i.command))&&(i.isPreferred===void 0||p.boolean(i.isPreferred))&&(i.edit===void 0||As.is(i.edit))}n.is=t})(Co||(Co={}));var $o;(function(n){function e(r,i){let s={range:r};return p.defined(i)&&(s.data=i),s}n.create=e;function t(r){let i=r;return p.defined(i)&&b.is(i.range)&&(p.undefined(i.command)||Xt.is(i.command))}n.is=t})($o||($o={}));var No;(function(n){function e(r,i){return{tabSize:r,insertSpaces:i}}n.create=e;function t(r){let i=r;return p.defined(i)&&p.uinteger(i.tabSize)&&p.boolean(i.insertSpaces)}n.is=t})(No||(No={}));var wo;(function(n){function e(r,i,s){return{range:r,target:i,data:s}}n.create=e;function t(r){let i=r;return p.defined(i)&&b.is(i.range)&&(p.undefined(i.target)||p.string(i.target))}n.is=t})(wo||(wo={}));var _o;(function(n){function e(r,i){return{range:r,parent:i}}n.create=e;function t(r){let i=r;return p.objectLiteral(i)&&b.is(i.range)&&(i.parent===void 0||n.is(i.parent))}n.is=t})(_o||(_o={}));var Lo;(function(n){n.namespace="namespace",n.type="type",n.class="class",n.enum="enum",n.interface="interface",n.struct="struct",n.typeParameter="typeParameter",n.parameter="parameter",n.variable="variable",n.property="property",n.enumMember="enumMember",n.event="event",n.function="function",n.method="method",n.macro="macro",n.keyword="keyword",n.modifier="modifier",n.comment="comment",n.string="string",n.number="number",n.regexp="regexp",n.operator="operator",n.decorator="decorator"})(Lo||(Lo={}));var Oo;(function(n){n.declaration="declaration",n.definition="definition",n.readonly="readonly",n.static="static",n.deprecated="deprecated",n.abstract="abstract",n.async="async",n.modification="modification",n.documentation="documentation",n.defaultLibrary="defaultLibrary"})(Oo||(Oo={}));var bo;(function(n){function e(t){const r=t;return p.objectLiteral(r)&&(r.resultId===void 0||typeof r.resultId=="string")&&Array.isArray(r.data)&&(r.data.length===0||typeof r.data[0]=="number")}n.is=e})(bo||(bo={}));var Po;(function(n){function e(r,i){return{range:r,text:i}}n.create=e;function t(r){const i=r;return i!=null&&b.is(i.range)&&p.string(i.text)}n.is=t})(Po||(Po={}));var Mo;(function(n){function e(r,i,s){return{range:r,variableName:i,caseSensitiveLookup:s}}n.create=e;function t(r){const i=r;return i!=null&&b.is(i.range)&&p.boolean(i.caseSensitiveLookup)&&(p.string(i.variableName)||i.variableName===void 0)}n.is=t})(Mo||(Mo={}));var Do;(function(n){function e(r,i){return{range:r,expression:i}}n.create=e;function t(r){const i=r;return i!=null&&b.is(i.range)&&(p.string(i.expression)||i.expression===void 0)}n.is=t})(Do||(Do={}));var Fo;(function(n){function e(r,i){return{frameId:r,stoppedLocation:i}}n.create=e;function t(r){const i=r;return p.defined(i)&&b.is(r.stoppedLocation)}n.is=t})(Fo||(Fo={}));var ks;(function(n){n.Type=1,n.Parameter=2;function e(t){return t===1||t===2}n.is=e})(ks||(ks={}));var Ss;(function(n){function e(r){return{value:r}}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&(i.tooltip===void 0||p.string(i.tooltip)||Yn.is(i.tooltip))&&(i.location===void 0||Zr.is(i.location))&&(i.command===void 0||Xt.is(i.command))}n.is=t})(Ss||(Ss={}));var Go;(function(n){function e(r,i,s){const a={position:r,label:i};return s!==void 0&&(a.kind=s),a}n.create=e;function t(r){const i=r;return p.objectLiteral(i)&&P.is(i.position)&&(p.string(i.label)||p.typedArray(i.label,Ss.is))&&(i.kind===void 0||ks.is(i.kind))&&i.textEdits===void 0||p.typedArray(i.textEdits,Jt.is)&&(i.tooltip===void 0||p.string(i.tooltip)||Yn.is(i.tooltip))&&(i.paddingLeft===void 0||p.boolean(i.paddingLeft))&&(i.paddingRight===void 0||p.boolean(i.paddingRight))}n.is=t})(Go||(Go={}));var Uo;(function(n){function e(t){return{kind:"snippet",value:t}}n.createSnippet=e})(Uo||(Uo={}));var Bo;(function(n){function e(t,r,i,s){return{insertText:t,filterText:r,range:i,command:s}}n.create=e})(Bo||(Bo={}));var Vo;(function(n){function e(t){return{items:t}}n.create=e})(Vo||(Vo={}));var Wo;(function(n){n.Invoked=0,n.Automatic=1})(Wo||(Wo={}));var jo;(function(n){function e(t,r){return{range:t,text:r}}n.create=e})(jo||(jo={}));var Ko;(function(n){function e(t,r){return{triggerKind:t,selectedCompletionInfo:r}}n.create=e})(Ko||(Ko={}));var Ho;(function(n){function e(t){const r=t;return p.objectLiteral(r)&&fs.is(r.uri)&&p.string(r.name)}n.is=e})(Ho||(Ho={}));var zo;(function(n){function e(s,a,o,l){return new Xp(s,a,o,l)}n.create=e;function t(s){let a=s;return!!(p.defined(a)&&p.string(a.uri)&&(p.undefined(a.languageId)||p.string(a.languageId))&&p.uinteger(a.lineCount)&&p.func(a.getText)&&p.func(a.positionAt)&&p.func(a.offsetAt))}n.is=t;function r(s,a){let o=s.getText(),l=i(a,(c,d)=>{let h=c.range.start.line-d.range.start.line;return h===0?c.range.start.character-d.range.start.character:h}),u=o.length;for(let c=l.length-1;c>=0;c--){let d=l[c],h=s.offsetAt(d.range.start),f=s.offsetAt(d.range.end);if(f<=u)o=o.substring(0,h)+d.newText+o.substring(f,o.length);else throw new Error("Overlapping edit");u=h}return o}n.applyEdits=r;function i(s,a){if(s.length<=1)return s;const o=s.length/2|0,l=s.slice(0,o),u=s.slice(o);i(l,a),i(u,a);let c=0,d=0,h=0;for(;c<l.length&&d<u.length;)a(l[c],u[d])<=0?s[h++]=l[c++]:s[h++]=u[d++];for(;c<l.length;)s[h++]=l[c++];for(;d<u.length;)s[h++]=u[d++];return s}})(zo||(zo={}));let Xp=class{constructor(e,t,r,i){this._uri=e,this._languageId=t,this._version=r,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(t,r)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let e=[],t=this._content,r=!0;for(let i=0;i<t.length;i++){r&&(e.push(i),r=!1);let s=t.charAt(i);r=s==="\r"||s===`
`,s==="\r"&&i+1<t.length&&t.charAt(i+1)===`
`&&i++}r&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),r=0,i=t.length;if(i===0)return P.create(0,e);for(;r<i;){let a=Math.floor((r+i)/2);t[a]>e?i=a:r=a+1}let s=r-1;return P.create(s,e-t[s])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let r=t[e.line],i=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(r+e.character,i),r)}get lineCount(){return this.getLineOffsets().length}};var p;(function(n){const e=Object.prototype.toString;function t(f){return typeof f<"u"}n.defined=t;function r(f){return typeof f>"u"}n.undefined=r;function i(f){return f===!0||f===!1}n.boolean=i;function s(f){return e.call(f)==="[object String]"}n.string=s;function a(f){return e.call(f)==="[object Number]"}n.number=a;function o(f,m,g){return e.call(f)==="[object Number]"&&m<=f&&f<=g}n.numberRange=o;function l(f){return e.call(f)==="[object Number]"&&-2147483648<=f&&f<=2147483647}n.integer=l;function u(f){return e.call(f)==="[object Number]"&&0<=f&&f<=2147483647}n.uinteger=u;function c(f){return e.call(f)==="[object Function]"}n.func=c;function d(f){return f!==null&&typeof f=="object"}n.objectLiteral=d;function h(f,m){return Array.isArray(f)&&f.every(m)}n.typedArray=h})(p||(p={}));class Jp{constructor(){this.nodeStack=[]}get current(){var e;return(e=this.nodeStack[this.nodeStack.length-1])!==null&&e!==void 0?e:this.rootNode}buildRootNode(e){return this.rootNode=new Mu(e),this.rootNode.root=this.rootNode,this.nodeStack=[this.rootNode],this.rootNode}buildCompositeNode(e){const t=new ta;return t.grammarSource=e,t.root=this.rootNode,this.current.content.push(t),this.nodeStack.push(t),t}buildLeafNode(e,t){const r=new xs(e.startOffset,e.image.length,Qi(e),e.tokenType,!t);return r.grammarSource=t,r.root=this.rootNode,this.current.content.push(r),r}removeNode(e){const t=e.container;if(t){const r=t.content.indexOf(e);r>=0&&t.content.splice(r,1)}}addHiddenNodes(e){const t=[];for(const s of e){const a=new xs(s.startOffset,s.image.length,Qi(s),s.tokenType,!0);a.root=this.rootNode,t.push(a)}let r=this.current,i=!1;if(r.content.length>0){r.content.push(...t);return}for(;r.container;){const s=r.container.content.indexOf(r);if(s>0){r.container.content.splice(s,0,...t),i=!0;break}r=r.container}i||this.rootNode.content.unshift(...t)}construct(e){const t=this.current;typeof e.$type=="string"&&(this.current.astNode=e),e.$cstNode=t;const r=this.nodeStack.pop();(r==null?void 0:r.content.length)===0&&this.removeNode(r)}}class Pu{get parent(){return this.container}get feature(){return this.grammarSource}get hidden(){return!1}get astNode(){var e,t;const r=typeof((e=this._astNode)===null||e===void 0?void 0:e.$type)=="string"?this._astNode:(t=this.container)===null||t===void 0?void 0:t.astNode;if(!r)throw new Error("This node has no associated AST element");return r}set astNode(e){this._astNode=e}get element(){return this.astNode}get text(){return this.root.fullText.substring(this.offset,this.end)}}class xs extends Pu{get offset(){return this._offset}get length(){return this._length}get end(){return this._offset+this._length}get hidden(){return this._hidden}get tokenType(){return this._tokenType}get range(){return this._range}constructor(e,t,r,i,s=!1){super(),this._hidden=s,this._offset=e,this._tokenType=i,this._length=t,this._range=r}}class ta extends Pu{constructor(){super(...arguments),this.content=new na(this)}get children(){return this.content}get offset(){var e,t;return(t=(e=this.firstNonHiddenNode)===null||e===void 0?void 0:e.offset)!==null&&t!==void 0?t:0}get length(){return this.end-this.offset}get end(){var e,t;return(t=(e=this.lastNonHiddenNode)===null||e===void 0?void 0:e.end)!==null&&t!==void 0?t:0}get range(){const e=this.firstNonHiddenNode,t=this.lastNonHiddenNode;if(e&&t){if(this._rangeCache===void 0){const{range:r}=e,{range:i}=t;this._rangeCache={start:r.start,end:i.end.line<r.start.line?r.start:i.end}}return this._rangeCache}else return{start:P.create(0,0),end:P.create(0,0)}}get firstNonHiddenNode(){for(const e of this.content)if(!e.hidden)return e;return this.content[0]}get lastNonHiddenNode(){for(let e=this.content.length-1;e>=0;e--){const t=this.content[e];if(!t.hidden)return t}return this.content[this.content.length-1]}}class na extends Array{constructor(e){super(),this.parent=e,Object.setPrototypeOf(this,na.prototype)}push(...e){return this.addParents(e),super.push(...e)}unshift(...e){return this.addParents(e),super.unshift(...e)}splice(e,t,...r){return this.addParents(r),super.splice(e,t,...r)}addParents(e){for(const t of e)t.container=this.parent}}class Mu extends ta{get text(){return this._text.substring(this.offset,this.end)}get fullText(){return this._text}constructor(e){super(),this._text="",this._text=e??""}}const Is=Symbol("Datatype");function Di(n){return n.$type===Is}const qo="​",Du=n=>n.endsWith(qo)?n:n+qo;class Fu{constructor(e){this._unorderedGroups=new Map,this.allRules=new Map,this.lexer=e.parser.Lexer;const t=this.lexer.definition,r=e.LanguageMetaData.mode==="production";this.wrapper=new nm(t,Object.assign(Object.assign({},e.parser.ParserConfig),{skipValidations:r,errorMessageProvider:e.parser.ParserErrorMessageProvider}))}alternatives(e,t){this.wrapper.wrapOr(e,t)}optional(e,t){this.wrapper.wrapOption(e,t)}many(e,t){this.wrapper.wrapMany(e,t)}atLeastOne(e,t){this.wrapper.wrapAtLeastOne(e,t)}getRule(e){return this.allRules.get(e)}isRecording(){return this.wrapper.IS_RECORDING}get unorderedGroups(){return this._unorderedGroups}getRuleStack(){return this.wrapper.RULE_STACK}finalize(){this.wrapper.wrapSelfAnalysis()}}class Qp extends Fu{get current(){return this.stack[this.stack.length-1]}constructor(e){super(e),this.nodeBuilder=new Jp,this.stack=[],this.assignmentMap=new Map,this.linker=e.references.Linker,this.converter=e.parser.ValueConverter,this.astReflection=e.shared.AstReflection}rule(e,t){const r=this.computeRuleType(e),i=this.wrapper.DEFINE_RULE(Du(e.name),this.startImplementation(r,t).bind(this));return this.allRules.set(e.name,i),e.entry&&(this.mainRule=i),i}computeRuleType(e){if(!e.fragment){if(Wl(e))return Is;{const t=Bs(e);return t??e.name}}}parse(e,t={}){this.nodeBuilder.buildRootNode(e);const r=this.lexerResult=this.lexer.tokenize(e);this.wrapper.input=r.tokens;const i=t.rule?this.allRules.get(t.rule):this.mainRule;if(!i)throw new Error(t.rule?`No rule found with name '${t.rule}'`:"No main rule available.");const s=i.call(this.wrapper,{});return this.nodeBuilder.addHiddenNodes(r.hidden),this.unorderedGroups.clear(),this.lexerResult=void 0,{value:s,lexerErrors:r.errors,lexerReport:r.report,parserErrors:this.wrapper.errors}}startImplementation(e,t){return r=>{const i=!this.isRecording()&&e!==void 0;if(i){const a={$type:e};this.stack.push(a),e===Is&&(a.value="")}let s;try{s=t(r)}catch{s=void 0}return s===void 0&&i&&(s=this.construct()),s}}extractHiddenTokens(e){const t=this.lexerResult.hidden;if(!t.length)return[];const r=e.startOffset;for(let i=0;i<t.length;i++)if(t[i].startOffset>r)return t.splice(0,i);return t.splice(0,t.length)}consume(e,t,r){const i=this.wrapper.wrapConsume(e,t);if(!this.isRecording()&&this.isValidToken(i)){const s=this.extractHiddenTokens(i);this.nodeBuilder.addHiddenNodes(s);const a=this.nodeBuilder.buildLeafNode(i,r),{assignment:o,isCrossRef:l}=this.getAssignment(r),u=this.current;if(o){const c=ht(r)?i.image:this.converter.convert(i.image,a);this.assign(o.operator,o.feature,c,a,l)}else if(Di(u)){let c=i.image;ht(r)||(c=this.converter.convert(c,a).toString()),u.value+=c}}}isValidToken(e){return!e.isInsertedInRecovery&&!isNaN(e.startOffset)&&typeof e.endOffset=="number"&&!isNaN(e.endOffset)}subrule(e,t,r,i,s){let a;!this.isRecording()&&!r&&(a=this.nodeBuilder.buildCompositeNode(i));const o=this.wrapper.wrapSubrule(e,t,s);!this.isRecording()&&a&&a.length>0&&this.performSubruleAssignment(o,i,a)}performSubruleAssignment(e,t,r){const{assignment:i,isCrossRef:s}=this.getAssignment(t);if(i)this.assign(i.operator,i.feature,e,r,s);else if(!i){const a=this.current;if(Di(a))a.value+=e.toString();else if(typeof e=="object"&&e){const l=this.assignWithoutOverride(e,a);this.stack.pop(),this.stack.push(l)}}}action(e,t){if(!this.isRecording()){let r=this.current;if(t.feature&&t.operator){r=this.construct(),this.nodeBuilder.removeNode(r.$cstNode),this.nodeBuilder.buildCompositeNode(t).content.push(r.$cstNode);const s={$type:e};this.stack.push(s),this.assign(t.operator,t.feature,r,r.$cstNode,!1)}else r.$type=e}}construct(){if(this.isRecording())return;const e=this.current;return _d(e),this.nodeBuilder.construct(e),this.stack.pop(),Di(e)?this.converter.convert(e.value,e.$cstNode):(Ld(this.astReflection,e),e)}getAssignment(e){if(!this.assignmentMap.has(e)){const t=di(e,ft);this.assignmentMap.set(e,{assignment:t,isCrossRef:t?Ds(t.terminal):!1})}return this.assignmentMap.get(e)}assign(e,t,r,i,s){const a=this.current;let o;switch(s&&typeof r=="string"?o=this.linker.buildReference(a,t,i,r):o=r,e){case"=":{a[t]=o;break}case"?=":{a[t]=!0;break}case"+=":Array.isArray(a[t])||(a[t]=[]),a[t].push(o)}}assignWithoutOverride(e,t){for(const[i,s]of Object.entries(t)){const a=e[i];a===void 0?e[i]=s:Array.isArray(a)&&Array.isArray(s)&&(s.push(...a),e[i]=s)}const r=e.$cstNode;return r&&(r.astNode=void 0,e.$cstNode=void 0),e}get definitionErrors(){return this.wrapper.definitionErrors}}class Zp{buildMismatchTokenMessage(e){return Nt.buildMismatchTokenMessage(e)}buildNotAllInputParsedMessage(e){return Nt.buildNotAllInputParsedMessage(e)}buildNoViableAltMessage(e){return Nt.buildNoViableAltMessage(e)}buildEarlyExitMessage(e){return Nt.buildEarlyExitMessage(e)}}class Gu extends Zp{buildMismatchTokenMessage({expected:e,actual:t}){return`Expecting ${e.LABEL?"`"+e.LABEL+"`":e.name.endsWith(":KW")?`keyword '${e.name.substring(0,e.name.length-3)}'`:`token of type '${e.name}'`} but found \`${t.image}\`.`}buildNotAllInputParsedMessage({firstRedundant:e}){return`Expecting end of file but found \`${e.image}\`.`}}class em extends Fu{constructor(){super(...arguments),this.tokens=[],this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}action(){}construct(){}parse(e){this.resetState();const t=this.lexer.tokenize(e,{mode:"partial"});return this.tokens=t.tokens,this.wrapper.input=[...this.tokens],this.mainRule.call(this.wrapper,{}),this.unorderedGroups.clear(),{tokens:this.tokens,elementStack:[...this.lastElementStack],tokenIndex:this.nextTokenIndex}}rule(e,t){const r=this.wrapper.DEFINE_RULE(Du(e.name),this.startImplementation(t).bind(this));return this.allRules.set(e.name,r),e.entry&&(this.mainRule=r),r}resetState(){this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}startImplementation(e){return t=>{const r=this.keepStackSize();try{e(t)}finally{this.resetStackSize(r)}}}removeUnexpectedElements(){this.elementStack.splice(this.stackSize)}keepStackSize(){const e=this.elementStack.length;return this.stackSize=e,e}resetStackSize(e){this.removeUnexpectedElements(),this.stackSize=e}consume(e,t,r){this.wrapper.wrapConsume(e,t),this.isRecording()||(this.lastElementStack=[...this.elementStack,r],this.nextTokenIndex=this.currIdx+1)}subrule(e,t,r,i,s){this.before(i),this.wrapper.wrapSubrule(e,t,s),this.after(i)}before(e){this.isRecording()||this.elementStack.push(e)}after(e){if(!this.isRecording()){const t=this.elementStack.lastIndexOf(e);t>=0&&this.elementStack.splice(t)}}get currIdx(){return this.wrapper.currIdx}}const tm={recoveryEnabled:!0,nodeLocationTracking:"full",skipValidations:!0,errorMessageProvider:new Gu};class nm extends up{constructor(e,t){const r=t&&"maxLookahead"in t;super(e,Object.assign(Object.assign(Object.assign({},tm),{lookaheadStrategy:r?new Ys({maxLookahead:t.maxLookahead}):new wp({logging:t.skipValidations?()=>{}:void 0})}),t))}get IS_RECORDING(){return this.RECORDING_PHASE}DEFINE_RULE(e,t){return this.RULE(e,t)}wrapSelfAnalysis(){this.performSelfAnalysis()}wrapConsume(e,t){return this.consume(e,t)}wrapSubrule(e,t,r){return this.subrule(e,t,{ARGS:[r]})}wrapOr(e,t){this.or(e,t)}wrapOption(e,t){this.option(e,t)}wrapMany(e,t){this.many(e,t)}wrapAtLeastOne(e,t){this.atLeastOne(e,t)}}function Uu(n,e,t){return rm({parser:e,tokens:t,ruleNames:new Map},n),e}function rm(n,e){const t=Fl(e,!1),r=ee(e.rules).filter(we).filter(i=>t.has(i));for(const i of r){const s=Object.assign(Object.assign({},n),{consume:1,optional:1,subrule:1,many:1,or:1});n.parser.rule(i,gt(s,i.definition))}}function gt(n,e,t=!1){let r;if(ht(e))r=cm(n,e);else if(ci(e))r=im(n,e);else if(ft(e))r=gt(n,e.terminal);else if(Ds(e))r=Bu(n,e);else if(pt(e))r=sm(n,e);else if(Ll(e))r=om(n,e);else if(Ol(e))r=lm(n,e);else if(Fs(e))r=um(n,e);else if(kd(e)){const i=n.consume++;r=()=>n.parser.consume(i,nt,e)}else throw new $l(e.$cstNode,`Unexpected element type: ${e.$type}`);return Vu(n,t?void 0:ri(e),r,e.cardinality)}function im(n,e){const t=Vs(e);return()=>n.parser.action(t,e)}function sm(n,e){const t=e.rule.ref;if(we(t)){const r=n.subrule++,i=t.fragment,s=e.arguments.length>0?am(t,e.arguments):()=>({});return a=>n.parser.subrule(r,Wu(n,t),i,e,s(a))}else if(Rt(t)){const r=n.consume++,i=Cs(n,t.name);return()=>n.parser.consume(r,i,e)}else if(t)Qn();else throw new $l(e.$cstNode,`Undefined rule: ${e.rule.$refText}`)}function am(n,e){const t=e.map(r=>He(r.value));return r=>{const i={};for(let s=0;s<t.length;s++){const a=n.parameters[s],o=t[s];i[a.name]=o(r)}return i}}function He(n){if(yd(n)){const e=He(n.left),t=He(n.right);return r=>e(r)||t(r)}else if(gd(n)){const e=He(n.left),t=He(n.right);return r=>e(r)&&t(r)}else if(Td(n)){const e=He(n.value);return t=>!e(t)}else if(Rd(n)){const e=n.parameter.ref.name;return t=>t!==void 0&&t[e]===!0}else if(md(n)){const e=!!n.true;return()=>e}Qn()}function om(n,e){if(e.elements.length===1)return gt(n,e.elements[0]);{const t=[];for(const i of e.elements){const s={ALT:gt(n,i,!0)},a=ri(i);a&&(s.GATE=He(a)),t.push(s)}const r=n.or++;return i=>n.parser.alternatives(r,t.map(s=>{const a={ALT:()=>s.ALT(i)},o=s.GATE;return o&&(a.GATE=()=>o(i)),a}))}}function lm(n,e){if(e.elements.length===1)return gt(n,e.elements[0]);const t=[];for(const o of e.elements){const l={ALT:gt(n,o,!0)},u=ri(o);u&&(l.GATE=He(u)),t.push(l)}const r=n.or++,i=(o,l)=>{const u=l.getRuleStack().join("-");return`uGroup_${o}_${u}`},s=o=>n.parser.alternatives(r,t.map((l,u)=>{const c={ALT:()=>!0},d=n.parser;c.ALT=()=>{if(l.ALT(o),!d.isRecording()){const f=i(r,d);d.unorderedGroups.get(f)||d.unorderedGroups.set(f,[]);const m=d.unorderedGroups.get(f);typeof(m==null?void 0:m[u])>"u"&&(m[u]=!0)}};const h=l.GATE;return h?c.GATE=()=>h(o):c.GATE=()=>{const f=d.unorderedGroups.get(i(r,d));return!(f!=null&&f[u])},c})),a=Vu(n,ri(e),s,"*");return o=>{a(o),n.parser.isRecording()||n.parser.unorderedGroups.delete(i(r,n.parser))}}function um(n,e){const t=e.elements.map(r=>gt(n,r));return r=>t.forEach(i=>i(r))}function ri(n){if(Fs(n))return n.guardCondition}function Bu(n,e,t=e.terminal){if(t)if(pt(t)&&we(t.rule.ref)){const r=t.rule.ref,i=n.subrule++;return s=>n.parser.subrule(i,Wu(n,r),!1,e,s)}else if(pt(t)&&Rt(t.rule.ref)){const r=n.consume++,i=Cs(n,t.rule.ref.name);return()=>n.parser.consume(r,i,e)}else if(ht(t)){const r=n.consume++,i=Cs(n,t.value);return()=>n.parser.consume(r,i,e)}else throw new Error("Could not build cross reference parser");else{if(!e.type.ref)throw new Error("Could not resolve reference to type: "+e.type.$refText);const r=Bl(e.type.ref),i=r==null?void 0:r.terminal;if(!i)throw new Error("Could not find name assignment for type: "+Vs(e.type.ref));return Bu(n,e,i)}}function cm(n,e){const t=n.consume++,r=n.tokens[e.value];if(!r)throw new Error("Could not find token for keyword: "+e.value);return()=>n.parser.consume(t,r,e)}function Vu(n,e,t,r){const i=e&&He(e);if(!r)if(i){const s=n.or++;return a=>n.parser.alternatives(s,[{ALT:()=>t(a),GATE:()=>i(a)},{ALT:Va(),GATE:()=>!i(a)}])}else return t;if(r==="*"){const s=n.many++;return a=>n.parser.many(s,{DEF:()=>t(a),GATE:i?()=>i(a):void 0})}else if(r==="+"){const s=n.many++;if(i){const a=n.or++;return o=>n.parser.alternatives(a,[{ALT:()=>n.parser.atLeastOne(s,{DEF:()=>t(o)}),GATE:()=>i(o)},{ALT:Va(),GATE:()=>!i(o)}])}else return a=>n.parser.atLeastOne(s,{DEF:()=>t(a)})}else if(r==="?"){const s=n.optional++;return a=>n.parser.optional(s,{DEF:()=>t(a),GATE:i?()=>i(a):void 0})}else Qn()}function Wu(n,e){const t=dm(n,e),r=n.parser.getRule(t);if(!r)throw new Error(`Rule "${t}" not found."`);return r}function dm(n,e){if(we(e))return e.name;if(n.ruleNames.has(e))return n.ruleNames.get(e);{let t=e,r=t.$container,i=e.$type;for(;!we(r);)(Fs(r)||Ll(r)||Ol(r))&&(i=r.elements.indexOf(t).toString()+":"+i),t=r,r=r.$container;return i=r.name+":"+i,n.ruleNames.set(e,i),i}}function Cs(n,e){const t=n.tokens[e];if(!t)throw new Error(`Token "${e}" not found."`);return t}function fm(n){const e=n.Grammar,t=n.parser.Lexer,r=new em(n);return Uu(e,r,t.definition),r.finalize(),r}function hm(n){const e=pm(n);return e.finalize(),e}function pm(n){const e=n.Grammar,t=n.parser.Lexer,r=new Qp(n);return Uu(e,r,t.definition)}class ju{constructor(){this.diagnostics=[]}buildTokens(e,t){const r=ee(Fl(e,!1)),i=this.buildTerminalTokens(r),s=this.buildKeywordTokens(r,i,t);return i.forEach(a=>{const o=a.PATTERN;typeof o=="object"&&o&&"test"in o&&es(o)?s.unshift(a):s.push(a)}),s}flushLexingReport(e){return{diagnostics:this.popDiagnostics()}}popDiagnostics(){const e=[...this.diagnostics];return this.diagnostics=[],e}buildTerminalTokens(e){return e.filter(Rt).filter(t=>!t.fragment).map(t=>this.buildTerminalToken(t)).toArray()}buildTerminalToken(e){const t=Ws(e),r=this.requiresCustomPattern(t)?this.regexPatternFunction(t):t,i={name:e.name,PATTERN:r};return typeof r=="function"&&(i.LINE_BREAKS=!0),e.hidden&&(i.GROUP=es(t)?de.SKIPPED:"hidden"),i}requiresCustomPattern(e){return e.flags.includes("u")||e.flags.includes("s")?!0:!!(e.source.includes("?<=")||e.source.includes("?<!"))}regexPatternFunction(e){const t=new RegExp(e,e.flags+"y");return(r,i)=>(t.lastIndex=i,t.exec(r))}buildKeywordTokens(e,t,r){return e.filter(we).flatMap(i=>Zn(i).filter(ht)).distinct(i=>i.value).toArray().sort((i,s)=>s.value.length-i.value.length).map(i=>this.buildKeywordToken(i,t,!!(r!=null&&r.caseInsensitive)))}buildKeywordToken(e,t,r){const i=this.buildKeywordPattern(e,r),s={name:e.value,PATTERN:i,LONGER_ALT:this.findLongerAlt(e,t)};return typeof i=="function"&&(s.LINE_BREAKS=!0),s}buildKeywordPattern(e,t){return t?new RegExp(Bd(e.value)):e.value}findLongerAlt(e,t){return t.reduce((r,i)=>{const s=i==null?void 0:i.PATTERN;return s!=null&&s.source&&Vd("^"+s.source+"$",e.value)&&r.push(i),r},[])}}class Ku{convert(e,t){let r=t.grammarSource;if(Ds(r)&&(r=Hd(r)),pt(r)){const i=r.rule.ref;if(!i)throw new Error("This cst node was not parsed by a rule.");return this.runConverter(i,e,t)}return e}runConverter(e,t,r){var i;switch(e.name.toUpperCase()){case"INT":return je.convertInt(t);case"STRING":return je.convertString(t);case"ID":return je.convertID(t)}switch((i=Zd(e))===null||i===void 0?void 0:i.toLowerCase()){case"number":return je.convertNumber(t);case"boolean":return je.convertBoolean(t);case"bigint":return je.convertBigint(t);case"date":return je.convertDate(t);default:return t}}}var je;(function(n){function e(u){let c="";for(let d=1;d<u.length-1;d++){const h=u.charAt(d);if(h==="\\"){const f=u.charAt(++d);c+=t(f)}else c+=h}return c}n.convertString=e;function t(u){switch(u){case"b":return"\b";case"f":return"\f";case"n":return`
`;case"r":return"\r";case"t":return"	";case"v":return"\v";case"0":return"\0";default:return u}}function r(u){return u.charAt(0)==="^"?u.substring(1):u}n.convertID=r;function i(u){return parseInt(u)}n.convertInt=i;function s(u){return BigInt(u)}n.convertBigint=s;function a(u){return new Date(u)}n.convertDate=a;function o(u){return Number(u)}n.convertNumber=o;function l(u){return u.toLowerCase()==="true"}n.convertBoolean=l})(je||(je={}));var ut={},Rr={},Yo;function Hu(){if(Yo)return Rr;Yo=1,Object.defineProperty(Rr,"__esModule",{value:!0});let n;function e(){if(n===void 0)throw new Error("No runtime abstraction layer installed");return n}return function(t){function r(i){if(i===void 0)throw new Error("No runtime abstraction layer provided");n=i}t.install=r}(e||(e={})),Rr.default=e,Rr}var J={},Xo;function mm(){if(Xo)return J;Xo=1,Object.defineProperty(J,"__esModule",{value:!0}),J.stringArray=J.array=J.func=J.error=J.number=J.string=J.boolean=void 0;function n(o){return o===!0||o===!1}J.boolean=n;function e(o){return typeof o=="string"||o instanceof String}J.string=e;function t(o){return typeof o=="number"||o instanceof Number}J.number=t;function r(o){return o instanceof Error}J.error=r;function i(o){return typeof o=="function"}J.func=i;function s(o){return Array.isArray(o)}J.array=s;function a(o){return s(o)&&o.every(l=>e(l))}return J.stringArray=a,J}var ct={},Jo;function zu(){if(Jo)return ct;Jo=1,Object.defineProperty(ct,"__esModule",{value:!0}),ct.Emitter=ct.Event=void 0;const n=Hu();var e;(function(i){const s={dispose(){}};i.None=function(){return s}})(e||(ct.Event=e={}));class t{add(s,a=null,o){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(s),this._contexts.push(a),Array.isArray(o)&&o.push({dispose:()=>this.remove(s,a)})}remove(s,a=null){if(!this._callbacks)return;let o=!1;for(let l=0,u=this._callbacks.length;l<u;l++)if(this._callbacks[l]===s)if(this._contexts[l]===a){this._callbacks.splice(l,1),this._contexts.splice(l,1);return}else o=!0;if(o)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...s){if(!this._callbacks)return[];const a=[],o=this._callbacks.slice(0),l=this._contexts.slice(0);for(let u=0,c=o.length;u<c;u++)try{a.push(o[u].apply(l[u],s))}catch(d){(0,n.default)().console.error(d)}return a}isEmpty(){return!this._callbacks||this._callbacks.length===0}dispose(){this._callbacks=void 0,this._contexts=void 0}}class r{constructor(s){this._options=s}get event(){return this._event||(this._event=(s,a,o)=>{this._callbacks||(this._callbacks=new t),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(s,a);const l={dispose:()=>{this._callbacks&&(this._callbacks.remove(s,a),l.dispose=r._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))}};return Array.isArray(o)&&o.push(l),l}),this._event}fire(s){this._callbacks&&this._callbacks.invoke.call(this._callbacks,s)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}}return ct.Emitter=r,r._noop=function(){},ct}var Qo;function gm(){if(Qo)return ut;Qo=1,Object.defineProperty(ut,"__esModule",{value:!0}),ut.CancellationTokenSource=ut.CancellationToken=void 0;const n=Hu(),e=mm(),t=zu();var r;(function(o){o.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:t.Event.None}),o.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:t.Event.None});function l(u){const c=u;return c&&(c===o.None||c===o.Cancelled||e.boolean(c.isCancellationRequested)&&!!c.onCancellationRequested)}o.is=l})(r||(ut.CancellationToken=r={}));const i=Object.freeze(function(o,l){const u=(0,n.default)().timer.setTimeout(o.bind(l),0);return{dispose(){u.dispose()}}});class s{constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?i:(this._emitter||(this._emitter=new t.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}}class a{get token(){return this._token||(this._token=new s),this._token}cancel(){this._token?this._token.cancel():this._token=r.Cancelled}dispose(){this._token?this._token instanceof s&&this._token.dispose():this._token=r.None}}return ut.CancellationTokenSource=a,ut}var V=gm();function ym(){return new Promise(n=>{typeof setImmediate>"u"?setTimeout(n,0):setImmediate(n)})}let _r=0,Tm=10;function Rm(){return _r=performance.now(),new V.CancellationTokenSource}const ii=Symbol("OperationCancelled");function Ei(n){return n===ii}async function Ee(n){if(n===V.CancellationToken.None)return;const e=performance.now();if(e-_r>=Tm&&(_r=e,await ym(),_r=performance.now()),n.isCancellationRequested)throw ii}class ra{constructor(){this.promise=new Promise((e,t)=>{this.resolve=r=>(e(r),this),this.reject=r=>(t(r),this)})}}class Xn{constructor(e,t,r,i){this._uri=e,this._languageId=t,this._version=r,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){const t=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(t,r)}return this._content}update(e,t){for(const r of e)if(Xn.isIncremental(r)){const i=Yu(r.range),s=this.offsetAt(i.start),a=this.offsetAt(i.end);this._content=this._content.substring(0,s)+r.text+this._content.substring(a,this._content.length);const o=Math.max(i.start.line,0),l=Math.max(i.end.line,0);let u=this._lineOffsets;const c=Zo(r.text,!1,s);if(l-o===c.length)for(let h=0,f=c.length;h<f;h++)u[h+o+1]=c[h];else c.length<1e4?u.splice(o+1,l-o,...c):this._lineOffsets=u=u.slice(0,o+1).concat(c,u.slice(l+1));const d=r.text.length-(a-s);if(d!==0)for(let h=o+1+c.length,f=u.length;h<f;h++)u[h]=u[h]+d}else if(Xn.isFull(r))this._content=r.text,this._lineOffsets=void 0;else throw new Error("Unknown change event received");this._version=t}getLineOffsets(){return this._lineOffsets===void 0&&(this._lineOffsets=Zo(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);const t=this.getLineOffsets();let r=0,i=t.length;if(i===0)return{line:0,character:e};for(;r<i;){const a=Math.floor((r+i)/2);t[a]>e?i=a:r=a+1}const s=r-1;return e=this.ensureBeforeEOL(e,t[s]),{line:s,character:e-t[s]}}offsetAt(e){const t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;const r=t[e.line];if(e.character<=0)return r;const i=e.line+1<t.length?t[e.line+1]:this._content.length,s=Math.min(r+e.character,i);return this.ensureBeforeEOL(s,r)}ensureBeforeEOL(e,t){for(;e>t&&qu(this._content.charCodeAt(e-1));)e--;return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){const t=e;return t!=null&&typeof t.text=="string"&&t.range!==void 0&&(t.rangeLength===void 0||typeof t.rangeLength=="number")}static isFull(e){const t=e;return t!=null&&typeof t.text=="string"&&t.range===void 0&&t.rangeLength===void 0}}var $s;(function(n){function e(i,s,a,o){return new Xn(i,s,a,o)}n.create=e;function t(i,s,a){if(i instanceof Xn)return i.update(s,a),i;throw new Error("TextDocument.update: document must be created by TextDocument.create")}n.update=t;function r(i,s){const a=i.getText(),o=Ns(s.map(Am),(c,d)=>{const h=c.range.start.line-d.range.start.line;return h===0?c.range.start.character-d.range.start.character:h});let l=0;const u=[];for(const c of o){const d=i.offsetAt(c.range.start);if(d<l)throw new Error("Overlapping edit");d>l&&u.push(a.substring(l,d)),c.newText.length&&u.push(c.newText),l=i.offsetAt(c.range.end)}return u.push(a.substr(l)),u.join("")}n.applyEdits=r})($s||($s={}));function Ns(n,e){if(n.length<=1)return n;const t=n.length/2|0,r=n.slice(0,t),i=n.slice(t);Ns(r,e),Ns(i,e);let s=0,a=0,o=0;for(;s<r.length&&a<i.length;)e(r[s],i[a])<=0?n[o++]=r[s++]:n[o++]=i[a++];for(;s<r.length;)n[o++]=r[s++];for(;a<i.length;)n[o++]=i[a++];return n}function Zo(n,e,t=0){const r=e?[t]:[];for(let i=0;i<n.length;i++){const s=n.charCodeAt(i);qu(s)&&(s===13&&i+1<n.length&&n.charCodeAt(i+1)===10&&i++,r.push(t+i+1))}return r}function qu(n){return n===13||n===10}function Yu(n){const e=n.start,t=n.end;return e.line>t.line||e.line===t.line&&e.character>t.character?{start:t,end:e}:n}function Am(n){const e=Yu(n.range);return e!==n.range?{newText:n.newText,range:e}:n}var Xu;(()=>{var n={470:i=>{function s(l){if(typeof l!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(l))}function a(l,u){for(var c,d="",h=0,f=-1,m=0,g=0;g<=l.length;++g){if(g<l.length)c=l.charCodeAt(g);else{if(c===47)break;c=47}if(c===47){if(!(f===g-1||m===1))if(f!==g-1&&m===2){if(d.length<2||h!==2||d.charCodeAt(d.length-1)!==46||d.charCodeAt(d.length-2)!==46){if(d.length>2){var A=d.lastIndexOf("/");if(A!==d.length-1){A===-1?(d="",h=0):h=(d=d.slice(0,A)).length-1-d.lastIndexOf("/"),f=g,m=0;continue}}else if(d.length===2||d.length===1){d="",h=0,f=g,m=0;continue}}u&&(d.length>0?d+="/..":d="..",h=2)}else d.length>0?d+="/"+l.slice(f+1,g):d=l.slice(f+1,g),h=g-f-1;f=g,m=0}else c===46&&m!==-1?++m:m=-1}return d}var o={resolve:function(){for(var l,u="",c=!1,d=arguments.length-1;d>=-1&&!c;d--){var h;d>=0?h=arguments[d]:(l===void 0&&(l=process.cwd()),h=l),s(h),h.length!==0&&(u=h+"/"+u,c=h.charCodeAt(0)===47)}return u=a(u,!c),c?u.length>0?"/"+u:"/":u.length>0?u:"."},normalize:function(l){if(s(l),l.length===0)return".";var u=l.charCodeAt(0)===47,c=l.charCodeAt(l.length-1)===47;return(l=a(l,!u)).length!==0||u||(l="."),l.length>0&&c&&(l+="/"),u?"/"+l:l},isAbsolute:function(l){return s(l),l.length>0&&l.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var l,u=0;u<arguments.length;++u){var c=arguments[u];s(c),c.length>0&&(l===void 0?l=c:l+="/"+c)}return l===void 0?".":o.normalize(l)},relative:function(l,u){if(s(l),s(u),l===u||(l=o.resolve(l))===(u=o.resolve(u)))return"";for(var c=1;c<l.length&&l.charCodeAt(c)===47;++c);for(var d=l.length,h=d-c,f=1;f<u.length&&u.charCodeAt(f)===47;++f);for(var m=u.length-f,g=h<m?h:m,A=-1,y=0;y<=g;++y){if(y===g){if(m>g){if(u.charCodeAt(f+y)===47)return u.slice(f+y+1);if(y===0)return u.slice(f+y)}else h>g&&(l.charCodeAt(c+y)===47?A=y:y===0&&(A=0));break}var E=l.charCodeAt(c+y);if(E!==u.charCodeAt(f+y))break;E===47&&(A=y)}var R="";for(y=c+A+1;y<=d;++y)y!==d&&l.charCodeAt(y)!==47||(R.length===0?R+="..":R+="/..");return R.length>0?R+u.slice(f+A):(f+=A,u.charCodeAt(f)===47&&++f,u.slice(f))},_makeLong:function(l){return l},dirname:function(l){if(s(l),l.length===0)return".";for(var u=l.charCodeAt(0),c=u===47,d=-1,h=!0,f=l.length-1;f>=1;--f)if((u=l.charCodeAt(f))===47){if(!h){d=f;break}}else h=!1;return d===-1?c?"/":".":c&&d===1?"//":l.slice(0,d)},basename:function(l,u){if(u!==void 0&&typeof u!="string")throw new TypeError('"ext" argument must be a string');s(l);var c,d=0,h=-1,f=!0;if(u!==void 0&&u.length>0&&u.length<=l.length){if(u.length===l.length&&u===l)return"";var m=u.length-1,g=-1;for(c=l.length-1;c>=0;--c){var A=l.charCodeAt(c);if(A===47){if(!f){d=c+1;break}}else g===-1&&(f=!1,g=c+1),m>=0&&(A===u.charCodeAt(m)?--m==-1&&(h=c):(m=-1,h=g))}return d===h?h=g:h===-1&&(h=l.length),l.slice(d,h)}for(c=l.length-1;c>=0;--c)if(l.charCodeAt(c)===47){if(!f){d=c+1;break}}else h===-1&&(f=!1,h=c+1);return h===-1?"":l.slice(d,h)},extname:function(l){s(l);for(var u=-1,c=0,d=-1,h=!0,f=0,m=l.length-1;m>=0;--m){var g=l.charCodeAt(m);if(g!==47)d===-1&&(h=!1,d=m+1),g===46?u===-1?u=m:f!==1&&(f=1):u!==-1&&(f=-1);else if(!h){c=m+1;break}}return u===-1||d===-1||f===0||f===1&&u===d-1&&u===c+1?"":l.slice(u,d)},format:function(l){if(l===null||typeof l!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof l);return function(u,c){var d=c.dir||c.root,h=c.base||(c.name||"")+(c.ext||"");return d?d===c.root?d+h:d+"/"+h:h}(0,l)},parse:function(l){s(l);var u={root:"",dir:"",base:"",ext:"",name:""};if(l.length===0)return u;var c,d=l.charCodeAt(0),h=d===47;h?(u.root="/",c=1):c=0;for(var f=-1,m=0,g=-1,A=!0,y=l.length-1,E=0;y>=c;--y)if((d=l.charCodeAt(y))!==47)g===-1&&(A=!1,g=y+1),d===46?f===-1?f=y:E!==1&&(E=1):f!==-1&&(E=-1);else if(!A){m=y+1;break}return f===-1||g===-1||E===0||E===1&&f===g-1&&f===m+1?g!==-1&&(u.base=u.name=m===0&&h?l.slice(1,g):l.slice(m,g)):(m===0&&h?(u.name=l.slice(1,f),u.base=l.slice(1,g)):(u.name=l.slice(m,f),u.base=l.slice(m,g)),u.ext=l.slice(f,g)),m>0?u.dir=l.slice(0,m-1):h&&(u.dir="/"),u},sep:"/",delimiter:":",win32:null,posix:null};o.posix=o,i.exports=o}},e={};function t(i){var s=e[i];if(s!==void 0)return s.exports;var a=e[i]={exports:{}};return n[i](a,a.exports,t),a.exports}t.d=(i,s)=>{for(var a in s)t.o(s,a)&&!t.o(i,a)&&Object.defineProperty(i,a,{enumerable:!0,get:s[a]})},t.o=(i,s)=>Object.prototype.hasOwnProperty.call(i,s),t.r=i=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})};var r={};(()=>{let i;t.r(r),t.d(r,{URI:()=>h,Utils:()=>Ce}),typeof process=="object"?i=process.platform==="win32":typeof navigator=="object"&&(i=navigator.userAgent.indexOf("Windows")>=0);const s=/^\w[\w\d+.-]*$/,a=/^\//,o=/^\/\//;function l(S,T){if(!S.scheme&&T)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${S.authority}", path: "${S.path}", query: "${S.query}", fragment: "${S.fragment}"}`);if(S.scheme&&!s.test(S.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(S.path){if(S.authority){if(!a.test(S.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(o.test(S.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}const u="",c="/",d=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class h{constructor(T,v,I,O,L,_=!1){Qe(this,"scheme");Qe(this,"authority");Qe(this,"path");Qe(this,"query");Qe(this,"fragment");typeof T=="object"?(this.scheme=T.scheme||u,this.authority=T.authority||u,this.path=T.path||u,this.query=T.query||u,this.fragment=T.fragment||u):(this.scheme=function(Te,q){return Te||q?Te:"file"}(T,_),this.authority=v||u,this.path=function(Te,q){switch(Te){case"https":case"http":case"file":q?q[0]!==c&&(q=c+q):q=c}return q}(this.scheme,I||u),this.query=O||u,this.fragment=L||u,l(this,_))}static isUri(T){return T instanceof h||!!T&&typeof T.authority=="string"&&typeof T.fragment=="string"&&typeof T.path=="string"&&typeof T.query=="string"&&typeof T.scheme=="string"&&typeof T.fsPath=="string"&&typeof T.with=="function"&&typeof T.toString=="function"}get fsPath(){return E(this)}with(T){if(!T)return this;let{scheme:v,authority:I,path:O,query:L,fragment:_}=T;return v===void 0?v=this.scheme:v===null&&(v=u),I===void 0?I=this.authority:I===null&&(I=u),O===void 0?O=this.path:O===null&&(O=u),L===void 0?L=this.query:L===null&&(L=u),_===void 0?_=this.fragment:_===null&&(_=u),v===this.scheme&&I===this.authority&&O===this.path&&L===this.query&&_===this.fragment?this:new m(v,I,O,L,_)}static parse(T,v=!1){const I=d.exec(T);return I?new m(I[2]||u,ie(I[4]||u),ie(I[5]||u),ie(I[7]||u),ie(I[9]||u),v):new m(u,u,u,u,u)}static file(T){let v=u;if(i&&(T=T.replace(/\\/g,c)),T[0]===c&&T[1]===c){const I=T.indexOf(c,2);I===-1?(v=T.substring(2),T=c):(v=T.substring(2,I),T=T.substring(I)||c)}return new m("file",v,T,u,u)}static from(T){const v=new m(T.scheme,T.authority,T.path,T.query,T.fragment);return l(v,!0),v}toString(T=!1){return R(this,T)}toJSON(){return this}static revive(T){if(T){if(T instanceof h)return T;{const v=new m(T);return v._formatted=T.external,v._fsPath=T._sep===f?T.fsPath:null,v}}return T}}const f=i?1:void 0;class m extends h{constructor(){super(...arguments);Qe(this,"_formatted",null);Qe(this,"_fsPath",null)}get fsPath(){return this._fsPath||(this._fsPath=E(this)),this._fsPath}toString(v=!1){return v?R(this,!0):(this._formatted||(this._formatted=R(this,!1)),this._formatted)}toJSON(){const v={$mid:1};return this._fsPath&&(v.fsPath=this._fsPath,v._sep=f),this._formatted&&(v.external=this._formatted),this.path&&(v.path=this.path),this.scheme&&(v.scheme=this.scheme),this.authority&&(v.authority=this.authority),this.query&&(v.query=this.query),this.fragment&&(v.fragment=this.fragment),v}}const g={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function A(S,T,v){let I,O=-1;for(let L=0;L<S.length;L++){const _=S.charCodeAt(L);if(_>=97&&_<=122||_>=65&&_<=90||_>=48&&_<=57||_===45||_===46||_===95||_===126||T&&_===47||v&&_===91||v&&_===93||v&&_===58)O!==-1&&(I+=encodeURIComponent(S.substring(O,L)),O=-1),I!==void 0&&(I+=S.charAt(L));else{I===void 0&&(I=S.substr(0,L));const Te=g[_];Te!==void 0?(O!==-1&&(I+=encodeURIComponent(S.substring(O,L)),O=-1),I+=Te):O===-1&&(O=L)}}return O!==-1&&(I+=encodeURIComponent(S.substring(O))),I!==void 0?I:S}function y(S){let T;for(let v=0;v<S.length;v++){const I=S.charCodeAt(v);I===35||I===63?(T===void 0&&(T=S.substr(0,v)),T+=g[I]):T!==void 0&&(T+=S[v])}return T!==void 0?T:S}function E(S,T){let v;return v=S.authority&&S.path.length>1&&S.scheme==="file"?`//${S.authority}${S.path}`:S.path.charCodeAt(0)===47&&(S.path.charCodeAt(1)>=65&&S.path.charCodeAt(1)<=90||S.path.charCodeAt(1)>=97&&S.path.charCodeAt(1)<=122)&&S.path.charCodeAt(2)===58?S.path[1].toLowerCase()+S.path.substr(2):S.path,i&&(v=v.replace(/\//g,"\\")),v}function R(S,T){const v=T?y:A;let I="",{scheme:O,authority:L,path:_,query:Te,fragment:q}=S;if(O&&(I+=O,I+=":"),(L||O==="file")&&(I+=c,I+=c),L){let W=L.indexOf("@");if(W!==-1){const ot=L.substr(0,W);L=L.substr(W+1),W=ot.lastIndexOf(":"),W===-1?I+=v(ot,!1,!1):(I+=v(ot.substr(0,W),!1,!1),I+=":",I+=v(ot.substr(W+1),!1,!0)),I+="@"}L=L.toLowerCase(),W=L.lastIndexOf(":"),W===-1?I+=v(L,!1,!0):(I+=v(L.substr(0,W),!1,!0),I+=L.substr(W))}if(_){if(_.length>=3&&_.charCodeAt(0)===47&&_.charCodeAt(2)===58){const W=_.charCodeAt(1);W>=65&&W<=90&&(_=`/${String.fromCharCode(W+32)}:${_.substr(3)}`)}else if(_.length>=2&&_.charCodeAt(1)===58){const W=_.charCodeAt(0);W>=65&&W<=90&&(_=`${String.fromCharCode(W+32)}:${_.substr(2)}`)}I+=v(_,!0,!1)}return Te&&(I+="?",I+=v(Te,!1,!1)),q&&(I+="#",I+=T?q:A(q,!1,!1)),I}function C(S){try{return decodeURIComponent(S)}catch{return S.length>3?S.substr(0,3)+C(S.substr(3)):S}}const F=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function ie(S){return S.match(F)?S.replace(F,T=>C(T)):S}var _e=t(470);const ye=_e.posix||_e,Fe="/";var Ce;(function(S){S.joinPath=function(T,...v){return T.with({path:ye.join(T.path,...v)})},S.resolvePath=function(T,...v){let I=T.path,O=!1;I[0]!==Fe&&(I=Fe+I,O=!0);let L=ye.resolve(I,...v);return O&&L[0]===Fe&&!T.authority&&(L=L.substring(1)),T.with({path:L})},S.dirname=function(T){if(T.path.length===0||T.path===Fe)return T;let v=ye.dirname(T.path);return v.length===1&&v.charCodeAt(0)===46&&(v=""),T.with({path:v})},S.basename=function(T){return ye.basename(T.path)},S.extname=function(T){return ye.extname(T.path)}})(Ce||(Ce={}))})(),Xu=r})();const{URI:yt,Utils:un}=Xu;var it;(function(n){n.basename=un.basename,n.dirname=un.dirname,n.extname=un.extname,n.joinPath=un.joinPath,n.resolvePath=un.resolvePath;function e(i,s){return(i==null?void 0:i.toString())===(s==null?void 0:s.toString())}n.equals=e;function t(i,s){const a=typeof i=="string"?i:i.path,o=typeof s=="string"?s:s.path,l=a.split("/").filter(f=>f.length>0),u=o.split("/").filter(f=>f.length>0);let c=0;for(;c<l.length&&l[c]===u[c];c++);const d="../".repeat(l.length-c),h=u.slice(c).join("/");return d+h}n.relative=t;function r(i){return yt.parse(i.toString()).toString()}n.normalize=r})(it||(it={}));var U;(function(n){n[n.Changed=0]="Changed",n[n.Parsed=1]="Parsed",n[n.IndexedContent=2]="IndexedContent",n[n.ComputedScopes=3]="ComputedScopes",n[n.Linked=4]="Linked",n[n.IndexedReferences=5]="IndexedReferences",n[n.Validated=6]="Validated"})(U||(U={}));class Em{constructor(e){this.serviceRegistry=e.ServiceRegistry,this.textDocuments=e.workspace.TextDocuments,this.fileSystemProvider=e.workspace.FileSystemProvider}async fromUri(e,t=V.CancellationToken.None){const r=await this.fileSystemProvider.readFile(e);return this.createAsync(e,r,t)}fromTextDocument(e,t,r){return t=t??yt.parse(e.uri),V.CancellationToken.is(r)?this.createAsync(t,e,r):this.create(t,e,r)}fromString(e,t,r){return V.CancellationToken.is(r)?this.createAsync(t,e,r):this.create(t,e,r)}fromModel(e,t){return this.create(t,{$model:e})}create(e,t,r){if(typeof t=="string"){const i=this.parse(e,t,r);return this.createLangiumDocument(i,e,void 0,t)}else if("$model"in t){const i={value:t.$model,parserErrors:[],lexerErrors:[]};return this.createLangiumDocument(i,e)}else{const i=this.parse(e,t.getText(),r);return this.createLangiumDocument(i,e,t)}}async createAsync(e,t,r){if(typeof t=="string"){const i=await this.parseAsync(e,t,r);return this.createLangiumDocument(i,e,void 0,t)}else{const i=await this.parseAsync(e,t.getText(),r);return this.createLangiumDocument(i,e,t)}}createLangiumDocument(e,t,r,i){let s;if(r)s={parseResult:e,uri:t,state:U.Parsed,references:[],textDocument:r};else{const a=this.createTextDocumentGetter(t,i);s={parseResult:e,uri:t,state:U.Parsed,references:[],get textDocument(){return a()}}}return e.value.$document=s,s}async update(e,t){var r,i;const s=(r=e.parseResult.value.$cstNode)===null||r===void 0?void 0:r.root.fullText,a=(i=this.textDocuments)===null||i===void 0?void 0:i.get(e.uri.toString()),o=a?a.getText():await this.fileSystemProvider.readFile(e.uri);if(a)Object.defineProperty(e,"textDocument",{value:a});else{const l=this.createTextDocumentGetter(e.uri,o);Object.defineProperty(e,"textDocument",{get:l})}return s!==o&&(e.parseResult=await this.parseAsync(e.uri,o,t),e.parseResult.value.$document=e),e.state=U.Parsed,e}parse(e,t,r){return this.serviceRegistry.getServices(e).parser.LangiumParser.parse(t,r)}parseAsync(e,t,r){return this.serviceRegistry.getServices(e).parser.AsyncParser.parse(t,r)}createTextDocumentGetter(e,t){const r=this.serviceRegistry;let i;return()=>i??(i=$s.create(e.toString(),r.getServices(e).LanguageMetaData.languageId,0,t??""))}}class vm{constructor(e){this.documentMap=new Map,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.serviceRegistry=e.ServiceRegistry}get all(){return ee(this.documentMap.values())}addDocument(e){const t=e.uri.toString();if(this.documentMap.has(t))throw new Error(`A document with the URI '${t}' is already present.`);this.documentMap.set(t,e)}getDocument(e){const t=e.toString();return this.documentMap.get(t)}async getOrCreateDocument(e,t){let r=this.getDocument(e);return r||(r=await this.langiumDocumentFactory.fromUri(e,t),this.addDocument(r),r)}createDocument(e,t,r){if(r)return this.langiumDocumentFactory.fromString(t,e,r).then(i=>(this.addDocument(i),i));{const i=this.langiumDocumentFactory.fromString(t,e);return this.addDocument(i),i}}hasDocument(e){return this.documentMap.has(e.toString())}invalidateDocument(e){const t=e.toString(),r=this.documentMap.get(t);return r&&(this.serviceRegistry.getServices(e).references.Linker.unlink(r),r.state=U.Changed,r.precomputedScopes=void 0,r.diagnostics=void 0),r}deleteDocument(e){const t=e.toString(),r=this.documentMap.get(t);return r&&(r.state=U.Changed,this.documentMap.delete(t)),r}}const Fi=Symbol("ref_resolving");class km{constructor(e){this.reflection=e.shared.AstReflection,this.langiumDocuments=()=>e.shared.workspace.LangiumDocuments,this.scopeProvider=e.references.ScopeProvider,this.astNodeLocator=e.workspace.AstNodeLocator}async link(e,t=V.CancellationToken.None){for(const r of wt(e.parseResult.value))await Ee(t),Pl(r).forEach(i=>this.doLink(i,e))}doLink(e,t){var r;const i=e.reference;if(i._ref===void 0){i._ref=Fi;try{const s=this.getCandidate(e);if(Sr(s))i._ref=s;else if(i._nodeDescription=s,this.langiumDocuments().hasDocument(s.documentUri)){const a=this.loadAstNode(s);i._ref=a??this.createLinkingError(e,s)}else i._ref=void 0}catch(s){console.error(`An error occurred while resolving reference to '${i.$refText}':`,s);const a=(r=s.message)!==null&&r!==void 0?r:String(s);i._ref=Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${i.$refText}': ${a}`})}t.references.push(i)}}unlink(e){for(const t of e.references)delete t._ref,delete t._nodeDescription;e.references=[]}getCandidate(e){const r=this.scopeProvider.getScope(e).getElement(e.reference.$refText);return r??this.createLinkingError(e)}buildReference(e,t,r,i){const s=this,a={$refNode:r,$refText:i,get ref(){var o;if(ae(this._ref))return this._ref;if(sd(this._nodeDescription)){const l=s.loadAstNode(this._nodeDescription);this._ref=l??s.createLinkingError({reference:a,container:e,property:t},this._nodeDescription)}else if(this._ref===void 0){this._ref=Fi;const l=Zi(e).$document,u=s.getLinkedNode({reference:a,container:e,property:t});if(u.error&&l&&l.state<U.ComputedScopes)return this._ref=void 0;this._ref=(o=u.node)!==null&&o!==void 0?o:u.error,this._nodeDescription=u.descr,l==null||l.references.push(this)}else if(this._ref===Fi)throw new Error(`Cyclic reference resolution detected: ${s.astNodeLocator.getAstNodePath(e)}/${t} (symbol '${i}')`);return ae(this._ref)?this._ref:void 0},get $nodeDescription(){return this._nodeDescription},get error(){return Sr(this._ref)?this._ref:void 0}};return a}getLinkedNode(e){var t;try{const r=this.getCandidate(e);if(Sr(r))return{error:r};const i=this.loadAstNode(r);return i?{node:i,descr:r}:{descr:r,error:this.createLinkingError(e,r)}}catch(r){console.error(`An error occurred while resolving reference to '${e.reference.$refText}':`,r);const i=(t=r.message)!==null&&t!==void 0?t:String(r);return{error:Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${e.reference.$refText}': ${i}`})}}}loadAstNode(e){if(e.node)return e.node;const t=this.langiumDocuments().getDocument(e.documentUri);if(t)return this.astNodeLocator.getAstNode(t.parseResult.value,e.path)}createLinkingError(e,t){const r=Zi(e.container).$document;r&&r.state<U.ComputedScopes&&console.warn(`Attempted reference resolution before document reached ComputedScopes state (${r.uri}).`);const i=this.reflection.getReferenceType(e);return Object.assign(Object.assign({},e),{message:`Could not resolve reference to ${i} named '${e.reference.$refText}'.`,targetDescription:t})}}function Sm(n){return typeof n.name=="string"}class xm{getName(e){if(Sm(e))return e.name}getNameNode(e){return Ul(e.$cstNode,"name")}}class Im{constructor(e){this.nameProvider=e.references.NameProvider,this.index=e.shared.workspace.IndexManager,this.nodeLocator=e.workspace.AstNodeLocator}findDeclaration(e){if(e){const t=Jd(e),r=e.astNode;if(t&&r){const i=r[t.feature];if(Ue(i))return i.ref;if(Array.isArray(i)){for(const s of i)if(Ue(s)&&s.$refNode&&s.$refNode.offset<=e.offset&&s.$refNode.end>=e.end)return s.ref}}if(r){const i=this.nameProvider.getNameNode(r);if(i&&(i===e||ld(e,i)))return r}}}findDeclarationNode(e){const t=this.findDeclaration(e);if(t!=null&&t.$cstNode){const r=this.nameProvider.getNameNode(t);return r??t.$cstNode}}findReferences(e,t){const r=[];if(t.includeDeclaration){const s=this.getReferenceToSelf(e);s&&r.push(s)}let i=this.index.findAllReferences(e,this.nodeLocator.getAstNodePath(e));return t.documentUri&&(i=i.filter(s=>it.equals(s.sourceUri,t.documentUri))),r.push(...i),ee(r)}getReferenceToSelf(e){const t=this.nameProvider.getNameNode(e);if(t){const r=et(e),i=this.nodeLocator.getAstNodePath(e);return{sourceUri:r.uri,sourcePath:i,targetUri:r.uri,targetPath:i,segment:Ur(t),local:!0}}}}class si{constructor(e){if(this.map=new Map,e)for(const[t,r]of e)this.add(t,r)}get size(){return Xi.sum(ee(this.map.values()).map(e=>e.length))}clear(){this.map.clear()}delete(e,t){if(t===void 0)return this.map.delete(e);{const r=this.map.get(e);if(r){const i=r.indexOf(t);if(i>=0)return r.length===1?this.map.delete(e):r.splice(i,1),!0}return!1}}get(e){var t;return(t=this.map.get(e))!==null&&t!==void 0?t:[]}has(e,t){if(t===void 0)return this.map.has(e);{const r=this.map.get(e);return r?r.indexOf(t)>=0:!1}}add(e,t){return this.map.has(e)?this.map.get(e).push(t):this.map.set(e,[t]),this}addAll(e,t){return this.map.has(e)?this.map.get(e).push(...t):this.map.set(e,Array.from(t)),this}forEach(e){this.map.forEach((t,r)=>t.forEach(i=>e(i,r,this)))}[Symbol.iterator](){return this.entries().iterator()}entries(){return ee(this.map.entries()).flatMap(([e,t])=>t.map(r=>[e,r]))}keys(){return ee(this.map.keys())}values(){return ee(this.map.values()).flat()}entriesGroupedByKey(){return ee(this.map.entries())}}class el{get size(){return this.map.size}constructor(e){if(this.map=new Map,this.inverse=new Map,e)for(const[t,r]of e)this.set(t,r)}clear(){this.map.clear(),this.inverse.clear()}set(e,t){return this.map.set(e,t),this.inverse.set(t,e),this}get(e){return this.map.get(e)}getKey(e){return this.inverse.get(e)}delete(e){const t=this.map.get(e);return t!==void 0?(this.map.delete(e),this.inverse.delete(t),!0):!1}}class Cm{constructor(e){this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider}async computeExports(e,t=V.CancellationToken.None){return this.computeExportsForNode(e.parseResult.value,e,void 0,t)}async computeExportsForNode(e,t,r=Gs,i=V.CancellationToken.None){const s=[];this.exportNode(e,s,t);for(const a of r(e))await Ee(i),this.exportNode(a,s,t);return s}exportNode(e,t,r){const i=this.nameProvider.getName(e);i&&t.push(this.descriptions.createDescription(e,i,r))}async computeLocalScopes(e,t=V.CancellationToken.None){const r=e.parseResult.value,i=new si;for(const s of Zn(r))await Ee(t),this.processNode(s,e,i);return i}processNode(e,t,r){const i=e.$container;if(i){const s=this.nameProvider.getName(e);s&&r.add(i,this.descriptions.createDescription(e,s,t))}}}class tl{constructor(e,t,r){var i;this.elements=e,this.outerScope=t,this.caseInsensitive=(i=r==null?void 0:r.caseInsensitive)!==null&&i!==void 0?i:!1}getAllElements(){return this.outerScope?this.elements.concat(this.outerScope.getAllElements()):this.elements}getElement(e){const t=this.caseInsensitive?this.elements.find(r=>r.name.toLowerCase()===e.toLowerCase()):this.elements.find(r=>r.name===e);if(t)return t;if(this.outerScope)return this.outerScope.getElement(e)}}class $m{constructor(e,t,r){var i;this.elements=new Map,this.caseInsensitive=(i=r==null?void 0:r.caseInsensitive)!==null&&i!==void 0?i:!1;for(const s of e){const a=this.caseInsensitive?s.name.toLowerCase():s.name;this.elements.set(a,s)}this.outerScope=t}getElement(e){const t=this.caseInsensitive?e.toLowerCase():e,r=this.elements.get(t);if(r)return r;if(this.outerScope)return this.outerScope.getElement(e)}getAllElements(){let e=ee(this.elements.values());return this.outerScope&&(e=e.concat(this.outerScope.getAllElements())),e}}class Ju{constructor(){this.toDispose=[],this.isDisposed=!1}onDispose(e){this.toDispose.push(e)}dispose(){this.throwIfDisposed(),this.clear(),this.isDisposed=!0,this.toDispose.forEach(e=>e.dispose())}throwIfDisposed(){if(this.isDisposed)throw new Error("This cache has already been disposed")}}class Nm extends Ju{constructor(){super(...arguments),this.cache=new Map}has(e){return this.throwIfDisposed(),this.cache.has(e)}set(e,t){this.throwIfDisposed(),this.cache.set(e,t)}get(e,t){if(this.throwIfDisposed(),this.cache.has(e))return this.cache.get(e);if(t){const r=t();return this.cache.set(e,r),r}else return}delete(e){return this.throwIfDisposed(),this.cache.delete(e)}clear(){this.throwIfDisposed(),this.cache.clear()}}class wm extends Ju{constructor(e){super(),this.cache=new Map,this.converter=e??(t=>t)}has(e,t){return this.throwIfDisposed(),this.cacheForContext(e).has(t)}set(e,t,r){this.throwIfDisposed(),this.cacheForContext(e).set(t,r)}get(e,t,r){this.throwIfDisposed();const i=this.cacheForContext(e);if(i.has(t))return i.get(t);if(r){const s=r();return i.set(t,s),s}else return}delete(e,t){return this.throwIfDisposed(),this.cacheForContext(e).delete(t)}clear(e){if(this.throwIfDisposed(),e){const t=this.converter(e);this.cache.delete(t)}else this.cache.clear()}cacheForContext(e){const t=this.converter(e);let r=this.cache.get(t);return r||(r=new Map,this.cache.set(t,r)),r}}class _m extends Nm{constructor(e,t){super(),t?(this.toDispose.push(e.workspace.DocumentBuilder.onBuildPhase(t,()=>{this.clear()})),this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((r,i)=>{i.length>0&&this.clear()}))):this.toDispose.push(e.workspace.DocumentBuilder.onUpdate(()=>{this.clear()}))}}class Lm{constructor(e){this.reflection=e.shared.AstReflection,this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider,this.indexManager=e.shared.workspace.IndexManager,this.globalScopeCache=new _m(e.shared)}getScope(e){const t=[],r=this.reflection.getReferenceType(e),i=et(e.container).precomputedScopes;if(i){let a=e.container;do{const o=i.get(a);o.length>0&&t.push(ee(o).filter(l=>this.reflection.isSubtype(l.type,r))),a=a.$container}while(a)}let s=this.getGlobalScope(r,e);for(let a=t.length-1;a>=0;a--)s=this.createScope(t[a],s);return s}createScope(e,t,r){return new tl(ee(e),t,r)}createScopeForNodes(e,t,r){const i=ee(e).map(s=>{const a=this.nameProvider.getName(s);if(a)return this.descriptions.createDescription(s,a)}).nonNullable();return new tl(i,t,r)}getGlobalScope(e,t){return this.globalScopeCache.get(e,()=>new $m(this.indexManager.allElements(e)))}}function Om(n){return typeof n.$comment=="string"}function nl(n){return typeof n=="object"&&!!n&&("$ref"in n||"$error"in n)}class bm{constructor(e){this.ignoreProperties=new Set(["$container","$containerProperty","$containerIndex","$document","$cstNode"]),this.langiumDocuments=e.shared.workspace.LangiumDocuments,this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider,this.commentProvider=e.documentation.CommentProvider}serialize(e,t){const r=t??{},i=t==null?void 0:t.replacer,s=(o,l)=>this.replacer(o,l,r),a=i?(o,l)=>i(o,l,s):s;try{return this.currentDocument=et(e),JSON.stringify(e,a,t==null?void 0:t.space)}finally{this.currentDocument=void 0}}deserialize(e,t){const r=t??{},i=JSON.parse(e);return this.linkNode(i,i,r),i}replacer(e,t,{refText:r,sourceText:i,textRegions:s,comments:a,uriConverter:o}){var l,u,c,d;if(!this.ignoreProperties.has(e))if(Ue(t)){const h=t.ref,f=r?t.$refText:void 0;if(h){const m=et(h);let g="";this.currentDocument&&this.currentDocument!==m&&(o?g=o(m.uri,t):g=m.uri.toString());const A=this.astNodeLocator.getAstNodePath(h);return{$ref:`${g}#${A}`,$refText:f}}else return{$error:(u=(l=t.error)===null||l===void 0?void 0:l.message)!==null&&u!==void 0?u:"Could not resolve reference",$refText:f}}else if(ae(t)){let h;if(s&&(h=this.addAstNodeRegionWithAssignmentsTo(Object.assign({},t)),(!e||t.$document)&&(h!=null&&h.$textRegion)&&(h.$textRegion.documentURI=(c=this.currentDocument)===null||c===void 0?void 0:c.uri.toString())),i&&!e&&(h??(h=Object.assign({},t)),h.$sourceText=(d=t.$cstNode)===null||d===void 0?void 0:d.text),a){h??(h=Object.assign({},t));const f=this.commentProvider.getComment(t);f&&(h.$comment=f.replace(/\r/g,""))}return h??t}else return t}addAstNodeRegionWithAssignmentsTo(e){const t=r=>({offset:r.offset,end:r.end,length:r.length,range:r.range});if(e.$cstNode){const r=e.$textRegion=t(e.$cstNode),i=r.assignments={};return Object.keys(e).filter(s=>!s.startsWith("$")).forEach(s=>{const a=qd(e.$cstNode,s).map(t);a.length!==0&&(i[s]=a)}),e}}linkNode(e,t,r,i,s,a){for(const[l,u]of Object.entries(e))if(Array.isArray(u))for(let c=0;c<u.length;c++){const d=u[c];nl(d)?u[c]=this.reviveReference(e,l,t,d,r):ae(d)&&this.linkNode(d,t,r,e,l,c)}else nl(u)?e[l]=this.reviveReference(e,l,t,u,r):ae(u)&&this.linkNode(u,t,r,e,l);const o=e;o.$container=i,o.$containerProperty=s,o.$containerIndex=a}reviveReference(e,t,r,i,s){let a=i.$refText,o=i.$error;if(i.$ref){const l=this.getRefNode(r,i.$ref,s.uriConverter);if(ae(l))return a||(a=this.nameProvider.getName(l)),{$refText:a??"",ref:l};o=l}if(o){const l={$refText:a??""};return l.error={container:e,property:t,message:o,reference:l},l}else return}getRefNode(e,t,r){try{const i=t.indexOf("#");if(i===0){const l=this.astNodeLocator.getAstNode(e,t.substring(1));return l||"Could not resolve path: "+t}if(i<0){const l=r?r(t):yt.parse(t),u=this.langiumDocuments.getDocument(l);return u?u.parseResult.value:"Could not find document for URI: "+t}const s=r?r(t.substring(0,i)):yt.parse(t.substring(0,i)),a=this.langiumDocuments.getDocument(s);if(!a)return"Could not find document for URI: "+t;if(i===t.length-1)return a.parseResult.value;const o=this.astNodeLocator.getAstNode(a.parseResult.value,t.substring(i+1));return o||"Could not resolve URI: "+t}catch(i){return String(i)}}}class Pm{get map(){return this.fileExtensionMap}constructor(e){this.languageIdMap=new Map,this.fileExtensionMap=new Map,this.textDocuments=e==null?void 0:e.workspace.TextDocuments}register(e){const t=e.LanguageMetaData;for(const r of t.fileExtensions)this.fileExtensionMap.has(r)&&console.warn(`The file extension ${r} is used by multiple languages. It is now assigned to '${t.languageId}'.`),this.fileExtensionMap.set(r,e);this.languageIdMap.set(t.languageId,e),this.languageIdMap.size===1?this.singleton=e:this.singleton=void 0}getServices(e){var t,r;if(this.singleton!==void 0)return this.singleton;if(this.languageIdMap.size===0)throw new Error("The service registry is empty. Use `register` to register the services of a language.");const i=(r=(t=this.textDocuments)===null||t===void 0?void 0:t.get(e))===null||r===void 0?void 0:r.languageId;if(i!==void 0){const o=this.languageIdMap.get(i);if(o)return o}const s=it.extname(e),a=this.fileExtensionMap.get(s);if(!a)throw i?new Error(`The service registry contains no services for the extension '${s}' for language '${i}'.`):new Error(`The service registry contains no services for the extension '${s}'.`);return a}hasServices(e){try{return this.getServices(e),!0}catch{return!1}}get all(){return Array.from(this.languageIdMap.values())}}function Bn(n){return{code:n}}var ai;(function(n){n.all=["fast","slow","built-in"]})(ai||(ai={}));class Mm{constructor(e){this.entries=new si,this.entriesBefore=[],this.entriesAfter=[],this.reflection=e.shared.AstReflection}register(e,t=this,r="fast"){if(r==="built-in")throw new Error("The 'built-in' category is reserved for lexer, parser, and linker errors.");for(const[i,s]of Object.entries(e)){const a=s;if(Array.isArray(a))for(const o of a){const l={check:this.wrapValidationException(o,t),category:r};this.addEntry(i,l)}else if(typeof a=="function"){const o={check:this.wrapValidationException(a,t),category:r};this.addEntry(i,o)}else Qn()}}wrapValidationException(e,t){return async(r,i,s)=>{await this.handleException(()=>e.call(t,r,i,s),"An error occurred during validation",i,r)}}async handleException(e,t,r,i){try{await e()}catch(s){if(Ei(s))throw s;console.error(`${t}:`,s),s instanceof Error&&s.stack&&console.error(s.stack);const a=s instanceof Error?s.message:String(s);r("error",`${t}: ${a}`,{node:i})}}addEntry(e,t){if(e==="AstNode"){this.entries.add("AstNode",t);return}for(const r of this.reflection.getAllSubTypes(e))this.entries.add(r,t)}getChecks(e,t){let r=ee(this.entries.get(e)).concat(this.entries.get("AstNode"));return t&&(r=r.filter(i=>t.includes(i.category))),r.map(i=>i.check)}registerBeforeDocument(e,t=this){this.entriesBefore.push(this.wrapPreparationException(e,"An error occurred during set-up of the validation",t))}registerAfterDocument(e,t=this){this.entriesAfter.push(this.wrapPreparationException(e,"An error occurred during tear-down of the validation",t))}wrapPreparationException(e,t,r){return async(i,s,a,o)=>{await this.handleException(()=>e.call(r,i,s,a,o),t,s,i)}}get checksBefore(){return this.entriesBefore}get checksAfter(){return this.entriesAfter}}class Dm{constructor(e){this.validationRegistry=e.validation.ValidationRegistry,this.metadata=e.LanguageMetaData}async validateDocument(e,t={},r=V.CancellationToken.None){const i=e.parseResult,s=[];if(await Ee(r),(!t.categories||t.categories.includes("built-in"))&&(this.processLexingErrors(i,s,t),t.stopAfterLexingErrors&&s.some(a=>{var o;return((o=a.data)===null||o===void 0?void 0:o.code)===Oe.LexingError})||(this.processParsingErrors(i,s,t),t.stopAfterParsingErrors&&s.some(a=>{var o;return((o=a.data)===null||o===void 0?void 0:o.code)===Oe.ParsingError}))||(this.processLinkingErrors(e,s,t),t.stopAfterLinkingErrors&&s.some(a=>{var o;return((o=a.data)===null||o===void 0?void 0:o.code)===Oe.LinkingError}))))return s;try{s.push(...await this.validateAst(i.value,t,r))}catch(a){if(Ei(a))throw a;console.error("An error occurred during validation:",a)}return await Ee(r),s}processLexingErrors(e,t,r){var i,s,a;const o=[...e.lexerErrors,...(s=(i=e.lexerReport)===null||i===void 0?void 0:i.diagnostics)!==null&&s!==void 0?s:[]];for(const l of o){const u=(a=l.severity)!==null&&a!==void 0?a:"error",c={severity:Gi(u),range:{start:{line:l.line-1,character:l.column-1},end:{line:l.line-1,character:l.column+l.length-1}},message:l.message,data:Gm(u),source:this.getSource()};t.push(c)}}processParsingErrors(e,t,r){for(const i of e.parserErrors){let s;if(isNaN(i.token.startOffset)){if("previousToken"in i){const a=i.previousToken;if(isNaN(a.startOffset)){const o={line:0,character:0};s={start:o,end:o}}else{const o={line:a.endLine-1,character:a.endColumn};s={start:o,end:o}}}}else s=Qi(i.token);if(s){const a={severity:Gi("error"),range:s,message:i.message,data:Bn(Oe.ParsingError),source:this.getSource()};t.push(a)}}}processLinkingErrors(e,t,r){for(const i of e.references){const s=i.error;if(s){const a={node:s.container,property:s.property,index:s.index,data:{code:Oe.LinkingError,containerType:s.container.$type,property:s.property,refText:s.reference.$refText}};t.push(this.toDiagnostic("error",s.message,a))}}}async validateAst(e,t,r=V.CancellationToken.None){const i=[],s=(a,o,l)=>{i.push(this.toDiagnostic(a,o,l))};return await this.validateAstBefore(e,t,s,r),await this.validateAstNodes(e,t,s,r),await this.validateAstAfter(e,t,s,r),i}async validateAstBefore(e,t,r,i=V.CancellationToken.None){var s;const a=this.validationRegistry.checksBefore;for(const o of a)await Ee(i),await o(e,r,(s=t.categories)!==null&&s!==void 0?s:[],i)}async validateAstNodes(e,t,r,i=V.CancellationToken.None){await Promise.all(wt(e).map(async s=>{await Ee(i);const a=this.validationRegistry.getChecks(s.$type,t.categories);for(const o of a)await o(s,r,i)}))}async validateAstAfter(e,t,r,i=V.CancellationToken.None){var s;const a=this.validationRegistry.checksAfter;for(const o of a)await Ee(i),await o(e,r,(s=t.categories)!==null&&s!==void 0?s:[],i)}toDiagnostic(e,t,r){return{message:t,range:Fm(r),severity:Gi(e),code:r.code,codeDescription:r.codeDescription,tags:r.tags,relatedInformation:r.relatedInformation,data:r.data,source:this.getSource()}}getSource(){return this.metadata.languageId}}function Fm(n){if(n.range)return n.range;let e;return typeof n.property=="string"?e=Ul(n.node.$cstNode,n.property,n.index):typeof n.keyword=="string"&&(e=Yd(n.node.$cstNode,n.keyword,n.index)),e??(e=n.node.$cstNode),e?e.range:{start:{line:0,character:0},end:{line:0,character:0}}}function Gi(n){switch(n){case"error":return 1;case"warning":return 2;case"info":return 3;case"hint":return 4;default:throw new Error("Invalid diagnostic severity: "+n)}}function Gm(n){switch(n){case"error":return Bn(Oe.LexingError);case"warning":return Bn(Oe.LexingWarning);case"info":return Bn(Oe.LexingInfo);case"hint":return Bn(Oe.LexingHint);default:throw new Error("Invalid diagnostic severity: "+n)}}var Oe;(function(n){n.LexingError="lexing-error",n.LexingWarning="lexing-warning",n.LexingInfo="lexing-info",n.LexingHint="lexing-hint",n.ParsingError="parsing-error",n.LinkingError="linking-error"})(Oe||(Oe={}));class Um{constructor(e){this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider}createDescription(e,t,r){const i=r??et(e);t??(t=this.nameProvider.getName(e));const s=this.astNodeLocator.getAstNodePath(e);if(!t)throw new Error(`Node at path ${s} has no name.`);let a;const o=()=>{var l;return a??(a=Ur((l=this.nameProvider.getNameNode(e))!==null&&l!==void 0?l:e.$cstNode))};return{node:e,name:t,get nameSegment(){return o()},selectionSegment:Ur(e.$cstNode),type:e.$type,documentUri:i.uri,path:s}}}class Bm{constructor(e){this.nodeLocator=e.workspace.AstNodeLocator}async createDescriptions(e,t=V.CancellationToken.None){const r=[],i=e.parseResult.value;for(const s of wt(i))await Ee(t),Pl(s).filter(a=>!Sr(a)).forEach(a=>{const o=this.createDescription(a);o&&r.push(o)});return r}createDescription(e){const t=e.reference.$nodeDescription,r=e.reference.$refNode;if(!t||!r)return;const i=et(e.container).uri;return{sourceUri:i,sourcePath:this.nodeLocator.getAstNodePath(e.container),targetUri:t.documentUri,targetPath:t.path,segment:Ur(r),local:it.equals(t.documentUri,i)}}}class Vm{constructor(){this.segmentSeparator="/",this.indexSeparator="@"}getAstNodePath(e){if(e.$container){const t=this.getAstNodePath(e.$container),r=this.getPathSegment(e);return t+this.segmentSeparator+r}return""}getPathSegment({$containerProperty:e,$containerIndex:t}){if(!e)throw new Error("Missing '$containerProperty' in AST node.");return t!==void 0?e+this.indexSeparator+t:e}getAstNode(e,t){return t.split(this.segmentSeparator).reduce((i,s)=>{if(!i||s.length===0)return i;const a=s.indexOf(this.indexSeparator);if(a>0){const o=s.substring(0,a),l=parseInt(s.substring(a+1)),u=i[o];return u==null?void 0:u[l]}return i[s]},e)}}var Wm=zu();class jm{constructor(e){this._ready=new ra,this.settings={},this.workspaceConfig=!1,this.onConfigurationSectionUpdateEmitter=new Wm.Emitter,this.serviceRegistry=e.ServiceRegistry}get ready(){return this._ready.promise}initialize(e){var t,r;this.workspaceConfig=(r=(t=e.capabilities.workspace)===null||t===void 0?void 0:t.configuration)!==null&&r!==void 0?r:!1}async initialized(e){if(this.workspaceConfig){if(e.register){const t=this.serviceRegistry.all;e.register({section:t.map(r=>this.toSectionName(r.LanguageMetaData.languageId))})}if(e.fetchConfiguration){const t=this.serviceRegistry.all.map(i=>({section:this.toSectionName(i.LanguageMetaData.languageId)})),r=await e.fetchConfiguration(t);t.forEach((i,s)=>{this.updateSectionConfiguration(i.section,r[s])})}}this._ready.resolve()}updateConfiguration(e){e.settings&&Object.keys(e.settings).forEach(t=>{const r=e.settings[t];this.updateSectionConfiguration(t,r),this.onConfigurationSectionUpdateEmitter.fire({section:t,configuration:r})})}updateSectionConfiguration(e,t){this.settings[e]=t}async getConfiguration(e,t){await this.ready;const r=this.toSectionName(e);if(this.settings[r])return this.settings[r][t]}toSectionName(e){return`${e}`}get onConfigurationSectionUpdate(){return this.onConfigurationSectionUpdateEmitter.event}}var Hn;(function(n){function e(t){return{dispose:async()=>await t()}}n.create=e})(Hn||(Hn={}));class Km{constructor(e){this.updateBuildOptions={validation:{categories:["built-in","fast"]}},this.updateListeners=[],this.buildPhaseListeners=new si,this.documentPhaseListeners=new si,this.buildState=new Map,this.documentBuildWaiters=new Map,this.currentState=U.Changed,this.langiumDocuments=e.workspace.LangiumDocuments,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.textDocuments=e.workspace.TextDocuments,this.indexManager=e.workspace.IndexManager,this.serviceRegistry=e.ServiceRegistry}async build(e,t={},r=V.CancellationToken.None){var i,s;for(const a of e){const o=a.uri.toString();if(a.state===U.Validated){if(typeof t.validation=="boolean"&&t.validation)a.state=U.IndexedReferences,a.diagnostics=void 0,this.buildState.delete(o);else if(typeof t.validation=="object"){const l=this.buildState.get(o),u=(i=l==null?void 0:l.result)===null||i===void 0?void 0:i.validationChecks;if(u){const d=((s=t.validation.categories)!==null&&s!==void 0?s:ai.all).filter(h=>!u.includes(h));d.length>0&&(this.buildState.set(o,{completed:!1,options:{validation:Object.assign(Object.assign({},t.validation),{categories:d})},result:l.result}),a.state=U.IndexedReferences)}}}else this.buildState.delete(o)}this.currentState=U.Changed,await this.emitUpdate(e.map(a=>a.uri),[]),await this.buildDocuments(e,t,r)}async update(e,t,r=V.CancellationToken.None){this.currentState=U.Changed;for(const a of t)this.langiumDocuments.deleteDocument(a),this.buildState.delete(a.toString()),this.indexManager.remove(a);for(const a of e){if(!this.langiumDocuments.invalidateDocument(a)){const l=this.langiumDocumentFactory.fromModel({$type:"INVALID"},a);l.state=U.Changed,this.langiumDocuments.addDocument(l)}this.buildState.delete(a.toString())}const i=ee(e).concat(t).map(a=>a.toString()).toSet();this.langiumDocuments.all.filter(a=>!i.has(a.uri.toString())&&this.shouldRelink(a,i)).forEach(a=>{this.serviceRegistry.getServices(a.uri).references.Linker.unlink(a),a.state=Math.min(a.state,U.ComputedScopes),a.diagnostics=void 0}),await this.emitUpdate(e,t),await Ee(r);const s=this.sortDocuments(this.langiumDocuments.all.filter(a=>{var o;return a.state<U.Linked||!(!((o=this.buildState.get(a.uri.toString()))===null||o===void 0)&&o.completed)}).toArray());await this.buildDocuments(s,this.updateBuildOptions,r)}async emitUpdate(e,t){await Promise.all(this.updateListeners.map(r=>r(e,t)))}sortDocuments(e){let t=0,r=e.length-1;for(;t<r;){for(;t<e.length&&this.hasTextDocument(e[t]);)t++;for(;r>=0&&!this.hasTextDocument(e[r]);)r--;t<r&&([e[t],e[r]]=[e[r],e[t]])}return e}hasTextDocument(e){var t;return!!(!((t=this.textDocuments)===null||t===void 0)&&t.get(e.uri))}shouldRelink(e,t){return e.references.some(r=>r.error!==void 0)?!0:this.indexManager.isAffected(e,t)}onUpdate(e){return this.updateListeners.push(e),Hn.create(()=>{const t=this.updateListeners.indexOf(e);t>=0&&this.updateListeners.splice(t,1)})}async buildDocuments(e,t,r){this.prepareBuild(e,t),await this.runCancelable(e,U.Parsed,r,s=>this.langiumDocumentFactory.update(s,r)),await this.runCancelable(e,U.IndexedContent,r,s=>this.indexManager.updateContent(s,r)),await this.runCancelable(e,U.ComputedScopes,r,async s=>{const a=this.serviceRegistry.getServices(s.uri).references.ScopeComputation;s.precomputedScopes=await a.computeLocalScopes(s,r)}),await this.runCancelable(e,U.Linked,r,s=>this.serviceRegistry.getServices(s.uri).references.Linker.link(s,r)),await this.runCancelable(e,U.IndexedReferences,r,s=>this.indexManager.updateReferences(s,r));const i=e.filter(s=>this.shouldValidate(s));await this.runCancelable(i,U.Validated,r,s=>this.validate(s,r));for(const s of e){const a=this.buildState.get(s.uri.toString());a&&(a.completed=!0)}}prepareBuild(e,t){for(const r of e){const i=r.uri.toString(),s=this.buildState.get(i);(!s||s.completed)&&this.buildState.set(i,{completed:!1,options:t,result:s==null?void 0:s.result})}}async runCancelable(e,t,r,i){const s=e.filter(o=>o.state<t);for(const o of s)await Ee(r),await i(o),o.state=t,await this.notifyDocumentPhase(o,t,r);const a=e.filter(o=>o.state===t);await this.notifyBuildPhase(a,t,r),this.currentState=t}onBuildPhase(e,t){return this.buildPhaseListeners.add(e,t),Hn.create(()=>{this.buildPhaseListeners.delete(e,t)})}onDocumentPhase(e,t){return this.documentPhaseListeners.add(e,t),Hn.create(()=>{this.documentPhaseListeners.delete(e,t)})}waitUntil(e,t,r){let i;if(t&&"path"in t?i=t:r=t,r??(r=V.CancellationToken.None),i){const s=this.langiumDocuments.getDocument(i);if(s&&s.state>e)return Promise.resolve(i)}return this.currentState>=e?Promise.resolve(void 0):r.isCancellationRequested?Promise.reject(ii):new Promise((s,a)=>{const o=this.onBuildPhase(e,()=>{if(o.dispose(),l.dispose(),i){const u=this.langiumDocuments.getDocument(i);s(u==null?void 0:u.uri)}else s(void 0)}),l=r.onCancellationRequested(()=>{o.dispose(),l.dispose(),a(ii)})})}async notifyDocumentPhase(e,t,r){const s=this.documentPhaseListeners.get(t).slice();for(const a of s)try{await a(e,r)}catch(o){if(!Ei(o))throw o}}async notifyBuildPhase(e,t,r){if(e.length===0)return;const s=this.buildPhaseListeners.get(t).slice();for(const a of s)await Ee(r),await a(e,r)}shouldValidate(e){return!!this.getBuildOptions(e).validation}async validate(e,t){var r,i;const s=this.serviceRegistry.getServices(e.uri).validation.DocumentValidator,a=this.getBuildOptions(e).validation,o=typeof a=="object"?a:void 0,l=await s.validateDocument(e,o,t);e.diagnostics?e.diagnostics.push(...l):e.diagnostics=l;const u=this.buildState.get(e.uri.toString());if(u){(r=u.result)!==null&&r!==void 0||(u.result={});const c=(i=o==null?void 0:o.categories)!==null&&i!==void 0?i:ai.all;u.result.validationChecks?u.result.validationChecks.push(...c):u.result.validationChecks=[...c]}}getBuildOptions(e){var t,r;return(r=(t=this.buildState.get(e.uri.toString()))===null||t===void 0?void 0:t.options)!==null&&r!==void 0?r:{}}}class Hm{constructor(e){this.symbolIndex=new Map,this.symbolByTypeIndex=new wm,this.referenceIndex=new Map,this.documents=e.workspace.LangiumDocuments,this.serviceRegistry=e.ServiceRegistry,this.astReflection=e.AstReflection}findAllReferences(e,t){const r=et(e).uri,i=[];return this.referenceIndex.forEach(s=>{s.forEach(a=>{it.equals(a.targetUri,r)&&a.targetPath===t&&i.push(a)})}),ee(i)}allElements(e,t){let r=ee(this.symbolIndex.keys());return t&&(r=r.filter(i=>!t||t.has(i))),r.map(i=>this.getFileDescriptions(i,e)).flat()}getFileDescriptions(e,t){var r;return t?this.symbolByTypeIndex.get(e,t,()=>{var s;return((s=this.symbolIndex.get(e))!==null&&s!==void 0?s:[]).filter(o=>this.astReflection.isSubtype(o.type,t))}):(r=this.symbolIndex.get(e))!==null&&r!==void 0?r:[]}remove(e){const t=e.toString();this.symbolIndex.delete(t),this.symbolByTypeIndex.clear(t),this.referenceIndex.delete(t)}async updateContent(e,t=V.CancellationToken.None){const i=await this.serviceRegistry.getServices(e.uri).references.ScopeComputation.computeExports(e,t),s=e.uri.toString();this.symbolIndex.set(s,i),this.symbolByTypeIndex.clear(s)}async updateReferences(e,t=V.CancellationToken.None){const i=await this.serviceRegistry.getServices(e.uri).workspace.ReferenceDescriptionProvider.createDescriptions(e,t);this.referenceIndex.set(e.uri.toString(),i)}isAffected(e,t){const r=this.referenceIndex.get(e.uri.toString());return r?r.some(i=>!i.local&&t.has(i.targetUri.toString())):!1}}class zm{constructor(e){this.initialBuildOptions={},this._ready=new ra,this.serviceRegistry=e.ServiceRegistry,this.langiumDocuments=e.workspace.LangiumDocuments,this.documentBuilder=e.workspace.DocumentBuilder,this.fileSystemProvider=e.workspace.FileSystemProvider,this.mutex=e.workspace.WorkspaceLock}get ready(){return this._ready.promise}get workspaceFolders(){return this.folders}initialize(e){var t;this.folders=(t=e.workspaceFolders)!==null&&t!==void 0?t:void 0}initialized(e){return this.mutex.write(t=>{var r;return this.initializeWorkspace((r=this.folders)!==null&&r!==void 0?r:[],t)})}async initializeWorkspace(e,t=V.CancellationToken.None){const r=await this.performStartup(e);await Ee(t),await this.documentBuilder.build(r,this.initialBuildOptions,t)}async performStartup(e){const t=this.serviceRegistry.all.flatMap(s=>s.LanguageMetaData.fileExtensions),r=[],i=s=>{r.push(s),this.langiumDocuments.hasDocument(s.uri)||this.langiumDocuments.addDocument(s)};return await this.loadAdditionalDocuments(e,i),await Promise.all(e.map(s=>[s,this.getRootFolder(s)]).map(async s=>this.traverseFolder(...s,t,i))),this._ready.resolve(),r}loadAdditionalDocuments(e,t){return Promise.resolve()}getRootFolder(e){return yt.parse(e.uri)}async traverseFolder(e,t,r,i){const s=await this.fileSystemProvider.readDirectory(t);await Promise.all(s.map(async a=>{if(this.includeEntry(e,a,r)){if(a.isDirectory)await this.traverseFolder(e,a.uri,r,i);else if(a.isFile){const o=await this.langiumDocuments.getOrCreateDocument(a.uri);i(o)}}}))}includeEntry(e,t,r){const i=it.basename(t.uri);if(i.startsWith("."))return!1;if(t.isDirectory)return i!=="node_modules"&&i!=="out";if(t.isFile){const s=it.extname(t.uri);return r.includes(s)}return!1}}class qm{buildUnexpectedCharactersMessage(e,t,r,i,s){return is.buildUnexpectedCharactersMessage(e,t,r,i,s)}buildUnableToPopLexerModeMessage(e){return is.buildUnableToPopLexerModeMessage(e)}}const Ym={mode:"full"};class Xm{constructor(e){this.errorMessageProvider=e.parser.LexerErrorMessageProvider,this.tokenBuilder=e.parser.TokenBuilder;const t=this.tokenBuilder.buildTokens(e.Grammar,{caseInsensitive:e.LanguageMetaData.caseInsensitive});this.tokenTypes=this.toTokenTypeDictionary(t);const r=rl(t)?Object.values(t):t,i=e.LanguageMetaData.mode==="production";this.chevrotainLexer=new de(r,{positionTracking:"full",skipValidations:i,errorMessageProvider:this.errorMessageProvider})}get definition(){return this.tokenTypes}tokenize(e,t=Ym){var r,i,s;const a=this.chevrotainLexer.tokenize(e);return{tokens:a.tokens,errors:a.errors,hidden:(r=a.groups.hidden)!==null&&r!==void 0?r:[],report:(s=(i=this.tokenBuilder).flushLexingReport)===null||s===void 0?void 0:s.call(i,e)}}toTokenTypeDictionary(e){if(rl(e))return e;const t=Qu(e)?Object.values(e.modes).flat():e,r={};return t.forEach(i=>r[i.name]=i),r}}function Jm(n){return Array.isArray(n)&&(n.length===0||"name"in n[0])}function Qu(n){return n&&"modes"in n&&"defaultMode"in n}function rl(n){return!Jm(n)&&!Qu(n)}function Qm(n,e,t){let r,i;typeof n=="string"?(i=e,r=t):(i=n.range.start,r=e),i||(i=P.create(0,0));const s=Zu(n),a=ia(r),o=tg({lines:s,position:i,options:a});return ag({index:0,tokens:o,position:i})}function Zm(n,e){const t=ia(e),r=Zu(n);if(r.length===0)return!1;const i=r[0],s=r[r.length-1],a=t.start,o=t.end;return!!(a!=null&&a.exec(i))&&!!(o!=null&&o.exec(s))}function Zu(n){let e="";return typeof n=="string"?e=n:e=n.text,e.split(Md)}const il=/\s*(@([\p{L}][\p{L}\p{N}]*)?)/uy,eg=/\{(@[\p{L}][\p{L}\p{N}]*)(\s*)([^\r\n}]+)?\}/gu;function tg(n){var e,t,r;const i=[];let s=n.position.line,a=n.position.character;for(let o=0;o<n.lines.length;o++){const l=o===0,u=o===n.lines.length-1;let c=n.lines[o],d=0;if(l&&n.options.start){const f=(e=n.options.start)===null||e===void 0?void 0:e.exec(c);f&&(d=f.index+f[0].length)}else{const f=(t=n.options.line)===null||t===void 0?void 0:t.exec(c);f&&(d=f.index+f[0].length)}if(u){const f=(r=n.options.end)===null||r===void 0?void 0:r.exec(c);f&&(c=c.substring(0,f.index))}if(c=c.substring(0,sg(c)),ws(c,d)>=c.length){if(i.length>0){const f=P.create(s,a);i.push({type:"break",content:"",range:b.create(f,f)})}}else{il.lastIndex=d;const f=il.exec(c);if(f){const m=f[0],g=f[1],A=P.create(s,a+d),y=P.create(s,a+d+m.length);i.push({type:"tag",content:g,range:b.create(A,y)}),d+=m.length,d=ws(c,d)}if(d<c.length){const m=c.substring(d),g=Array.from(m.matchAll(eg));i.push(...ng(g,m,s,a+d))}}s++,a=0}return i.length>0&&i[i.length-1].type==="break"?i.slice(0,-1):i}function ng(n,e,t,r){const i=[];if(n.length===0){const s=P.create(t,r),a=P.create(t,r+e.length);i.push({type:"text",content:e,range:b.create(s,a)})}else{let s=0;for(const o of n){const l=o.index,u=e.substring(s,l);u.length>0&&i.push({type:"text",content:e.substring(s,l),range:b.create(P.create(t,s+r),P.create(t,l+r))});let c=u.length+1;const d=o[1];if(i.push({type:"inline-tag",content:d,range:b.create(P.create(t,s+c+r),P.create(t,s+c+d.length+r))}),c+=d.length,o.length===4){c+=o[2].length;const h=o[3];i.push({type:"text",content:h,range:b.create(P.create(t,s+c+r),P.create(t,s+c+h.length+r))})}else i.push({type:"text",content:"",range:b.create(P.create(t,s+c+r),P.create(t,s+c+r))});s=l+o[0].length}const a=e.substring(s);a.length>0&&i.push({type:"text",content:a,range:b.create(P.create(t,s+r),P.create(t,s+r+a.length))})}return i}const rg=/\S/,ig=/\s*$/;function ws(n,e){const t=n.substring(e).match(rg);return t?e+t.index:n.length}function sg(n){const e=n.match(ig);if(e&&typeof e.index=="number")return e.index}function ag(n){var e,t,r,i;const s=P.create(n.position.line,n.position.character);if(n.tokens.length===0)return new sl([],b.create(s,s));const a=[];for(;n.index<n.tokens.length;){const u=og(n,a[a.length-1]);u&&a.push(u)}const o=(t=(e=a[0])===null||e===void 0?void 0:e.range.start)!==null&&t!==void 0?t:s,l=(i=(r=a[a.length-1])===null||r===void 0?void 0:r.range.end)!==null&&i!==void 0?i:s;return new sl(a,b.create(o,l))}function og(n,e){const t=n.tokens[n.index];if(t.type==="tag")return tc(n,!1);if(t.type==="text"||t.type==="inline-tag")return ec(n);lg(t,e),n.index++}function lg(n,e){if(e){const t=new rc("",n.range);"inlines"in e?e.inlines.push(t):e.content.inlines.push(t)}}function ec(n){let e=n.tokens[n.index];const t=e;let r=e;const i=[];for(;e&&e.type!=="break"&&e.type!=="tag";)i.push(ug(n)),r=e,e=n.tokens[n.index];return new _s(i,b.create(t.range.start,r.range.end))}function ug(n){return n.tokens[n.index].type==="inline-tag"?tc(n,!0):nc(n)}function tc(n,e){const t=n.tokens[n.index++],r=t.content.substring(1),i=n.tokens[n.index];if((i==null?void 0:i.type)==="text")if(e){const s=nc(n);return new Bi(r,new _s([s],s.range),e,b.create(t.range.start,s.range.end))}else{const s=ec(n);return new Bi(r,s,e,b.create(t.range.start,s.range.end))}else{const s=t.range;return new Bi(r,new _s([],s),e,s)}}function nc(n){const e=n.tokens[n.index++];return new rc(e.content,e.range)}function ia(n){if(!n)return ia({start:"/**",end:"*/",line:"*"});const{start:e,end:t,line:r}=n;return{start:Ui(e,!0),end:Ui(t,!1),line:Ui(r,!0)}}function Ui(n,e){if(typeof n=="string"||typeof n=="object"){const t=typeof n=="string"?hi(n):n.source;return e?new RegExp(`^\\s*${t}`):new RegExp(`\\s*${t}\\s*$`)}else return n}class sl{constructor(e,t){this.elements=e,this.range=t}getTag(e){return this.getAllTags().find(t=>t.name===e)}getTags(e){return this.getAllTags().filter(t=>t.name===e)}getAllTags(){return this.elements.filter(e=>"name"in e)}toString(){let e="";for(const t of this.elements)if(e.length===0)e=t.toString();else{const r=t.toString();e+=al(e)+r}return e.trim()}toMarkdown(e){let t="";for(const r of this.elements)if(t.length===0)t=r.toMarkdown(e);else{const i=r.toMarkdown(e);t+=al(t)+i}return t.trim()}}class Bi{constructor(e,t,r,i){this.name=e,this.content=t,this.inline=r,this.range=i}toString(){let e=`@${this.name}`;const t=this.content.toString();return this.content.inlines.length===1?e=`${e} ${t}`:this.content.inlines.length>1&&(e=`${e}
${t}`),this.inline?`{${e}}`:e}toMarkdown(e){var t,r;return(r=(t=e==null?void 0:e.renderTag)===null||t===void 0?void 0:t.call(e,this))!==null&&r!==void 0?r:this.toMarkdownDefault(e)}toMarkdownDefault(e){const t=this.content.toMarkdown(e);if(this.inline){const s=cg(this.name,t,e??{});if(typeof s=="string")return s}let r="";(e==null?void 0:e.tag)==="italic"||(e==null?void 0:e.tag)===void 0?r="*":(e==null?void 0:e.tag)==="bold"?r="**":(e==null?void 0:e.tag)==="bold-italic"&&(r="***");let i=`${r}@${this.name}${r}`;return this.content.inlines.length===1?i=`${i} — ${t}`:this.content.inlines.length>1&&(i=`${i}
${t}`),this.inline?`{${i}}`:i}}function cg(n,e,t){var r,i;if(n==="linkplain"||n==="linkcode"||n==="link"){const s=e.indexOf(" ");let a=e;if(s>0){const l=ws(e,s);a=e.substring(l),e=e.substring(0,s)}return(n==="linkcode"||n==="link"&&t.link==="code")&&(a=`\`${a}\``),(i=(r=t.renderLink)===null||r===void 0?void 0:r.call(t,e,a))!==null&&i!==void 0?i:dg(e,a)}}function dg(n,e){try{return yt.parse(n,!0),`[${e}](${n})`}catch{return n}}class _s{constructor(e,t){this.inlines=e,this.range=t}toString(){let e="";for(let t=0;t<this.inlines.length;t++){const r=this.inlines[t],i=this.inlines[t+1];e+=r.toString(),i&&i.range.start.line>r.range.start.line&&(e+=`
`)}return e}toMarkdown(e){let t="";for(let r=0;r<this.inlines.length;r++){const i=this.inlines[r],s=this.inlines[r+1];t+=i.toMarkdown(e),s&&s.range.start.line>i.range.start.line&&(t+=`
`)}return t}}class rc{constructor(e,t){this.text=e,this.range=t}toString(){return this.text}toMarkdown(){return this.text}}function al(n){return n.endsWith(`
`)?`
`:`

`}class fg{constructor(e){this.indexManager=e.shared.workspace.IndexManager,this.commentProvider=e.documentation.CommentProvider}getDocumentation(e){const t=this.commentProvider.getComment(e);if(t&&Zm(t))return Qm(t).toMarkdown({renderLink:(i,s)=>this.documentationLinkRenderer(e,i,s),renderTag:i=>this.documentationTagRenderer(e,i)})}documentationLinkRenderer(e,t,r){var i;const s=(i=this.findNameInPrecomputedScopes(e,t))!==null&&i!==void 0?i:this.findNameInGlobalScope(e,t);if(s&&s.nameSegment){const a=s.nameSegment.range.start.line+1,o=s.nameSegment.range.start.character+1,l=s.documentUri.with({fragment:`L${a},${o}`});return`[${r}](${l.toString()})`}else return}documentationTagRenderer(e,t){}findNameInPrecomputedScopes(e,t){const i=et(e).precomputedScopes;if(!i)return;let s=e;do{const o=i.get(s).find(l=>l.name===t);if(o)return o;s=s.$container}while(s)}findNameInGlobalScope(e,t){return this.indexManager.allElements().find(i=>i.name===t)}}class hg{constructor(e){this.grammarConfig=()=>e.parser.GrammarConfig}getComment(e){var t;return Om(e)?e.$comment:(t=fd(e.$cstNode,this.grammarConfig().multilineCommentRules))===null||t===void 0?void 0:t.text}}class pg{constructor(e){this.syncParser=e.parser.LangiumParser}parse(e,t){return Promise.resolve(this.syncParser.parse(e))}}class mg{constructor(){this.previousTokenSource=new V.CancellationTokenSource,this.writeQueue=[],this.readQueue=[],this.done=!0}write(e){this.cancelWrite();const t=Rm();return this.previousTokenSource=t,this.enqueue(this.writeQueue,e,t.token)}read(e){return this.enqueue(this.readQueue,e)}enqueue(e,t,r=V.CancellationToken.None){const i=new ra,s={action:t,deferred:i,cancellationToken:r};return e.push(s),this.performNextOperation(),i.promise}async performNextOperation(){if(!this.done)return;const e=[];if(this.writeQueue.length>0)e.push(this.writeQueue.shift());else if(this.readQueue.length>0)e.push(...this.readQueue.splice(0,this.readQueue.length));else return;this.done=!1,await Promise.all(e.map(async({action:t,deferred:r,cancellationToken:i})=>{try{const s=await Promise.resolve().then(()=>t(i));r.resolve(s)}catch(s){Ei(s)?r.resolve(void 0):r.reject(s)}})),this.done=!0,this.performNextOperation()}cancelWrite(){this.previousTokenSource.cancel()}}class gg{constructor(e){this.grammarElementIdMap=new el,this.tokenTypeIdMap=new el,this.grammar=e.Grammar,this.lexer=e.parser.Lexer,this.linker=e.references.Linker}dehydrate(e){return{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport?this.dehydrateLexerReport(e.lexerReport):void 0,parserErrors:e.parserErrors.map(t=>Object.assign(Object.assign({},t),{message:t.message})),value:this.dehydrateAstNode(e.value,this.createDehyrationContext(e.value))}}dehydrateLexerReport(e){return e}createDehyrationContext(e){const t=new Map,r=new Map;for(const i of wt(e))t.set(i,{});if(e.$cstNode)for(const i of Ji(e.$cstNode))r.set(i,{});return{astNodes:t,cstNodes:r}}dehydrateAstNode(e,t){const r=t.astNodes.get(e);r.$type=e.$type,r.$containerIndex=e.$containerIndex,r.$containerProperty=e.$containerProperty,e.$cstNode!==void 0&&(r.$cstNode=this.dehydrateCstNode(e.$cstNode,t));for(const[i,s]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(s)){const a=[];r[i]=a;for(const o of s)ae(o)?a.push(this.dehydrateAstNode(o,t)):Ue(o)?a.push(this.dehydrateReference(o,t)):a.push(o)}else ae(s)?r[i]=this.dehydrateAstNode(s,t):Ue(s)?r[i]=this.dehydrateReference(s,t):s!==void 0&&(r[i]=s);return r}dehydrateReference(e,t){const r={};return r.$refText=e.$refText,e.$refNode&&(r.$refNode=t.cstNodes.get(e.$refNode)),r}dehydrateCstNode(e,t){const r=t.cstNodes.get(e);return Cl(e)?r.fullText=e.fullText:r.grammarSource=this.getGrammarElementId(e.grammarSource),r.hidden=e.hidden,r.astNode=t.astNodes.get(e.astNode),qn(e)?r.content=e.content.map(i=>this.dehydrateCstNode(i,t)):Il(e)&&(r.tokenType=e.tokenType.name,r.offset=e.offset,r.length=e.length,r.startLine=e.range.start.line,r.startColumn=e.range.start.character,r.endLine=e.range.end.line,r.endColumn=e.range.end.character),r}hydrate(e){const t=e.value,r=this.createHydrationContext(t);return"$cstNode"in t&&this.hydrateCstNode(t.$cstNode,r),{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport,parserErrors:e.parserErrors,value:this.hydrateAstNode(t,r)}}createHydrationContext(e){const t=new Map,r=new Map;for(const s of wt(e))t.set(s,{});let i;if(e.$cstNode)for(const s of Ji(e.$cstNode)){let a;"fullText"in s?(a=new Mu(s.fullText),i=a):"content"in s?a=new ta:"tokenType"in s&&(a=this.hydrateCstLeafNode(s)),a&&(r.set(s,a),a.root=i)}return{astNodes:t,cstNodes:r}}hydrateAstNode(e,t){const r=t.astNodes.get(e);r.$type=e.$type,r.$containerIndex=e.$containerIndex,r.$containerProperty=e.$containerProperty,e.$cstNode&&(r.$cstNode=t.cstNodes.get(e.$cstNode));for(const[i,s]of Object.entries(e))if(!i.startsWith("$"))if(Array.isArray(s)){const a=[];r[i]=a;for(const o of s)ae(o)?a.push(this.setParent(this.hydrateAstNode(o,t),r)):Ue(o)?a.push(this.hydrateReference(o,r,i,t)):a.push(o)}else ae(s)?r[i]=this.setParent(this.hydrateAstNode(s,t),r):Ue(s)?r[i]=this.hydrateReference(s,r,i,t):s!==void 0&&(r[i]=s);return r}setParent(e,t){return e.$container=t,e}hydrateReference(e,t,r,i){return this.linker.buildReference(t,r,i.cstNodes.get(e.$refNode),e.$refText)}hydrateCstNode(e,t,r=0){const i=t.cstNodes.get(e);if(typeof e.grammarSource=="number"&&(i.grammarSource=this.getGrammarElement(e.grammarSource)),i.astNode=t.astNodes.get(e.astNode),qn(i))for(const s of e.content){const a=this.hydrateCstNode(s,t,r++);i.content.push(a)}return i}hydrateCstLeafNode(e){const t=this.getTokenType(e.tokenType),r=e.offset,i=e.length,s=e.startLine,a=e.startColumn,o=e.endLine,l=e.endColumn,u=e.hidden;return new xs(r,i,{start:{line:s,character:a},end:{line:o,character:l}},t,u)}getTokenType(e){return this.lexer.definition[e]}getGrammarElementId(e){if(e)return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.get(e)}getGrammarElement(e){return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.getKey(e)}createGrammarElementIdMap(){let e=0;for(const t of wt(this.grammar))pd(t)&&this.grammarElementIdMap.set(t,e++)}}function Et(n){return{documentation:{CommentProvider:e=>new hg(e),DocumentationProvider:e=>new fg(e)},parser:{AsyncParser:e=>new pg(e),GrammarConfig:e=>af(e),LangiumParser:e=>hm(e),CompletionParser:e=>fm(e),ValueConverter:()=>new Ku,TokenBuilder:()=>new ju,Lexer:e=>new Xm(e),ParserErrorMessageProvider:()=>new Gu,LexerErrorMessageProvider:()=>new qm},workspace:{AstNodeLocator:()=>new Vm,AstNodeDescriptionProvider:e=>new Um(e),ReferenceDescriptionProvider:e=>new Bm(e)},references:{Linker:e=>new km(e),NameProvider:()=>new xm,ScopeProvider:e=>new Lm(e),ScopeComputation:e=>new Cm(e),References:e=>new Im(e)},serializer:{Hydrator:e=>new gg(e),JsonSerializer:e=>new bm(e)},validation:{DocumentValidator:e=>new Dm(e),ValidationRegistry:e=>new Mm(e)},shared:()=>n.shared}}function vt(n){return{ServiceRegistry:e=>new Pm(e),workspace:{LangiumDocuments:e=>new vm(e),LangiumDocumentFactory:e=>new Em(e),DocumentBuilder:e=>new Km(e),IndexManager:e=>new Hm(e),WorkspaceManager:e=>new zm(e),FileSystemProvider:e=>n.fileSystemProvider(e),WorkspaceLock:()=>new mg,ConfigurationProvider:e=>new jm(e)}}}var ol;(function(n){n.merge=(e,t)=>oi(oi({},e),t)})(ol||(ol={}));function pe(n,e,t,r,i,s,a,o,l){const u=[n,e,t,r,i,s,a,o,l].reduce(oi,{});return ic(u)}const yg=Symbol("isProxy");function ic(n,e){const t=new Proxy({},{deleteProperty:()=>!1,set:()=>{throw new Error("Cannot set property on injected service container")},get:(r,i)=>i===yg?!0:ul(r,i,n,e||t),getOwnPropertyDescriptor:(r,i)=>(ul(r,i,n,e||t),Object.getOwnPropertyDescriptor(r,i)),has:(r,i)=>i in n,ownKeys:()=>[...Object.getOwnPropertyNames(n)]});return t}const ll=Symbol();function ul(n,e,t,r){if(e in n){if(n[e]instanceof Error)throw new Error("Construction failure. Please make sure that your dependencies are constructable.",{cause:n[e]});if(n[e]===ll)throw new Error('Cycle detected. Please make "'+String(e)+'" lazy. Visit https://langium.org/docs/reference/configuration-services/#resolving-cyclic-dependencies');return n[e]}else if(e in t){const i=t[e];n[e]=ll;try{n[e]=typeof i=="function"?i(r):ic(i,r)}catch(s){throw n[e]=s instanceof Error?s:void 0,s}return n[e]}else return}function oi(n,e){if(e){for(const[t,r]of Object.entries(e))if(r!==void 0){const i=n[t];i!==null&&r!==null&&typeof i=="object"&&typeof r=="object"?n[t]=oi(i,r):n[t]=r}}return n}class Tg{readFile(){throw new Error("No file system is available.")}async readDirectory(){return[]}}const kt={fileSystemProvider:()=>new Tg},Rg={Grammar:()=>{},LanguageMetaData:()=>({caseInsensitive:!1,fileExtensions:[".langium"],languageId:"langium"})},Ag={AstReflection:()=>new bl};function Eg(){const n=pe(vt(kt),Ag),e=pe(Et({shared:n}),Rg);return n.ServiceRegistry.register(e),e}function sn(n){var e;const t=Eg(),r=t.serializer.JsonSerializer.deserialize(n);return t.shared.workspace.LangiumDocumentFactory.fromModel(r,yt.parse(`memory://${(e=r.name)!==null&&e!==void 0?e:"grammar"}.langium`)),r}var vg=Object.defineProperty,k=(n,e)=>vg(n,"name",{value:e,configurable:!0}),cl="Statement",Lr="Architecture";function kg(n){return De.isInstance(n,Lr)}k(kg,"isArchitecture");var Ar="Axis",Vn="Branch";function Sg(n){return De.isInstance(n,Vn)}k(Sg,"isBranch");var Er="Checkout",vr="CherryPicking",Wn="Commit";function xg(n){return De.isInstance(n,Wn)}k(xg,"isCommit");var Or="Common";function Ig(n){return De.isInstance(n,Or)}k(Ig,"isCommon");var Vi="Curve",Wi="Edge",ji="Entry",jn="GitGraph";function Cg(n){return De.isInstance(n,jn)}k(Cg,"isGitGraph");var Ki="Group",br="Info";function $g(n){return De.isInstance(n,br)}k($g,"isInfo");var Hi="Junction",Kn="Merge";function Ng(n){return De.isInstance(n,Kn)}k(Ng,"isMerge");var zi="Option",Pr="Packet";function wg(n){return De.isInstance(n,Pr)}k(wg,"isPacket");var Mr="PacketBlock";function _g(n){return De.isInstance(n,Mr)}k(_g,"isPacketBlock");var Dr="Pie";function Lg(n){return De.isInstance(n,Dr)}k(Lg,"isPie");var Fr="PieSection";function Og(n){return De.isInstance(n,Fr)}k(Og,"isPieSection");var qi="Radar",Yi="Service",kr="Direction",Lt,sc=(Lt=class extends xl{getAllTypes(){return[Lr,Ar,Vn,Er,vr,Wn,Or,Vi,kr,Wi,ji,jn,Ki,br,Hi,Kn,zi,Pr,Mr,Dr,Fr,qi,Yi,cl]}computeIsSubtype(e,t){switch(e){case Vn:case Er:case vr:case Wn:case Kn:return this.isSubtype(cl,t);case kr:return this.isSubtype(jn,t);default:return!1}}getReferenceType(e){const t=`${e.container.$type}:${e.property}`;switch(t){case"Entry:axis":return Ar;default:throw new Error(`${t} is not a valid reference id.`)}}getTypeMetaData(e){switch(e){case Lr:return{name:Lr,properties:[{name:"accDescr"},{name:"accTitle"},{name:"edges",defaultValue:[]},{name:"groups",defaultValue:[]},{name:"junctions",defaultValue:[]},{name:"services",defaultValue:[]},{name:"title"}]};case Ar:return{name:Ar,properties:[{name:"label"},{name:"name"}]};case Vn:return{name:Vn,properties:[{name:"name"},{name:"order"}]};case Er:return{name:Er,properties:[{name:"branch"}]};case vr:return{name:vr,properties:[{name:"id"},{name:"parent"},{name:"tags",defaultValue:[]}]};case Wn:return{name:Wn,properties:[{name:"id"},{name:"message"},{name:"tags",defaultValue:[]},{name:"type"}]};case Or:return{name:Or,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case Vi:return{name:Vi,properties:[{name:"entries",defaultValue:[]},{name:"label"},{name:"name"}]};case Wi:return{name:Wi,properties:[{name:"lhsDir"},{name:"lhsGroup",defaultValue:!1},{name:"lhsId"},{name:"lhsInto",defaultValue:!1},{name:"rhsDir"},{name:"rhsGroup",defaultValue:!1},{name:"rhsId"},{name:"rhsInto",defaultValue:!1},{name:"title"}]};case ji:return{name:ji,properties:[{name:"axis"},{name:"value"}]};case jn:return{name:jn,properties:[{name:"accDescr"},{name:"accTitle"},{name:"statements",defaultValue:[]},{name:"title"}]};case Ki:return{name:Ki,properties:[{name:"icon"},{name:"id"},{name:"in"},{name:"title"}]};case br:return{name:br,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case Hi:return{name:Hi,properties:[{name:"id"},{name:"in"}]};case Kn:return{name:Kn,properties:[{name:"branch"},{name:"id"},{name:"tags",defaultValue:[]},{name:"type"}]};case zi:return{name:zi,properties:[{name:"name"},{name:"value",defaultValue:!1}]};case Pr:return{name:Pr,properties:[{name:"accDescr"},{name:"accTitle"},{name:"blocks",defaultValue:[]},{name:"title"}]};case Mr:return{name:Mr,properties:[{name:"end"},{name:"label"},{name:"start"}]};case Dr:return{name:Dr,properties:[{name:"accDescr"},{name:"accTitle"},{name:"sections",defaultValue:[]},{name:"showData",defaultValue:!1},{name:"title"}]};case Fr:return{name:Fr,properties:[{name:"label"},{name:"value"}]};case qi:return{name:qi,properties:[{name:"accDescr"},{name:"accTitle"},{name:"axes",defaultValue:[]},{name:"curves",defaultValue:[]},{name:"options",defaultValue:[]},{name:"title"}]};case Yi:return{name:Yi,properties:[{name:"icon"},{name:"iconText"},{name:"id"},{name:"in"},{name:"title"}]};case kr:return{name:kr,properties:[{name:"accDescr"},{name:"accTitle"},{name:"dir"},{name:"statements",defaultValue:[]},{name:"title"}]};default:return{name:e,properties:[]}}}},k(Lt,"MermaidAstReflection"),Lt),De=new sc,dl,bg=k(()=>dl??(dl=sn('{"$type":"Grammar","isDeclared":true,"name":"Info","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Info","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"info"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"Keyword","value":"showInfo"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"*"}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"InfoGrammar"),fl,Pg=k(()=>fl??(fl=sn(`{"$type":"Grammar","isDeclared":true,"name":"Packet","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Packet","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"packet-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PacketBlock","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"start","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"end","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}}],"cardinality":"?"},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}`)),"PacketGrammar"),hl,Mg=k(()=>hl??(hl=sn('{"$type":"Grammar","isDeclared":true,"name":"Pie","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Pie","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"pie"},{"$type":"Assignment","feature":"showData","operator":"?=","terminal":{"$type":"Keyword","value":"showData"},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]},"cardinality":"+"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PieSection","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"PIE_SECTION_LABEL","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]+\\"/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"PIE_SECTION_VALUE","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/(0|[1-9][0-9]*)(\\\\.[0-9]+)?/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"PieGrammar"),pl,Dg=k(()=>pl??(pl=sn('{"$type":"Grammar","isDeclared":true,"name":"Architecture","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Architecture","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"architecture-beta"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"groups","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"services","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"junctions","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"edges","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"LeftPort","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"lhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"RightPort","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"rhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Keyword","value":":"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Arrow","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Assignment","feature":"lhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"--"},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Keyword","value":"-"}]}]},{"$type":"Assignment","feature":"rhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Group","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"group"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Service","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"service"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"iconText","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}}],"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Junction","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"junction"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Edge","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"lhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"lhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"rhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"Assignment","feature":"rhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"ARROW_DIRECTION","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"L"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"R"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"T"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"B"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ID","definition":{"$type":"RegexToken","regex":"/[\\\\w]+/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TEXT_ICON","definition":{"$type":"RegexToken","regex":"/\\\\(\\"[^\\"]+\\"\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_ICON","definition":{"$type":"RegexToken","regex":"/\\\\([\\\\w-:]+\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TITLE","definition":{"$type":"RegexToken","regex":"/\\\\[[\\\\w ]+\\\\]/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_GROUP","definition":{"$type":"RegexToken","regex":"/\\\\{group\\\\}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_INTO","definition":{"$type":"RegexToken","regex":"/<|>/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"types":[],"usedGrammars":[]}')),"ArchitectureGrammar"),ml,Fg=k(()=>ml??(ml=sn(`{"$type":"Grammar","isDeclared":true,"name":"GitGraph","interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"rules":[{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"ParserRule","entry":true,"name":"GitGraph","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Keyword","value":":"}]},{"$type":"Keyword","value":"gitGraph:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Assignment","feature":"statements","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"*"}]}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Direction","definition":{"$type":"Assignment","feature":"dir","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"LR"},{"$type":"Keyword","value":"TB"},{"$type":"Keyword","value":"BT"}]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Commit","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"commit"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"msg:","cardinality":"?"},{"$type":"Assignment","feature":"message","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Branch","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"branch"},{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"order:"},{"$type":"Assignment","feature":"order","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Merge","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"merge"},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Checkout","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"checkout"},{"$type":"Keyword","value":"switch"}]},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"CherryPicking","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"cherry-pick"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"parent:"},{"$type":"Assignment","feature":"parent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+(?=\\\\s)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\\\w([-\\\\./\\\\w]*[-\\\\w])?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[]}`)),"GitGraphGrammar"),gl,Gg=k(()=>gl??(gl=sn(`{"$type":"Grammar","isDeclared":true,"name":"Radar","interfaces":[{"$type":"Interface","name":"Common","attributes":[{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]},{"$type":"Interface","name":"Entry","attributes":[{"$type":"TypeAttribute","name":"axis","isOptional":true,"type":{"$type":"ReferenceType","referenceType":{"$type":"SimpleType","typeRef":{"$ref":"#/rules@12"}}}},{"$type":"TypeAttribute","name":"value","type":{"$type":"SimpleType","primitiveType":"number"},"isOptional":false}],"superTypes":[]}],"rules":[{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"ParserRule","entry":true,"name":"Radar","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":"radar-beta:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Group","elements":[{"$type":"Keyword","value":"axis"},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"curve"},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Label","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"["},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Keyword","value":"]"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Axis","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Curve","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[],"cardinality":"?"},{"$type":"Keyword","value":"{"},{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]},{"$type":"Keyword","value":"}"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Entries","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"DetailedEntry","returnType":{"$ref":"#/interfaces@1"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"axis","operator":"=","terminal":{"$type":"CrossReference","type":{"$ref":"#/rules@12"},"terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]},"deprecatedSyntax":false}},{"$type":"Keyword","value":":","cardinality":"?"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"NumberEntry","returnType":{"$ref":"#/interfaces@1"},"definition":{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Option","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"showLegend"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"ticks"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"max"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"min"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"graticule"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/(0|[1-9][0-9]*)(\\\\.[0-9]+)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"GRATICULE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"circle"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"polygon"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[a-zA-Z_][a-zA-Z0-9\\\\-_]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[]}`)),"RadarGrammar"),Ug={languageId:"info",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Bg={languageId:"packet",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Vg={languageId:"pie",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Wg={languageId:"architecture",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},jg={languageId:"gitGraph",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Kg={languageId:"radar",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},an={AstReflection:k(()=>new sc,"AstReflection")},Hg={Grammar:k(()=>bg(),"Grammar"),LanguageMetaData:k(()=>Ug,"LanguageMetaData"),parser:{}},zg={Grammar:k(()=>Pg(),"Grammar"),LanguageMetaData:k(()=>Bg,"LanguageMetaData"),parser:{}},qg={Grammar:k(()=>Mg(),"Grammar"),LanguageMetaData:k(()=>Vg,"LanguageMetaData"),parser:{}},Yg={Grammar:k(()=>Dg(),"Grammar"),LanguageMetaData:k(()=>Wg,"LanguageMetaData"),parser:{}},Xg={Grammar:k(()=>Fg(),"Grammar"),LanguageMetaData:k(()=>jg,"LanguageMetaData"),parser:{}},Jg={Grammar:k(()=>Gg(),"Grammar"),LanguageMetaData:k(()=>Kg,"LanguageMetaData"),parser:{}},Qg=/accDescr(?:[\t ]*:([^\n\r]*)|\s*{([^}]*)})/,Zg=/accTitle[\t ]*:([^\n\r]*)/,ey=/title([\t ][^\n\r]*|)/,ty={ACC_DESCR:Qg,ACC_TITLE:Zg,TITLE:ey},Ot,sa=(Ot=class extends Ku{runConverter(e,t,r){let i=this.runCommonConverter(e,t,r);return i===void 0&&(i=this.runCustomConverter(e,t,r)),i===void 0?super.runConverter(e,t,r):i}runCommonConverter(e,t,r){const i=ty[e.name];if(i===void 0)return;const s=i.exec(t);if(s!==null){if(s[1]!==void 0)return s[1].trim().replace(/[\t ]{2,}/gm," ");if(s[2]!==void 0)return s[2].replace(/^\s*/gm,"").replace(/\s+$/gm,"").replace(/[\t ]{2,}/gm," ").replace(/[\n\r]{2,}/gm,`
`)}}},k(Ot,"AbstractMermaidValueConverter"),Ot),bt,vi=(bt=class extends sa{runCustomConverter(e,t,r){}},k(bt,"CommonValueConverter"),bt),Pt,St=(Pt=class extends ju{constructor(e){super(),this.keywords=new Set(e)}buildKeywordTokens(e,t,r){const i=super.buildKeywordTokens(e,t,r);return i.forEach(s=>{this.keywords.has(s.name)&&s.PATTERN!==void 0&&(s.PATTERN=new RegExp(s.PATTERN.toString()+"(?:(?=%%)|(?!\\S))"))}),i}},k(Pt,"AbstractMermaidTokenBuilder"),Pt),Mt;Mt=class extends St{},k(Mt,"CommonTokenBuilder");var Dt,ny=(Dt=class extends St{constructor(){super(["gitGraph"])}},k(Dt,"GitGraphTokenBuilder"),Dt),ac={parser:{TokenBuilder:k(()=>new ny,"TokenBuilder"),ValueConverter:k(()=>new vi,"ValueConverter")}};function oc(n=kt){const e=pe(vt(n),an),t=pe(Et({shared:e}),Xg,ac);return e.ServiceRegistry.register(t),{shared:e,GitGraph:t}}k(oc,"createGitGraphServices");var Ft,ry=(Ft=class extends St{constructor(){super(["info","showInfo"])}},k(Ft,"InfoTokenBuilder"),Ft),lc={parser:{TokenBuilder:k(()=>new ry,"TokenBuilder"),ValueConverter:k(()=>new vi,"ValueConverter")}};function uc(n=kt){const e=pe(vt(n),an),t=pe(Et({shared:e}),Hg,lc);return e.ServiceRegistry.register(t),{shared:e,Info:t}}k(uc,"createInfoServices");var Gt,iy=(Gt=class extends St{constructor(){super(["packet-beta"])}},k(Gt,"PacketTokenBuilder"),Gt),cc={parser:{TokenBuilder:k(()=>new iy,"TokenBuilder"),ValueConverter:k(()=>new vi,"ValueConverter")}};function dc(n=kt){const e=pe(vt(n),an),t=pe(Et({shared:e}),zg,cc);return e.ServiceRegistry.register(t),{shared:e,Packet:t}}k(dc,"createPacketServices");var Ut,sy=(Ut=class extends St{constructor(){super(["pie","showData"])}},k(Ut,"PieTokenBuilder"),Ut),Bt,ay=(Bt=class extends sa{runCustomConverter(e,t,r){if(e.name==="PIE_SECTION_LABEL")return t.replace(/"/g,"").trim()}},k(Bt,"PieValueConverter"),Bt),fc={parser:{TokenBuilder:k(()=>new sy,"TokenBuilder"),ValueConverter:k(()=>new ay,"ValueConverter")}};function hc(n=kt){const e=pe(vt(n),an),t=pe(Et({shared:e}),qg,fc);return e.ServiceRegistry.register(t),{shared:e,Pie:t}}k(hc,"createPieServices");var Vt,oy=(Vt=class extends St{constructor(){super(["architecture"])}},k(Vt,"ArchitectureTokenBuilder"),Vt),Wt,ly=(Wt=class extends sa{runCustomConverter(e,t,r){if(e.name==="ARCH_ICON")return t.replace(/[()]/g,"").trim();if(e.name==="ARCH_TEXT_ICON")return t.replace(/["()]/g,"");if(e.name==="ARCH_TITLE")return t.replace(/[[\]]/g,"").trim()}},k(Wt,"ArchitectureValueConverter"),Wt),pc={parser:{TokenBuilder:k(()=>new oy,"TokenBuilder"),ValueConverter:k(()=>new ly,"ValueConverter")}};function mc(n=kt){const e=pe(vt(n),an),t=pe(Et({shared:e}),Yg,pc);return e.ServiceRegistry.register(t),{shared:e,Architecture:t}}k(mc,"createArchitectureServices");var jt,uy=(jt=class extends St{constructor(){super(["radar-beta"])}},k(jt,"RadarTokenBuilder"),jt),gc={parser:{TokenBuilder:k(()=>new uy,"TokenBuilder"),ValueConverter:k(()=>new vi,"ValueConverter")}};function yc(n=kt){const e=pe(vt(n),an),t=pe(Et({shared:e}),Jg,gc);return e.ServiceRegistry.register(t),{shared:e,Radar:t}}k(yc,"createRadarServices");var Ze={},cy={info:k(async()=>{const{createInfoServices:n}=await It(async()=>{const{createInfoServices:t}=await Promise.resolve().then(()=>hy);return{createInfoServices:t}},void 0),e=n().Info.parser.LangiumParser;Ze.info=e},"info"),packet:k(async()=>{const{createPacketServices:n}=await It(async()=>{const{createPacketServices:t}=await Promise.resolve().then(()=>py);return{createPacketServices:t}},void 0),e=n().Packet.parser.LangiumParser;Ze.packet=e},"packet"),pie:k(async()=>{const{createPieServices:n}=await It(async()=>{const{createPieServices:t}=await Promise.resolve().then(()=>my);return{createPieServices:t}},void 0),e=n().Pie.parser.LangiumParser;Ze.pie=e},"pie"),architecture:k(async()=>{const{createArchitectureServices:n}=await It(async()=>{const{createArchitectureServices:t}=await Promise.resolve().then(()=>gy);return{createArchitectureServices:t}},void 0),e=n().Architecture.parser.LangiumParser;Ze.architecture=e},"architecture"),gitGraph:k(async()=>{const{createGitGraphServices:n}=await It(async()=>{const{createGitGraphServices:t}=await Promise.resolve().then(()=>yy);return{createGitGraphServices:t}},void 0),e=n().GitGraph.parser.LangiumParser;Ze.gitGraph=e},"gitGraph"),radar:k(async()=>{const{createRadarServices:n}=await It(async()=>{const{createRadarServices:t}=await Promise.resolve().then(()=>Ty);return{createRadarServices:t}},void 0),e=n().Radar.parser.LangiumParser;Ze.radar=e},"radar")};async function dy(n,e){const t=cy[n];if(!t)throw new Error(`Unknown diagram type: ${n}`);Ze[n]||await t();const i=Ze[n].parse(e);if(i.lexerErrors.length>0||i.parserErrors.length>0)throw new fy(i);return i.value}k(dy,"parse");var Kt,fy=(Kt=class extends Error{constructor(e){const t=e.lexerErrors.map(i=>i.message).join(`
`),r=e.parserErrors.map(i=>i.message).join(`
`);super(`Parsing failed: ${t} ${r}`),this.result=e}},k(Kt,"MermaidParseError"),Kt);const hy=Object.freeze(Object.defineProperty({__proto__:null,InfoModule:lc,createInfoServices:uc},Symbol.toStringTag,{value:"Module"})),py=Object.freeze(Object.defineProperty({__proto__:null,PacketModule:cc,createPacketServices:dc},Symbol.toStringTag,{value:"Module"})),my=Object.freeze(Object.defineProperty({__proto__:null,PieModule:fc,createPieServices:hc},Symbol.toStringTag,{value:"Module"})),gy=Object.freeze(Object.defineProperty({__proto__:null,ArchitectureModule:pc,createArchitectureServices:mc},Symbol.toStringTag,{value:"Module"})),yy=Object.freeze(Object.defineProperty({__proto__:null,GitGraphModule:ac,createGitGraphServices:oc},Symbol.toStringTag,{value:"Module"})),Ty=Object.freeze(Object.defineProperty({__proto__:null,RadarModule:gc,createRadarServices:yc},Symbol.toStringTag,{value:"Module"}));export{dy as p};
