# .github/workflows/stale.yml
name: <PERSON> stale issues and pull requests

on:
  schedule:
    - cron: '30 22 * * *' # run at 22:30+08 every day

permissions:
  issues: write
  pull-requests: write

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          days-before-stale: 90 # 90 days
          days-before-close: 7 # 7 days after marked as stale
          stale-issue-message: 'This issue has been automatically marked as stale because it has not had recent activity. It will be closed if no further activity occurs. Thank you for your contributions.'
          close-issue-message: 'This issue has been automatically closed because it has not had recent activity. Please open a new issue if you still have this problem.'
          stale-pr-message: 'This pull request has been automatically marked as stale because it has not had recent activity. It will be closed if no further activity occurs.'
          close-pr-message: 'This pull request has been automatically closed because it has not had recent activity.'
          # If there are specific labels, exempt them from being marked as stale, for example:
          exempt-issue-labels: 'enhancement,tracked'
          # exempt-pr-labels: 'bug,enhancement,help wanted'
          repo-token: ${{ secrets.GITHUB_TOKEN }} # token provided by GitHub
