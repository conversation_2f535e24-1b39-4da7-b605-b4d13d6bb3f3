import{j as a,E as Wa,I as kt,F as Ga,G as Va,H as Dt,J as Ya,V as Nt,L as Ja,K as Qa,M as zt,N as Ct,Q as Xa,U as Pt,W as St,X as Et,_ as ge,d as _t}from"./ui-vendor-CeCm8EER.js";import{r as o,g as Za,R as Ft}from"./react-vendor-DEwriMA6.js";import{c as _,C as et,a as Tt,b as At,d as sa,F as Ot,e as la,f as at,u as ce,s as Rt,g as O,U as ca,S as Mt,h as tt,B as F,X as it,i as It,j as ie,D as ze,k as $e,l as Ce,m as Pe,n as Se,o as Ee,p as qt,q as Lt,E as Bt,T as nt,I as qe,r as ot,t as ba,v as Ut,w as $t,x as Ht,y as ya,z as ja,A as Kt,G as Wt,H as Gt,J as Vt,K as Yt,L as Jt,M as ke,N as De,O as wa,P as Qt,Q as ka,R as Da,V as Xt,W as Zt,Y as ei,Z as Xe,_ as Ze}from"./feature-graph-Chuw_ipx.js";const Na=Pt,yn=Et,za=St,ra=o.forwardRef(({className:e,children:t,...n},i)=>a.jsxs(Wa,{ref:i,className:_("border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex h-10 w-full items-center justify-between rounded-md border px-3 py-2 text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...n,children:[t,a.jsx(kt,{asChild:!0,children:a.jsx(et,{className:"h-4 w-4 opacity-50"})})]}));ra.displayName=Wa.displayName;const st=o.forwardRef(({className:e,...t},n)=>a.jsx(Ga,{ref:n,className:_("flex cursor-default items-center justify-center py-1",e),...t,children:a.jsx(Tt,{className:"h-4 w-4"})}));st.displayName=Ga.displayName;const lt=o.forwardRef(({className:e,...t},n)=>a.jsx(Va,{ref:n,className:_("flex cursor-default items-center justify-center py-1",e),...t,children:a.jsx(et,{className:"h-4 w-4"})}));lt.displayName=Va.displayName;const pa=o.forwardRef(({className:e,children:t,position:n="popper",...i},s)=>a.jsx(Dt,{children:a.jsxs(Ya,{ref:s,className:_("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...i,children:[a.jsx(st,{}),a.jsx(Nt,{className:_("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),a.jsx(lt,{})]})}));pa.displayName=Ya.displayName;const ai=o.forwardRef(({className:e,...t},n)=>a.jsx(Ja,{ref:n,className:_("py-1.5 pr-2 pl-8 text-sm font-semibold",e),...t}));ai.displayName=Ja.displayName;const da=o.forwardRef(({className:e,children:t,...n},i)=>a.jsxs(Qa,{ref:i,className:_("focus:bg-accent focus:text-accent-foreground relative flex w-full cursor-default items-center rounded-sm py-1.5 pr-2 pl-8 text-sm outline-none select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(zt,{children:a.jsx(At,{className:"h-4 w-4"})})}),a.jsx(Ct,{children:t})]}));da.displayName=Qa.displayName;const ti=o.forwardRef(({className:e,...t},n)=>a.jsx(Xa,{ref:n,className:_("bg-muted -mx-1 my-1 h-px",e),...t}));ti.displayName=Xa.displayName;const ct=o.forwardRef(({className:e,...t},n)=>a.jsx("div",{className:"relative w-full overflow-auto",children:a.jsx("table",{ref:n,className:_("w-full caption-bottom text-sm",e),...t})}));ct.displayName="Table";const rt=o.forwardRef(({className:e,...t},n)=>a.jsx("thead",{ref:n,className:_("[&_tr]:border-b",e),...t}));rt.displayName="TableHeader";const pt=o.forwardRef(({className:e,...t},n)=>a.jsx("tbody",{ref:n,className:_("[&_tr:last-child]:border-0",e),...t}));pt.displayName="TableBody";const ii=o.forwardRef(({className:e,...t},n)=>a.jsx("tfoot",{ref:n,className:_("bg-muted/50 border-t font-medium [&>tr]:last:border-b-0",e),...t}));ii.displayName="TableFooter";const ma=o.forwardRef(({className:e,...t},n)=>a.jsx("tr",{ref:n,className:_("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t}));ma.displayName="TableRow";const se=o.forwardRef(({className:e,...t},n)=>a.jsx("th",{ref:n,className:_("text-muted-foreground h-10 px-2 text-left align-middle font-medium [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t}));se.displayName="TableHead";const le=o.forwardRef(({className:e,...t},n)=>a.jsx("td",{ref:n,className:_("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t}));le.displayName="TableCell";const ni=o.forwardRef(({className:e,...t},n)=>a.jsx("caption",{ref:n,className:_("text-muted-foreground mt-4 text-sm",e),...t}));ni.displayName="TableCaption";function oi({title:e,description:t,icon:n=Ot,action:i,className:s,...l}){return a.jsxs(sa,{className:_("flex w-full flex-col items-center justify-center space-y-6 bg-transparent p-16",s),...l,children:[a.jsx("div",{className:"mr-4 shrink-0 rounded-full border border-dashed p-4",children:a.jsx(n,{className:"text-muted-foreground size-8","aria-hidden":"true"})}),a.jsxs("div",{className:"flex flex-col items-center gap-1.5 text-center",children:[a.jsx(la,{children:e}),t?a.jsx(at,{children:t}):null]}),i||null]})}var ea={exports:{}},aa,Ca;function si(){if(Ca)return aa;Ca=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return aa=e,aa}var ta,Pa;function li(){if(Pa)return ta;Pa=1;var e=si();function t(){}function n(){}return n.resetWarningCache=t,ta=function(){function i(p,r,j,v,u,S){if(S!==e){var h=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw h.name="Invariant Violation",h}}i.isRequired=i;function s(){return i}var l={array:i,bigint:i,bool:i,func:i,number:i,object:i,string:i,symbol:i,any:i,arrayOf:s,element:i,elementType:i,instanceOf:s,node:i,objectOf:s,oneOf:s,oneOfType:s,shape:s,exact:s,checkPropTypes:n,resetWarningCache:t};return l.PropTypes=l,l},ta}var Sa;function ci(){return Sa||(Sa=1,ea.exports=li()()),ea.exports}var ri=ci();const T=Za(ri),pi=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function ye(e,t,n){const i=di(e),{webkitRelativePath:s}=e,l=typeof t=="string"?t:typeof s=="string"&&s.length>0?s:`./${e.name}`;return typeof i.path!="string"&&Ea(i,"path",l),Ea(i,"relativePath",l),i}function di(e){const{name:t}=e;if(t&&t.lastIndexOf(".")!==-1&&!e.type){const i=t.split(".").pop().toLowerCase(),s=pi.get(i);s&&Object.defineProperty(e,"type",{value:s,writable:!1,configurable:!1,enumerable:!0})}return e}function Ea(e,t,n){Object.defineProperty(e,t,{value:n,writable:!1,configurable:!1,enumerable:!0})}const mi=[".DS_Store","Thumbs.db"];function ui(e){return ge(this,void 0,void 0,function*(){return Le(e)&&fi(e.dataTransfer)?hi(e.dataTransfer,e.type):xi(e)?vi(e):Array.isArray(e)&&e.every(t=>"getFile"in t&&typeof t.getFile=="function")?gi(e):[]})}function fi(e){return Le(e)}function xi(e){return Le(e)&&Le(e.target)}function Le(e){return typeof e=="object"&&e!==null}function vi(e){return ua(e.target.files).map(t=>ye(t))}function gi(e){return ge(this,void 0,void 0,function*(){return(yield Promise.all(e.map(n=>n.getFile()))).map(n=>ye(n))})}function hi(e,t){return ge(this,void 0,void 0,function*(){if(e.items){const n=ua(e.items).filter(s=>s.kind==="file");if(t!=="drop")return n;const i=yield Promise.all(n.map(bi));return _a(dt(i))}return _a(ua(e.files).map(n=>ye(n)))})}function _a(e){return e.filter(t=>mi.indexOf(t.name)===-1)}function ua(e){if(e===null)return[];const t=[];for(let n=0;n<e.length;n++){const i=e[n];t.push(i)}return t}function bi(e){if(typeof e.webkitGetAsEntry!="function")return Fa(e);const t=e.webkitGetAsEntry();return t&&t.isDirectory?mt(t):Fa(e,t)}function dt(e){return e.reduce((t,n)=>[...t,...Array.isArray(n)?dt(n):[n]],[])}function Fa(e,t){return ge(this,void 0,void 0,function*(){var n;if(globalThis.isSecureContext&&typeof e.getAsFileSystemHandle=="function"){const l=yield e.getAsFileSystemHandle();if(l===null)throw new Error(`${e} is not a File`);if(l!==void 0){const p=yield l.getFile();return p.handle=l,ye(p)}}const i=e.getAsFile();if(!i)throw new Error(`${e} is not a File`);return ye(i,(n=t==null?void 0:t.fullPath)!==null&&n!==void 0?n:void 0)})}function yi(e){return ge(this,void 0,void 0,function*(){return e.isDirectory?mt(e):ji(e)})}function mt(e){const t=e.createReader();return new Promise((n,i)=>{const s=[];function l(){t.readEntries(p=>ge(this,void 0,void 0,function*(){if(p.length){const r=Promise.all(p.map(yi));s.push(r),l()}else try{const r=yield Promise.all(s);n(r)}catch(r){i(r)}}),p=>{i(p)})}l()})}function ji(e){return ge(this,void 0,void 0,function*(){return new Promise((t,n)=>{e.file(i=>{const s=ye(i,e.fullPath);t(s)},i=>{n(i)})})})}var Me={},Ta;function wi(){return Ta||(Ta=1,Me.__esModule=!0,Me.default=function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(",");if(n.length===0)return!0;var i=e.name||"",s=(e.type||"").toLowerCase(),l=s.replace(/\/.*$/,"");return n.some(function(p){var r=p.trim().toLowerCase();return r.charAt(0)==="."?i.toLowerCase().endsWith(r):r.endsWith("/*")?l===r.replace(/\/.*$/,""):s===r})}return!0}),Me}var ki=wi();const ia=Za(ki);function Aa(e){return zi(e)||Ni(e)||ft(e)||Di()}function Di(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ni(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function zi(e){if(Array.isArray(e))return fa(e)}function Oa(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),n.push.apply(n,i)}return n}function Ra(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Oa(Object(n),!0).forEach(function(i){ut(e,i,n[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Oa(Object(n)).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(n,i))})}return e}function ut(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ne(e,t){return Si(e)||Pi(e,t)||ft(e,t)||Ci()}function Ci(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ft(e,t){if(e){if(typeof e=="string")return fa(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return fa(e,t)}}function fa(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function Pi(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var i=[],s=!0,l=!1,p,r;try{for(n=n.call(e);!(s=(p=n.next()).done)&&(i.push(p.value),!(t&&i.length===t));s=!0);}catch(j){l=!0,r=j}finally{try{!s&&n.return!=null&&n.return()}finally{if(l)throw r}}return i}}function Si(e){if(Array.isArray(e))return e}var Ei=typeof ia=="function"?ia:ia.default,_i="file-invalid-type",Fi="file-too-large",Ti="file-too-small",Ai="too-many-files",Oi=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n=t.split(","),i=n.length>1?"one of ".concat(n.join(", ")):n[0];return{code:_i,message:"File type must be ".concat(i)}},Ma=function(t){return{code:Fi,message:"File is larger than ".concat(t," ").concat(t===1?"byte":"bytes")}},Ia=function(t){return{code:Ti,message:"File is smaller than ".concat(t," ").concat(t===1?"byte":"bytes")}},Ri={code:Ai,message:"Too many files"};function xt(e,t){var n=e.type==="application/x-moz-file"||Ei(e,t);return[n,n?null:Oi(t)]}function vt(e,t,n){if(ve(e.size))if(ve(t)&&ve(n)){if(e.size>n)return[!1,Ma(n)];if(e.size<t)return[!1,Ia(t)]}else{if(ve(t)&&e.size<t)return[!1,Ia(t)];if(ve(n)&&e.size>n)return[!1,Ma(n)]}return[!0,null]}function ve(e){return e!=null}function Mi(e){var t=e.files,n=e.accept,i=e.minSize,s=e.maxSize,l=e.multiple,p=e.maxFiles,r=e.validator;return!l&&t.length>1||l&&p>=1&&t.length>p?!1:t.every(function(j){var v=xt(j,n),u=Ne(v,1),S=u[0],h=vt(j,i,s),E=Ne(h,1),N=E[0],y=r?r(j):null;return S&&N&&!y})}function Be(e){return typeof e.isPropagationStopped=="function"?e.isPropagationStopped():typeof e.cancelBubble<"u"?e.cancelBubble:!1}function Ie(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(t){return t==="Files"||t==="application/x-moz-file"}):!!e.target&&!!e.target.files}function qa(e){e.preventDefault()}function Ii(e){return e.indexOf("MSIE")!==-1||e.indexOf("Trident/")!==-1}function qi(e){return e.indexOf("Edge/")!==-1}function Li(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return Ii(e)||qi(e)}function te(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(i){for(var s=arguments.length,l=new Array(s>1?s-1:0),p=1;p<s;p++)l[p-1]=arguments[p];return t.some(function(r){return!Be(i)&&r&&r.apply(void 0,[i].concat(l)),Be(i)})}}function Bi(){return"showOpenFilePicker"in window}function Ui(e){if(ve(e)){var t=Object.entries(e).filter(function(n){var i=Ne(n,2),s=i[0],l=i[1],p=!0;return gt(s)||(console.warn('Skipped "'.concat(s,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),p=!1),(!Array.isArray(l)||!l.every(ht))&&(console.warn('Skipped "'.concat(s,'" because an invalid file extension was provided.')),p=!1),p}).reduce(function(n,i){var s=Ne(i,2),l=s[0],p=s[1];return Ra(Ra({},n),{},ut({},l,p))},{});return[{description:"Files",accept:t}]}return e}function $i(e){if(ve(e))return Object.entries(e).reduce(function(t,n){var i=Ne(n,2),s=i[0],l=i[1];return[].concat(Aa(t),[s],Aa(l))},[]).filter(function(t){return gt(t)||ht(t)}).join(",")}function Hi(e){return e instanceof DOMException&&(e.name==="AbortError"||e.code===e.ABORT_ERR)}function Ki(e){return e instanceof DOMException&&(e.name==="SecurityError"||e.code===e.SECURITY_ERR)}function gt(e){return e==="audio/*"||e==="video/*"||e==="image/*"||e==="text/*"||e==="application/*"||/\w+\/[-+.\w]+/g.test(e)}function ht(e){return/^.*\.[\w]+$/.test(e)}var Wi=["children"],Gi=["open"],Vi=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],Yi=["refKey","onChange","onClick"];function Ji(e){return Zi(e)||Xi(e)||bt(e)||Qi()}function Qi(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Xi(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Zi(e){if(Array.isArray(e))return xa(e)}function na(e,t){return tn(e)||an(e,t)||bt(e,t)||en()}function en(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function bt(e,t){if(e){if(typeof e=="string")return xa(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return xa(e,t)}}function xa(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function an(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var i=[],s=!0,l=!1,p,r;try{for(n=n.call(e);!(s=(p=n.next()).done)&&(i.push(p.value),!(t&&i.length===t));s=!0);}catch(j){l=!0,r=j}finally{try{!s&&n.return!=null&&n.return()}finally{if(l)throw r}}return i}}function tn(e){if(Array.isArray(e))return e}function La(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),n.push.apply(n,i)}return n}function M(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?La(Object(n),!0).forEach(function(i){va(e,i,n[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):La(Object(n)).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(n,i))})}return e}function va(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ue(e,t){if(e==null)return{};var n=nn(e,t),i,s;if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(s=0;s<l.length;s++)i=l[s],!(t.indexOf(i)>=0)&&Object.prototype.propertyIsEnumerable.call(e,i)&&(n[i]=e[i])}return n}function nn(e,t){if(e==null)return{};var n={},i=Object.keys(e),s,l;for(l=0;l<i.length;l++)s=i[l],!(t.indexOf(s)>=0)&&(n[s]=e[s]);return n}var He=o.forwardRef(function(e,t){var n=e.children,i=Ue(e,Wi),s=on(i),l=s.open,p=Ue(s,Gi);return o.useImperativeHandle(t,function(){return{open:l}},[l]),Ft.createElement(o.Fragment,null,n(M(M({},p),{},{open:l})))});He.displayName="Dropzone";var yt={disabled:!1,getFilesFromEvent:ui,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};He.defaultProps=yt;He.propTypes={children:T.func,accept:T.objectOf(T.arrayOf(T.string)),multiple:T.bool,preventDropOnDocument:T.bool,noClick:T.bool,noKeyboard:T.bool,noDrag:T.bool,noDragEventsBubbling:T.bool,minSize:T.number,maxSize:T.number,maxFiles:T.number,disabled:T.bool,getFilesFromEvent:T.func,onFileDialogCancel:T.func,onFileDialogOpen:T.func,useFsAccessApi:T.bool,autoFocus:T.bool,onDragEnter:T.func,onDragLeave:T.func,onDragOver:T.func,onDrop:T.func,onDropAccepted:T.func,onDropRejected:T.func,onError:T.func,validator:T.func};var ga={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function on(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=M(M({},yt),e),n=t.accept,i=t.disabled,s=t.getFilesFromEvent,l=t.maxSize,p=t.minSize,r=t.multiple,j=t.maxFiles,v=t.onDragEnter,u=t.onDragLeave,S=t.onDragOver,h=t.onDrop,E=t.onDropAccepted,N=t.onDropRejected,y=t.onFileDialogCancel,w=t.onFileDialogOpen,g=t.useFsAccessApi,W=t.autoFocus,U=t.preventDropOnDocument,P=t.noClick,m=t.noKeyboard,z=t.noDrag,b=t.noDragEventsBubbling,$=t.onError,k=t.validator,Y=o.useMemo(function(){return $i(n)},[n]),me=o.useMemo(function(){return Ui(n)},[n]),H=o.useMemo(function(){return typeof w=="function"?w:Ba},[w]),V=o.useMemo(function(){return typeof y=="function"?y:Ba},[y]),I=o.useRef(null),G=o.useRef(null),je=o.useReducer(sn,ga),ue=na(je,2),ne=ue[0],K=ue[1],_e=ne.isFocused,oe=ne.isFileDialogActive,fe=o.useRef(typeof window<"u"&&window.isSecureContext&&g&&Bi()),Fe=function(){!fe.current&&oe&&setTimeout(function(){if(G.current){var x=G.current.files;x.length||(K({type:"closeDialog"}),V())}},300)};o.useEffect(function(){return window.addEventListener("focus",Fe,!1),function(){window.removeEventListener("focus",Fe,!1)}},[G,oe,V,fe]);var X=o.useRef([]),re=function(x){I.current&&I.current.contains(x.target)||(x.preventDefault(),X.current=[])};o.useEffect(function(){return U&&(document.addEventListener("dragover",qa,!1),document.addEventListener("drop",re,!1)),function(){U&&(document.removeEventListener("dragover",qa),document.removeEventListener("drop",re))}},[I,U]),o.useEffect(function(){return!i&&W&&I.current&&I.current.focus(),function(){}},[I,W,i]);var Z=o.useCallback(function(d){$?$(d):console.error(d)},[$]),xe=o.useCallback(function(d){d.preventDefault(),d.persist(),D(d),X.current=[].concat(Ji(X.current),[d.target]),Ie(d)&&Promise.resolve(s(d)).then(function(x){if(!(Be(d)&&!b)){var R=x.length,B=R>0&&Mi({files:x,accept:Y,minSize:p,maxSize:l,multiple:r,maxFiles:j,validator:k}),Q=R>0&&!B;K({isDragAccept:B,isDragReject:Q,isDragActive:!0,type:"setDraggedFiles"}),v&&v(d)}}).catch(function(x){return Z(x)})},[s,v,Z,b,Y,p,l,r,j,k]),ee=o.useCallback(function(d){d.preventDefault(),d.persist(),D(d);var x=Ie(d);if(x&&d.dataTransfer)try{d.dataTransfer.dropEffect="copy"}catch{}return x&&S&&S(d),!1},[S,b]),J=o.useCallback(function(d){d.preventDefault(),d.persist(),D(d);var x=X.current.filter(function(B){return I.current&&I.current.contains(B)}),R=x.indexOf(d.target);R!==-1&&x.splice(R,1),X.current=x,!(x.length>0)&&(K({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),Ie(d)&&u&&u(d))},[I,u,b]),he=o.useCallback(function(d,x){var R=[],B=[];d.forEach(function(Q){var we=xt(Q,Y),be=na(we,2),Ke=be[0],We=be[1],Ge=vt(Q,p,l),Re=na(Ge,2),Ve=Re[0],Ye=Re[1],Je=k?k(Q):null;if(Ke&&Ve&&!Je)R.push(Q);else{var Qe=[We,Ye];Je&&(Qe=Qe.concat(Je)),B.push({file:Q,errors:Qe.filter(function(wt){return wt})})}}),(!r&&R.length>1||r&&j>=1&&R.length>j)&&(R.forEach(function(Q){B.push({file:Q,errors:[Ri]})}),R.splice(0)),K({acceptedFiles:R,fileRejections:B,isDragReject:B.length>0,type:"setFiles"}),h&&h(R,B,x),B.length>0&&N&&N(B,x),R.length>0&&E&&E(R,x)},[K,r,Y,p,l,j,h,E,N,k]),pe=o.useCallback(function(d){d.preventDefault(),d.persist(),D(d),X.current=[],Ie(d)&&Promise.resolve(s(d)).then(function(x){Be(d)&&!b||he(x,d)}).catch(function(x){return Z(x)}),K({type:"reset"})},[s,he,Z,b]),ae=o.useCallback(function(){if(fe.current){K({type:"openDialog"}),H();var d={multiple:r,types:me};window.showOpenFilePicker(d).then(function(x){return s(x)}).then(function(x){he(x,null),K({type:"closeDialog"})}).catch(function(x){Hi(x)?(V(x),K({type:"closeDialog"})):Ki(x)?(fe.current=!1,G.current?(G.current.value=null,G.current.click()):Z(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):Z(x)});return}G.current&&(K({type:"openDialog"}),H(),G.current.value=null,G.current.click())},[K,H,V,g,he,Z,me,r]),Te=o.useCallback(function(d){!I.current||!I.current.isEqualNode(d.target)||(d.key===" "||d.key==="Enter"||d.keyCode===32||d.keyCode===13)&&(d.preventDefault(),ae())},[I,ae]),de=o.useCallback(function(){K({type:"focus"})},[]),Ae=o.useCallback(function(){K({type:"blur"})},[]),Oe=o.useCallback(function(){P||(Li()?setTimeout(ae,0):ae())},[P,ae]),c=function(x){return i?null:x},f=function(x){return m?null:c(x)},C=function(x){return z?null:c(x)},D=function(x){b&&x.stopPropagation()},A=o.useMemo(function(){return function(){var d=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},x=d.refKey,R=x===void 0?"ref":x,B=d.role,Q=d.onKeyDown,we=d.onFocus,be=d.onBlur,Ke=d.onClick,We=d.onDragEnter,Ge=d.onDragOver,Re=d.onDragLeave,Ve=d.onDrop,Ye=Ue(d,Vi);return M(M(va({onKeyDown:f(te(Q,Te)),onFocus:f(te(we,de)),onBlur:f(te(be,Ae)),onClick:c(te(Ke,Oe)),onDragEnter:C(te(We,xe)),onDragOver:C(te(Ge,ee)),onDragLeave:C(te(Re,J)),onDrop:C(te(Ve,pe)),role:typeof B=="string"&&B!==""?B:"presentation"},R,I),!i&&!m?{tabIndex:0}:{}),Ye)}},[I,Te,de,Ae,Oe,xe,ee,J,pe,m,z,i]),q=o.useCallback(function(d){d.stopPropagation()},[]),L=o.useMemo(function(){return function(){var d=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},x=d.refKey,R=x===void 0?"ref":x,B=d.onChange,Q=d.onClick,we=Ue(d,Yi),be=va({accept:Y,multiple:r,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:c(te(B,pe)),onClick:c(te(Q,q)),tabIndex:-1},R,G);return M(M({},be),we)}},[G,n,r,pe,i]);return M(M({},ne),{},{isFocused:_e&&!i,getRootProps:A,getInputProps:L,rootRef:I,inputRef:G,open:c(ae)})}function sn(e,t){switch(t.type){case"focus":return M(M({},e),{},{isFocused:!0});case"blur":return M(M({},e),{},{isFocused:!1});case"openDialog":return M(M({},ga),{},{isFileDialogActive:!0});case"closeDialog":return M(M({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return M(M({},e),{},{isDragActive:t.isDragActive,isDragAccept:t.isDragAccept,isDragReject:t.isDragReject});case"setFiles":return M(M({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections,isDragReject:t.isDragReject});case"reset":return M({},ga);default:return e}}function Ba(){}function ha(e,t={}){const{decimals:n=0,sizeType:i="normal"}=t,s=["Bytes","KB","MB","GB","TB"],l=["Bytes","KiB","MiB","GiB","TiB"];if(e===0)return"0 Byte";const p=Math.floor(Math.log(e)/Math.log(1024));return`${(e/Math.pow(1024,p)).toFixed(n)} ${i==="accurate"?l[p]??"Bytes":s[p]??"Bytes"}`}function ln(e){const{t}=ce(),{value:n,onValueChange:i,onUpload:s,onReject:l,progresses:p,fileErrors:r,accept:j=Rt,maxSize:v=1024*1024*200,maxFileCount:u=1,multiple:S=!1,disabled:h=!1,description:E,className:N,...y}=e,[w,g]=_t({prop:n,onChange:i}),W=o.useCallback((m,z)=>{const b=((w==null?void 0:w.length)??0)+m.length+z.length;if(!S&&u===1&&m.length+z.length>1){O.error(t("documentPanel.uploadDocuments.fileUploader.singleFileLimit"));return}if(b>u){O.error(t("documentPanel.uploadDocuments.fileUploader.maxFilesLimit",{count:u}));return}z.length>0&&(l?l(z):z.forEach(({file:H})=>{O.error(t("documentPanel.uploadDocuments.fileUploader.fileRejected",{name:H.name}))}));const $=m.map(H=>Object.assign(H,{preview:URL.createObjectURL(H)})),k=z.map(({file:H})=>Object.assign(H,{preview:URL.createObjectURL(H),rejected:!0})),Y=[...$,...k],me=w?[...w,...Y]:Y;if(g(me),s&&m.length>0){const H=m.filter(V=>{var ue;if(!V.name)return!1;const I=`.${((ue=V.name.split(".").pop())==null?void 0:ue.toLowerCase())||""}`,G=Object.entries(j||{}).some(([ne,K])=>V.type===ne||Array.isArray(K)&&K.includes(I)),je=V.size<=v;return G&&je});H.length>0&&s(H)}},[w,u,S,s,l,g,t,j,v]);function U(m){if(!w)return;const z=w.filter((b,$)=>$!==m);g(z),i==null||i(z)}o.useEffect(()=>()=>{w&&w.forEach(m=>{jt(m)&&URL.revokeObjectURL(m.preview)})},[]);const P=h||((w==null?void 0:w.length)??0)>=u;return a.jsxs("div",{className:"relative flex flex-col gap-6 overflow-hidden",children:[a.jsx(He,{onDrop:W,noClick:!1,noKeyboard:!1,maxSize:v,maxFiles:u,multiple:u>1||S,disabled:P,validator:m=>{var $;if(!m.name)return{code:"invalid-file-name",message:t("documentPanel.uploadDocuments.fileUploader.invalidFileName",{fallback:"Invalid file name"})};const z=`.${(($=m.name.split(".").pop())==null?void 0:$.toLowerCase())||""}`;return Object.entries(j||{}).some(([k,Y])=>m.type===k||Array.isArray(Y)&&Y.includes(z))?m.size>v?{code:"file-too-large",message:t("documentPanel.uploadDocuments.fileUploader.fileTooLarge",{maxSize:ha(v)})}:null:{code:"file-invalid-type",message:t("documentPanel.uploadDocuments.fileUploader.unsupportedType")}},children:({getRootProps:m,getInputProps:z,isDragActive:b})=>a.jsxs("div",{...m(),className:_("group border-muted-foreground/25 hover:bg-muted/25 relative grid h-52 w-full cursor-pointer place-items-center rounded-lg border-2 border-dashed px-5 py-2.5 text-center transition","ring-offset-background focus-visible:ring-ring focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none",b&&"border-muted-foreground/50",P&&"pointer-events-none opacity-60",N),...y,children:[a.jsx("input",{...z()}),b?a.jsxs("div",{className:"flex flex-col items-center justify-center gap-4 sm:px-5",children:[a.jsx("div",{className:"rounded-full border border-dashed p-3",children:a.jsx(ca,{className:"text-muted-foreground size-7","aria-hidden":"true"})}),a.jsx("p",{className:"text-muted-foreground font-medium",children:t("documentPanel.uploadDocuments.fileUploader.dropHere")})]}):a.jsxs("div",{className:"flex flex-col items-center justify-center gap-4 sm:px-5",children:[a.jsx("div",{className:"rounded-full border border-dashed p-3",children:a.jsx(ca,{className:"text-muted-foreground size-7","aria-hidden":"true"})}),a.jsxs("div",{className:"flex flex-col gap-px",children:[a.jsx("p",{className:"text-muted-foreground font-medium",children:t("documentPanel.uploadDocuments.fileUploader.dragAndDrop")}),E?a.jsx("p",{className:"text-muted-foreground/70 text-sm",children:E}):a.jsxs("p",{className:"text-muted-foreground/70 text-sm",children:[t("documentPanel.uploadDocuments.fileUploader.uploadDescription",{count:u,isMultiple:u===1/0,maxSize:ha(v)}),t("documentPanel.uploadDocuments.fileTypes")]})]})]})]})}),w!=null&&w.length?a.jsx(Mt,{className:"h-fit w-full px-3",children:a.jsx("div",{className:"flex max-h-48 flex-col gap-4",children:w==null?void 0:w.map((m,z)=>a.jsx(cn,{file:m,onRemove:()=>U(z),progress:p==null?void 0:p[m.name],error:r==null?void 0:r[m.name]},z))})}):null]})}function Ua({value:e,error:t}){return a.jsx("div",{className:"relative h-2 w-full",children:a.jsx("div",{className:"h-full w-full overflow-hidden rounded-full bg-secondary",children:a.jsx("div",{className:_("h-full transition-all",t?"bg-red-400":"bg-primary"),style:{width:`${e}%`}})})})}function cn({file:e,progress:t,error:n,onRemove:i}){const{t:s}=ce();return a.jsxs("div",{className:"relative flex items-center gap-2.5",children:[a.jsxs("div",{className:"flex flex-1 gap-2.5",children:[n?a.jsx(tt,{className:"text-red-400 size-10","aria-hidden":"true"}):jt(e)?a.jsx(rn,{file:e}):null,a.jsxs("div",{className:"flex w-full flex-col gap-2",children:[a.jsxs("div",{className:"flex flex-col gap-px",children:[a.jsx("p",{className:"text-foreground/80 line-clamp-1 text-sm font-medium",children:e.name}),a.jsx("p",{className:"text-muted-foreground text-xs",children:ha(e.size)})]}),n?a.jsxs("div",{className:"text-red-400 text-sm",children:[a.jsx("div",{className:"relative mb-2",children:a.jsx(Ua,{value:100,error:!0})}),a.jsx("p",{children:n})]}):t?a.jsx(Ua,{value:t}):null]})]}),a.jsx("div",{className:"flex items-center gap-2",children:a.jsxs(F,{type:"button",variant:"outline",size:"icon",className:"size-7",onClick:i,children:[a.jsx(it,{className:"size-4","aria-hidden":"true"}),a.jsx("span",{className:"sr-only",children:s("documentPanel.uploadDocuments.fileUploader.removeFile")})]})})]})}function jt(e){return"preview"in e&&typeof e.preview=="string"}function rn({file:e}){return e.type.startsWith("image/")?a.jsx("div",{className:"aspect-square shrink-0 rounded-md object-cover"}):a.jsx(tt,{className:"text-muted-foreground size-10","aria-hidden":"true"})}function pn({onDocumentsUploaded:e}){const{t}=ce(),[n,i]=o.useState(!1),[s,l]=o.useState(!1),[p,r]=o.useState({}),[j,v]=o.useState({}),u=o.useCallback(h=>{h.forEach(({file:E,errors:N})=>{var w;let y=((w=N[0])==null?void 0:w.message)||t("documentPanel.uploadDocuments.fileUploader.fileRejected",{name:E.name});y.includes("file-invalid-type")&&(y=t("documentPanel.uploadDocuments.fileUploader.unsupportedType")),r(g=>({...g,[E.name]:100})),v(g=>({...g,[E.name]:y}))})},[r,v,t]),S=o.useCallback(async h=>{var y,w;l(!0);let E=!1;v(g=>{const W={...g};return h.forEach(U=>{delete W[U.name]}),W});const N=O.loading(t("documentPanel.uploadDocuments.batch.uploading"));try{const g={},W=new Intl.Collator(["zh-CN","en"],{sensitivity:"accent",numeric:!0}),U=[...h].sort((m,z)=>W.compare(m.name,z.name));for(const m of U)try{r(b=>({...b,[m.name]:0}));const z=await It(m,b=>{console.debug(t("documentPanel.uploadDocuments.single.uploading",{name:m.name,percent:b})),r($=>({...$,[m.name]:b}))});z.status==="duplicated"?(g[m.name]=t("documentPanel.uploadDocuments.fileUploader.duplicateFile"),v(b=>({...b,[m.name]:t("documentPanel.uploadDocuments.fileUploader.duplicateFile")}))):z.status!=="success"?(g[m.name]=z.message,v(b=>({...b,[m.name]:z.message}))):E=!0}catch(z){console.error(`Upload failed for ${m.name}:`,z);let b=ie(z);if(z&&typeof z=="object"&&"response"in z){const $=z;((y=$.response)==null?void 0:y.status)===400&&(b=((w=$.response.data)==null?void 0:w.detail)||b),r(k=>({...k,[m.name]:100}))}g[m.name]=b,v($=>({...$,[m.name]:b}))}Object.keys(g).length>0?O.error(t("documentPanel.uploadDocuments.batch.error"),{id:N}):O.success(t("documentPanel.uploadDocuments.batch.success"),{id:N}),E&&e&&e().catch(m=>{console.error("Error refreshing documents:",m)})}catch(g){console.error("Unexpected error during upload:",g),O.error(t("documentPanel.uploadDocuments.generalError",{error:ie(g)}),{id:N})}finally{l(!1)}},[l,r,v,t,e]);return a.jsxs(ze,{open:n,onOpenChange:h=>{s||(h||(r({}),v({})),i(h))},children:[a.jsx($e,{asChild:!0,children:a.jsxs(F,{variant:"default",side:"bottom",tooltip:t("documentPanel.uploadDocuments.tooltip"),size:"sm",children:[a.jsx(ca,{})," ",t("documentPanel.uploadDocuments.button")]})}),a.jsxs(Ce,{className:"sm:max-w-xl",onCloseAutoFocus:h=>h.preventDefault(),children:[a.jsxs(Pe,{children:[a.jsx(Se,{children:t("documentPanel.uploadDocuments.title")}),a.jsx(Ee,{children:t("documentPanel.uploadDocuments.description")})]}),a.jsx(ln,{maxFileCount:1/0,maxSize:200*1024*1024,description:t("documentPanel.uploadDocuments.fileTypes"),onUpload:S,onReject:u,progresses:p,fileErrors:j,disabled:s})]})]})}const $a=({htmlFor:e,className:t,children:n,...i})=>a.jsx("label",{htmlFor:e,className:t,...i,children:n});function dn({onDocumentsCleared:e}){const{t}=ce(),[n,i]=o.useState(!1),[s,l]=o.useState(""),[p,r]=o.useState(!1),j=s.toLowerCase()==="yes";o.useEffect(()=>{n||(l(""),r(!1))},[n]);const v=o.useCallback(async()=>{if(j)try{const u=await qt();if(u.status!=="success"){O.error(t("documentPanel.clearDocuments.failed",{message:u.message})),l("");return}if(O.success(t("documentPanel.clearDocuments.success")),p)try{await Lt(),O.success(t("documentPanel.clearDocuments.cacheCleared"))}catch(S){O.error(t("documentPanel.clearDocuments.cacheClearFailed",{error:ie(S)}))}e&&e().catch(console.error),i(!1)}catch(u){O.error(t("documentPanel.clearDocuments.error",{error:ie(u)})),l("")}},[j,p,i,t,e]);return a.jsxs(ze,{open:n,onOpenChange:i,children:[a.jsx($e,{asChild:!0,children:a.jsxs(F,{variant:"outline",side:"bottom",tooltip:t("documentPanel.clearDocuments.tooltip"),size:"sm",children:[a.jsx(Bt,{})," ",t("documentPanel.clearDocuments.button")]})}),a.jsxs(Ce,{className:"sm:max-w-xl",onCloseAutoFocus:u=>u.preventDefault(),children:[a.jsxs(Pe,{children:[a.jsxs(Se,{className:"flex items-center gap-2 text-red-500 dark:text-red-400 font-bold",children:[a.jsx(nt,{className:"h-5 w-5"}),t("documentPanel.clearDocuments.title")]}),a.jsx(Ee,{className:"pt-2",children:t("documentPanel.clearDocuments.description")})]}),a.jsx("div",{className:"text-red-500 dark:text-red-400 font-semibold mb-4",children:t("documentPanel.clearDocuments.warning")}),a.jsx("div",{className:"mb-4",children:t("documentPanel.clearDocuments.confirm")}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:"space-y-2",children:[a.jsx($a,{htmlFor:"confirm-text",className:"text-sm font-medium",children:t("documentPanel.clearDocuments.confirmPrompt")}),a.jsx(qe,{id:"confirm-text",value:s,onChange:u=>l(u.target.value),placeholder:t("documentPanel.clearDocuments.confirmPlaceholder"),className:"w-full"})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(ot,{id:"clear-cache",checked:p,onCheckedChange:u=>r(u===!0)}),a.jsx($a,{htmlFor:"clear-cache",className:"text-sm font-medium cursor-pointer",children:t("documentPanel.clearDocuments.clearCache")})]})]}),a.jsxs(ba,{children:[a.jsx(F,{variant:"outline",onClick:()=>i(!1),children:t("common.cancel")}),a.jsx(F,{variant:"destructive",onClick:v,disabled:!j,children:t("documentPanel.clearDocuments.confirmButton")})]})]})]})}const Ha=({htmlFor:e,className:t,children:n,...i})=>a.jsx("label",{htmlFor:e,className:t,...i,children:n});function mn({selectedDocIds:e,totalCompletedCount:t,onDocumentsDeleted:n}){const{t:i}=ce(),[s,l]=o.useState(!1),[p,r]=o.useState(""),[j,v]=o.useState(!1),[u,S]=o.useState(!1),h=p.toLowerCase()==="yes"&&!u;o.useEffect(()=>{s||(r(""),v(!1),S(!1))},[s]);const E=o.useCallback(async()=>{if(!(!h||e.length===0)){if(e.length===t&&t>0){O.error(i("documentPanel.deleteDocuments.cannotDeleteAll"));return}S(!0);try{const N=await Ut(e,j);if(N.status==="deletion_started")O.success(i("documentPanel.deleteDocuments.success",{count:e.length}));else if(N.status==="busy"){O.error(i("documentPanel.deleteDocuments.busy")),r(""),S(!1);return}else if(N.status==="not_allowed"){O.error(i("documentPanel.deleteDocuments.notAllowed")),r(""),S(!1);return}else{O.error(i("documentPanel.deleteDocuments.failed",{message:N.message})),r(""),S(!1);return}n&&n().catch(console.error),l(!1)}catch(N){O.error(i("documentPanel.deleteDocuments.error",{error:ie(N)})),r("")}finally{S(!1)}}},[h,e,t,j,l,i,n]);return a.jsxs(ze,{open:s,onOpenChange:l,children:[a.jsx($e,{asChild:!0,children:a.jsxs(F,{variant:"destructive",side:"bottom",tooltip:i("documentPanel.deleteDocuments.tooltip",{count:e.length}),size:"sm",children:[a.jsx($t,{})," ",i("documentPanel.deleteDocuments.button")]})}),a.jsxs(Ce,{className:"sm:max-w-xl",onCloseAutoFocus:N=>N.preventDefault(),children:[a.jsxs(Pe,{children:[a.jsxs(Se,{className:"flex items-center gap-2 text-red-500 dark:text-red-400 font-bold",children:[a.jsx(nt,{className:"h-5 w-5"}),i("documentPanel.deleteDocuments.title")]}),a.jsx(Ee,{className:"pt-2",children:i("documentPanel.deleteDocuments.description",{count:e.length})})]}),a.jsx("div",{className:"text-red-500 dark:text-red-400 font-semibold mb-4",children:i("documentPanel.deleteDocuments.warning")}),a.jsx("div",{className:"mb-4",children:i("documentPanel.deleteDocuments.confirm",{count:e.length})}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:"space-y-2",children:[a.jsx(Ha,{htmlFor:"confirm-text",className:"text-sm font-medium",children:i("documentPanel.deleteDocuments.confirmPrompt")}),a.jsx(qe,{id:"confirm-text",value:p,onChange:N=>r(N.target.value),placeholder:i("documentPanel.deleteDocuments.confirmPlaceholder"),className:"w-full",disabled:u})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx("input",{type:"checkbox",id:"delete-file",checked:j,onChange:N=>v(N.target.checked),disabled:u,className:"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"}),a.jsx(Ha,{htmlFor:"delete-file",className:"text-sm font-medium cursor-pointer",children:i("documentPanel.deleteDocuments.deleteFileOption")})]})]}),a.jsxs(ba,{children:[a.jsx(F,{variant:"outline",onClick:()=>l(!1),disabled:u,children:i("common.cancel")}),a.jsx(F,{variant:"destructive",onClick:E,disabled:!h,children:i(u?"documentPanel.deleteDocuments.deleting":"documentPanel.deleteDocuments.confirmButton")})]})]})]})}function un({selectedCount:e,onDeselect:t}){const{t:n}=ce(),[i,s]=o.useState(!1);o.useEffect(()=>{},[i]);const l=o.useCallback(()=>{t(),s(!1)},[t,s]);return a.jsxs(ze,{open:i,onOpenChange:s,children:[a.jsx($e,{asChild:!0,children:a.jsxs(F,{variant:"outline",side:"bottom",tooltip:n("documentPanel.deselectDocuments.tooltip"),size:"sm",children:[a.jsx(it,{})," ",n("documentPanel.deselectDocuments.button")]})}),a.jsxs(Ce,{className:"sm:max-w-md",onCloseAutoFocus:p=>p.preventDefault(),children:[a.jsxs(Pe,{children:[a.jsxs(Se,{className:"flex items-center gap-2",children:[a.jsx(Ht,{className:"h-5 w-5"}),n("documentPanel.deselectDocuments.title")]}),a.jsx(Ee,{className:"pt-2",children:n("documentPanel.deselectDocuments.description",{count:e})})]}),a.jsxs(ba,{children:[a.jsx(F,{variant:"outline",onClick:()=>s(!1),children:n("common.cancel")}),a.jsx(F,{variant:"default",onClick:l,children:n("documentPanel.deselectDocuments.confirmButton")})]})]})]})}const Ka=[{value:10,label:"10"},{value:20,label:"20"},{value:50,label:"50"},{value:100,label:"100"},{value:200,label:"200"}];function fn({currentPage:e,totalPages:t,pageSize:n,totalCount:i,onPageChange:s,onPageSizeChange:l,isLoading:p=!1,compact:r=!1,className:j}){const{t:v}=ce(),[u,S]=o.useState(e.toString());o.useEffect(()=>{S(e.toString())},[e]);const h=o.useCallback(P=>{S(P)},[]),E=o.useCallback(()=>{const P=parseInt(u,10);!isNaN(P)&&P>=1&&P<=t?s(P):S(e.toString())},[u,t,s,e]),N=o.useCallback(P=>{P.key==="Enter"&&E()},[E]),y=o.useCallback(P=>{const m=parseInt(P,10);isNaN(m)||l(m)},[l]),w=o.useCallback(()=>{e>1&&!p&&s(1)},[e,s,p]),g=o.useCallback(()=>{e>1&&!p&&s(e-1)},[e,s,p]),W=o.useCallback(()=>{e<t&&!p&&s(e+1)},[e,t,s,p]),U=o.useCallback(()=>{e<t&&!p&&s(t)},[e,t,s,p]);return t<=1?null:r?a.jsxs("div",{className:_("flex items-center gap-2",j),children:[a.jsxs("div",{className:"flex items-center gap-1",children:[a.jsx(F,{variant:"outline",size:"sm",onClick:g,disabled:e<=1||p,className:"h-8 w-8 p-0",children:a.jsx(ya,{className:"h-4 w-4"})}),a.jsxs("div",{className:"flex items-center gap-1",children:[a.jsx(qe,{type:"text",value:u,onChange:P=>h(P.target.value),onBlur:E,onKeyPress:N,disabled:p,className:"h-8 w-12 text-center text-sm"}),a.jsxs("span",{className:"text-sm text-gray-500",children:["/ ",t]})]}),a.jsx(F,{variant:"outline",size:"sm",onClick:W,disabled:e>=t||p,className:"h-8 w-8 p-0",children:a.jsx(ja,{className:"h-4 w-4"})})]}),a.jsxs(Na,{value:n.toString(),onValueChange:y,disabled:p,children:[a.jsx(ra,{className:"h-8 w-16",children:a.jsx(za,{})}),a.jsx(pa,{children:Ka.map(P=>a.jsx(da,{value:P.value.toString(),children:P.label},P.value))})]})]}):a.jsxs("div",{className:_("flex items-center justify-between gap-4",j),children:[a.jsx("div",{className:"text-sm text-gray-500",children:v("pagination.showing",{start:Math.min((e-1)*n+1,i),end:Math.min(e*n,i),total:i})}),a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsxs("div",{className:"flex items-center gap-1",children:[a.jsx(F,{variant:"outline",size:"sm",onClick:w,disabled:e<=1||p,className:"h-8 w-8 p-0",tooltip:v("pagination.firstPage"),children:a.jsx(Kt,{className:"h-4 w-4"})}),a.jsx(F,{variant:"outline",size:"sm",onClick:g,disabled:e<=1||p,className:"h-8 w-8 p-0",tooltip:v("pagination.prevPage"),children:a.jsx(ya,{className:"h-4 w-4"})}),a.jsxs("div",{className:"flex items-center gap-1",children:[a.jsx("span",{className:"text-sm",children:v("pagination.page")}),a.jsx(qe,{type:"text",value:u,onChange:P=>h(P.target.value),onBlur:E,onKeyPress:N,disabled:p,className:"h-8 w-16 text-center text-sm"}),a.jsxs("span",{className:"text-sm",children:["/ ",t]})]}),a.jsx(F,{variant:"outline",size:"sm",onClick:W,disabled:e>=t||p,className:"h-8 w-8 p-0",tooltip:v("pagination.nextPage"),children:a.jsx(ja,{className:"h-4 w-4"})}),a.jsx(F,{variant:"outline",size:"sm",onClick:U,disabled:e>=t||p,className:"h-8 w-8 p-0",tooltip:v("pagination.lastPage"),children:a.jsx(Wt,{className:"h-4 w-4"})})]}),a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx("span",{className:"text-sm",children:v("pagination.pageSize")}),a.jsxs(Na,{value:n.toString(),onValueChange:y,disabled:p,children:[a.jsx(ra,{className:"h-8 w-16",children:a.jsx(za,{})}),a.jsx(pa,{children:Ka.map(P=>a.jsx(da,{value:P.value.toString(),children:P.label},P.value))})]})]})]})]})}function xn({open:e,onOpenChange:t}){var S;const{t:n}=ce(),[i,s]=o.useState(null),[l,p]=o.useState("center"),[r,j]=o.useState(!1),v=o.useRef(null);o.useEffect(()=>{e&&(p("center"),j(!1))},[e]),o.useEffect(()=>{const h=v.current;!h||r||(h.scrollTop=h.scrollHeight)},[i==null?void 0:i.history_messages,r]);const u=()=>{const h=v.current;if(!h)return;const E=Math.abs(h.scrollHeight-h.scrollTop-h.clientHeight)<1;j(!E)};return o.useEffect(()=>{if(!e)return;const h=async()=>{try{const N=await Jt();s(N)}catch(N){O.error(n("documentPanel.pipelineStatus.errors.fetchFailed",{error:ie(N)}))}};h();const E=setInterval(h,2e3);return()=>clearInterval(E)},[e,n]),a.jsx(ze,{open:e,onOpenChange:t,children:a.jsxs(Ce,{className:_("sm:max-w-[800px] transition-all duration-200 fixed",l==="left"&&"!left-[25%] !translate-x-[-50%] !mx-4",l==="center"&&"!left-1/2 !-translate-x-1/2",l==="right"&&"!left-[75%] !translate-x-[-50%] !mx-4"),children:[a.jsx(Ee,{className:"sr-only",children:i!=null&&i.job_name?`${n("documentPanel.pipelineStatus.jobName")}: ${i.job_name}, ${n("documentPanel.pipelineStatus.progress")}: ${i.cur_batch}/${i.batchs}`:n("documentPanel.pipelineStatus.noActiveJob")}),a.jsxs(Pe,{className:"flex flex-row items-center",children:[a.jsx(Se,{className:"flex-1",children:n("documentPanel.pipelineStatus.title")}),a.jsxs("div",{className:"flex items-center gap-2 mr-8",children:[a.jsx(F,{variant:"ghost",size:"icon",className:_("h-6 w-6",l==="left"&&"bg-zinc-200 text-zinc-800 hover:bg-zinc-300 dark:bg-zinc-700 dark:text-zinc-200 dark:hover:bg-zinc-600"),onClick:()=>p("left"),children:a.jsx(Gt,{className:"h-4 w-4"})}),a.jsx(F,{variant:"ghost",size:"icon",className:_("h-6 w-6",l==="center"&&"bg-zinc-200 text-zinc-800 hover:bg-zinc-300 dark:bg-zinc-700 dark:text-zinc-200 dark:hover:bg-zinc-600"),onClick:()=>p("center"),children:a.jsx(Vt,{className:"h-4 w-4"})}),a.jsx(F,{variant:"ghost",size:"icon",className:_("h-6 w-6",l==="right"&&"bg-zinc-200 text-zinc-800 hover:bg-zinc-300 dark:bg-zinc-700 dark:text-zinc-200 dark:hover:bg-zinc-600"),onClick:()=>p("right"),children:a.jsx(Yt,{className:"h-4 w-4"})})]})]}),a.jsxs("div",{className:"space-y-4 pt-4",children:[a.jsxs("div",{className:"flex items-center gap-4",children:[a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.busy"),":"]}),a.jsx("div",{className:`h-2 w-2 rounded-full ${i!=null&&i.busy?"bg-green-500":"bg-gray-300"}`})]}),a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.requestPending"),":"]}),a.jsx("div",{className:`h-2 w-2 rounded-full ${i!=null&&i.request_pending?"bg-green-500":"bg-gray-300"}`})]})]}),a.jsxs("div",{className:"rounded-md border p-3 space-y-2",children:[a.jsxs("div",{children:[n("documentPanel.pipelineStatus.jobName"),": ",(i==null?void 0:i.job_name)||"-"]}),a.jsxs("div",{className:"flex justify-between",children:[a.jsxs("span",{children:[n("documentPanel.pipelineStatus.startTime"),": ",i!=null&&i.job_start?new Date(i.job_start).toLocaleString(void 0,{year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric"}):"-"]}),a.jsxs("span",{children:[n("documentPanel.pipelineStatus.progress"),": ",i?`${i.cur_batch}/${i.batchs} ${n("documentPanel.pipelineStatus.unit")}`:"-"]})]})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.latestMessage"),":"]}),a.jsx("div",{className:"font-mono text-xs rounded-md bg-zinc-800 text-zinc-100 p-3 whitespace-pre-wrap break-words",children:(i==null?void 0:i.latest_message)||"-"})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.historyMessages"),":"]}),a.jsx("div",{ref:v,onScroll:u,className:"font-mono text-xs rounded-md bg-zinc-800 text-zinc-100 p-3 overflow-y-auto min-h-[7.5em] max-h-[40vh]",children:(S=i==null?void 0:i.history_messages)!=null&&S.length?i.history_messages.map((h,E)=>a.jsx("div",{className:"whitespace-pre-wrap break-words",children:h},E)):"-"})]})]})]})})}const oa=(e,t=20)=>{if(!e.file_path||typeof e.file_path!="string"||e.file_path.trim()==="")return e.id;const n=e.file_path.split("/"),i=n[n.length-1];return!i||i.trim()===""?e.id:i.length>t?i.slice(0,t)+"...":i},vn=`
/* Tooltip styles */
.tooltip-container {
  position: relative;
  overflow: visible !important;
}

.tooltip {
  position: fixed; /* Use fixed positioning to escape overflow constraints */
  z-index: 9999; /* Ensure tooltip appears above all other elements */
  max-width: 600px;
  white-space: normal;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(0, 0, 0, 0.95);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  pointer-events: none; /* Prevent tooltip from interfering with mouse events */
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.15s, visibility 0.15s;
}

.tooltip.visible {
  opacity: 1;
  visibility: visible;
}

.dark .tooltip {
  background-color: rgba(255, 255, 255, 0.95);
  color: black;
}

/* Position tooltip helper class */
.tooltip-helper {
  position: absolute;
  visibility: hidden;
  pointer-events: none;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
}

@keyframes pulse {
  0% {
    background-color: rgb(255 0 0 / 0.1);
    border-color: rgb(255 0 0 / 0.2);
  }
  50% {
    background-color: rgb(255 0 0 / 0.2);
    border-color: rgb(255 0 0 / 0.4);
  }
  100% {
    background-color: rgb(255 0 0 / 0.1);
    border-color: rgb(255 0 0 / 0.2);
  }
}

.dark .pipeline-busy {
  animation: dark-pulse 2s infinite;
}

@keyframes dark-pulse {
  0% {
    background-color: rgb(255 0 0 / 0.2);
    border-color: rgb(255 0 0 / 0.4);
  }
  50% {
    background-color: rgb(255 0 0 / 0.3);
    border-color: rgb(255 0 0 / 0.6);
  }
  100% {
    background-color: rgb(255 0 0 / 0.2);
    border-color: rgb(255 0 0 / 0.4);
  }
}

.pipeline-busy {
  animation: pulse 2s infinite;
  border: 1px solid;
}
`;function jn(){const e=o.useRef(!0);o.useEffect(()=>{e.current=!0;const c=()=>{e.current=!1};return window.addEventListener("beforeunload",c),()=>{e.current=!1,window.removeEventListener("beforeunload",c)}},[]);const[t,n]=o.useState(!1),{t:i,i18n:s}=ce(),l=ke.use.health(),p=ke.use.pipelineBusy(),[r,j]=o.useState(null),v=De.use.currentTab(),u=De.use.showFileName(),S=De.use.setShowFileName(),h=De.use.documentsPageSize(),E=De.use.setDocumentsPageSize(),[,N]=o.useState([]),[y,w]=o.useState({page:1,page_size:h,total_count:0,total_pages:0,has_next:!1,has_prev:!1}),[g,W]=o.useState({all:0}),[U,P]=o.useState(!1),[m,z]=o.useState("updated_at"),[b,$]=o.useState("desc"),[k,Y]=o.useState("all"),[me,H]=o.useState({all:1,processed:1,processing:1,pending:1,failed:1}),[V,I]=o.useState([]),G=V.length>0,je=o.useCallback((c,f)=>{I(C=>f?[...C,c]:C.filter(D=>D!==c))},[]),ue=o.useCallback(()=>{I([])},[]),ne=c=>{let f=c;c==="id"&&(f=u?"file_path":"id");const C=m===f&&b==="desc"?"asc":"desc";z(f),$(C),w(D=>({...D,page:1})),H({all:1,processed:1,processing:1,pending:1,failed:1})},K=o.useCallback(c=>[...c].sort((f,C)=>{let D,A;m==="id"&&u?(D=oa(f),A=oa(C)):m==="id"?(D=f.id,A=C.id):(D=new Date(f[m]).getTime(),A=new Date(C[m]).getTime());const q=b==="asc"?1:-1;return typeof D=="string"&&typeof A=="string"?q*D.localeCompare(A):q*(D>A?1:D<A?-1:0)}),[m,b,u]),_e=o.useMemo(()=>{if(!r)return null;const c=[];return k==="all"?Object.entries(r.statuses).forEach(([f,C])=>{C.forEach(D=>{c.push({...D,status:f})})}):(r.statuses[k]||[]).forEach(C=>{c.push({...C,status:k})}),m&&b?K(c):c},[r,m,b,k,K]),oe=o.useMemo(()=>{if(!r)return{all:0};const c={all:0};return Object.entries(r.statuses).forEach(([f,C])=>{c[f]=C.length,c.all+=C.length}),c},[r]),fe=o.useRef({processed:0,processing:0,pending:0,failed:0});o.useEffect(()=>{const c=document.createElement("style");return c.textContent=vn,document.head.appendChild(c),()=>{document.head.removeChild(c)}},[]);const Fe=o.useRef(null);o.useEffect(()=>{if(!r)return;const c=()=>{document.querySelectorAll(".tooltip-container").forEach(A=>{const q=A.querySelector(".tooltip");if(!q||!q.classList.contains("visible"))return;const L=A.getBoundingClientRect();q.style.left=`${L.left}px`,q.style.top=`${L.top-5}px`,q.style.transform="translateY(-100%)"})},f=D=>{const q=D.target.closest(".tooltip-container");if(!q)return;const L=q.querySelector(".tooltip");L&&(L.classList.add("visible"),c())},C=D=>{const q=D.target.closest(".tooltip-container");if(!q)return;const L=q.querySelector(".tooltip");L&&L.classList.remove("visible")};return document.addEventListener("mouseover",f),document.addEventListener("mouseout",C),()=>{document.removeEventListener("mouseover",f),document.removeEventListener("mouseout",C)}},[r]);const X=o.useCallback(async(c,f,C)=>{try{if(!e.current)return;P(!0);const A=await wa({status_filter:C==="all"?null:C,page:c,page_size:f,sort_field:m,sort_direction:b});if(!e.current)return;w(A.pagination),N(A.documents),W(A.status_counts);const q={statuses:{processed:A.documents.filter(L=>L.status==="processed"),processing:A.documents.filter(L=>L.status==="processing"),pending:A.documents.filter(L=>L.status==="pending"),failed:A.documents.filter(L=>L.status==="failed")}};A.pagination.total_count>0?j(q):j(null)}catch(D){e.current&&O.error(i("documentPanel.documentManager.errors.loadFailed",{error:ie(D)}))}finally{e.current&&P(!1)}},[m,b,i]),re=o.useCallback(async()=>{await X(y.page,y.page_size,k)},[X,y.page,y.page_size,k]),Z=o.useRef(void 0),xe=o.useRef(null),ee=o.useCallback(()=>{xe.current&&(clearInterval(xe.current),xe.current=null)},[]),J=o.useCallback(c=>{ee(),xe.current=setInterval(async()=>{try{e.current&&await re()}catch(f){e.current&&O.error(i("documentPanel.documentManager.errors.scanProgressFailed",{error:ie(f)}))}},c)},[re,i,ee]),he=o.useCallback(async()=>{try{if(!e.current)return;const{status:c,message:f,track_id:C}=await Qt();if(!e.current)return;O.message(f||c),ke.getState().resetHealthCheckTimerDelayed(1e3),J(2e3)}catch(c){e.current&&O.error(i("documentPanel.documentManager.errors.scanFailed",{error:ie(c)}))}},[i,J]),pe=o.useCallback(c=>{c!==y.page_size&&(E(c),H({all:1,processed:1,processing:1,pending:1,failed:1}),w(f=>({...f,page:1,page_size:c})))},[y.page_size,E]),ae=o.useCallback(async()=>{try{P(!0);const c={status_filter:k==="all"?null:k,page:1,page_size:y.page_size,sort_field:m,sort_direction:b},f=await wa(c);if(!e.current)return;if(f.pagination.total_count<y.page_size&&y.page_size!==10)pe(10);else{w(f.pagination),N(f.documents),W(f.status_counts);const C={statuses:{processed:f.documents.filter(D=>D.status==="processed"),processing:f.documents.filter(D=>D.status==="processing"),pending:f.documents.filter(D=>D.status==="pending"),failed:f.documents.filter(D=>D.status==="failed")}};f.pagination.total_count>0?j(C):j(null)}}catch(c){e.current&&O.error(i("documentPanel.documentManager.errors.loadFailed",{error:ie(c)}))}finally{e.current&&P(!1)}},[k,y.page_size,m,b,pe,i]);o.useEffect(()=>{if(Z.current!==void 0&&Z.current!==p&&v==="documents"&&l&&e.current){ae();const f=(g.processing||0)>0||(g.pending||0)>0?5e3:3e4;J(f)}Z.current=p},[p,v,l,ae,g.processing,g.pending,J]),o.useEffect(()=>{if(v!=="documents"||!l){ee();return}const f=(g.processing||0)>0||(g.pending||0)>0?5e3:3e4;return J(f),()=>{ee()}},[l,i,v,g,J,ee]),o.useEffect(()=>{var C,D,A,q,L,d,x,R;if(!r)return;const c={processed:((D=(C=r==null?void 0:r.statuses)==null?void 0:C.processed)==null?void 0:D.length)||0,processing:((q=(A=r==null?void 0:r.statuses)==null?void 0:A.processing)==null?void 0:q.length)||0,pending:((d=(L=r==null?void 0:r.statuses)==null?void 0:L.pending)==null?void 0:d.length)||0,failed:((R=(x=r==null?void 0:r.statuses)==null?void 0:x.failed)==null?void 0:R.length)||0};Object.keys(c).some(B=>c[B]!==fe.current[B])&&e.current&&ke.getState().check(),fe.current=c},[r]);const Te=o.useCallback(c=>{c!==y.page&&(H(f=>({...f,[k]:c})),w(f=>({...f,page:c})))},[y.page,k]),de=o.useCallback(c=>{if(c===k)return;H(C=>({...C,[k]:y.page}));const f=me[c];Y(c),w(C=>({...C,page:f}))},[k,y.page,me]),Ae=o.useCallback(async()=>{I([]),ke.getState().resetHealthCheckTimerDelayed(1e3),J(2e3)},[J]),Oe=o.useCallback(async()=>{if(ee(),W({all:0,processed:0,processing:0,pending:0,failed:0}),e.current)try{await re()}catch(c){console.error("Error fetching documents after clear:",c)}v==="documents"&&l&&e.current&&J(3e4)},[ee,W,re,v,l,J]);return o.useEffect(()=>{if(m==="id"||m==="file_path"){const c=u?"file_path":"id";m!==c&&z(c)}},[u,m]),o.useEffect(()=>{v==="documents"&&X(y.page,y.page_size,k)},[v,y.page,y.page_size,k,m,b,X]),a.jsxs(sa,{className:"!rounded-none !overflow-hidden flex flex-col h-full min-h-0",children:[a.jsx(ka,{className:"py-2 px-6",children:a.jsx(la,{className:"text-lg",children:i("documentPanel.documentManager.title")})}),a.jsxs(Da,{className:"flex-1 flex flex-col min-h-0 overflow-auto",children:[a.jsxs("div",{className:"flex justify-between items-center gap-2 mb-2",children:[a.jsxs("div",{className:"flex gap-2",children:[a.jsxs(F,{variant:"outline",onClick:he,side:"bottom",tooltip:i("documentPanel.documentManager.scanTooltip"),size:"sm",children:[a.jsx(Xt,{})," ",i("documentPanel.documentManager.scanButton")]}),a.jsxs(F,{variant:"outline",onClick:()=>n(!0),side:"bottom",tooltip:i("documentPanel.documentManager.pipelineStatusTooltip"),size:"sm",className:_(p&&"pipeline-busy"),children:[a.jsx(Zt,{})," ",i("documentPanel.documentManager.pipelineStatusButton")]})]}),y.total_pages>1&&a.jsx(fn,{currentPage:y.page,totalPages:y.total_pages,pageSize:y.page_size,totalCount:y.total_count,onPageChange:Te,onPageSizeChange:pe,isLoading:U,compact:!0}),a.jsxs("div",{className:"flex gap-2",children:[G&&a.jsx(mn,{selectedDocIds:V,totalCompletedCount:oe.processed||0,onDocumentsDeleted:Ae}),G?a.jsx(un,{selectedCount:V.length,onDeselect:ue}):a.jsx(dn,{onDocumentsCleared:Oe}),a.jsx(pn,{onDocumentsUploaded:re}),a.jsx(xn,{open:t,onOpenChange:n})]})]}),a.jsxs(sa,{className:"flex-1 flex flex-col border rounded-md min-h-0 mb-2",children:[a.jsxs(ka,{className:"flex-none py-2 px-4",children:[a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsx(la,{children:i("documentPanel.documentManager.uploadedTitle")}),a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsxs("div",{className:"flex gap-1",dir:s.dir(),children:[a.jsxs(F,{size:"sm",variant:k==="all"?"secondary":"outline",onClick:()=>de("all"),disabled:U,className:_(k==="all"&&"bg-gray-100 dark:bg-gray-900 font-medium border border-gray-400 dark:border-gray-500 shadow-sm"),children:[i("documentPanel.documentManager.status.all")," (",g.all||oe.all,")"]}),a.jsxs(F,{size:"sm",variant:k==="processed"?"secondary":"outline",onClick:()=>de("processed"),disabled:U,className:_((g.PROCESSED||g.processed||oe.processed)>0?"text-green-600":"text-gray-500",k==="processed"&&"bg-green-100 dark:bg-green-900/30 font-medium border border-green-400 dark:border-green-600 shadow-sm"),children:[i("documentPanel.documentManager.status.completed")," (",g.PROCESSED||g.processed||0,")"]}),a.jsxs(F,{size:"sm",variant:k==="processing"?"secondary":"outline",onClick:()=>de("processing"),disabled:U,className:_((g.PROCESSING||g.processing||oe.processing)>0?"text-blue-600":"text-gray-500",k==="processing"&&"bg-blue-100 dark:bg-blue-900/30 font-medium border border-blue-400 dark:border-blue-600 shadow-sm"),children:[i("documentPanel.documentManager.status.processing")," (",g.PROCESSING||g.processing||0,")"]}),a.jsxs(F,{size:"sm",variant:k==="pending"?"secondary":"outline",onClick:()=>de("pending"),disabled:U,className:_((g.PENDING||g.pending||oe.pending)>0?"text-yellow-600":"text-gray-500",k==="pending"&&"bg-yellow-100 dark:bg-yellow-900/30 font-medium border border-yellow-400 dark:border-yellow-600 shadow-sm"),children:[i("documentPanel.documentManager.status.pending")," (",g.PENDING||g.pending||0,")"]}),a.jsxs(F,{size:"sm",variant:k==="failed"?"secondary":"outline",onClick:()=>de("failed"),disabled:U,className:_((g.FAILED||g.failed||oe.failed)>0?"text-red-600":"text-gray-500",k==="failed"&&"bg-red-100 dark:bg-red-900/30 font-medium border border-red-400 dark:border-red-600 shadow-sm"),children:[i("documentPanel.documentManager.status.failed")," (",g.FAILED||g.failed||0,")"]})]}),a.jsx(F,{variant:"ghost",size:"sm",onClick:ae,disabled:U,side:"bottom",tooltip:i("documentPanel.documentManager.refreshTooltip"),children:a.jsx(ei,{className:"h-4 w-4"})})]}),a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx("label",{htmlFor:"toggle-filename-btn",className:"text-sm text-gray-500",children:i("documentPanel.documentManager.fileNameLabel")}),a.jsx(F,{id:"toggle-filename-btn",variant:"outline",size:"sm",onClick:()=>S(!u),className:"border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800",children:i(u?"documentPanel.documentManager.hideButton":"documentPanel.documentManager.showButton")})]})]}),a.jsx(at,{"aria-hidden":"true",className:"hidden",children:i("documentPanel.documentManager.uploadedDescription")})]}),a.jsxs(Da,{className:"flex-1 relative p-0",ref:Fe,children:[!r&&a.jsx("div",{className:"absolute inset-0 p-0",children:a.jsx(oi,{title:i("documentPanel.documentManager.emptyTitle"),description:i("documentPanel.documentManager.emptyDescription")})}),r&&a.jsx("div",{className:"absolute inset-0 flex flex-col p-0",children:a.jsx("div",{className:"absolute inset-[-1px] flex flex-col p-0 border rounded-md border-gray-200 dark:border-gray-700 overflow-hidden",children:a.jsxs(ct,{className:"w-full",children:[a.jsx(rt,{className:"sticky top-0 bg-background z-10 shadow-sm",children:a.jsxs(ma,{className:"border-b bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/75 shadow-[inset_0_-1px_0_rgba(0,0,0,0.1)]",children:[a.jsx(se,{onClick:()=>ne("id"),className:"cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-800 select-none",children:a.jsxs("div",{className:"flex items-center",children:[i(u?"documentPanel.documentManager.columns.fileName":"documentPanel.documentManager.columns.id"),(m==="id"&&!u||m==="file_path"&&u)&&a.jsx("span",{className:"ml-1",children:b==="asc"?a.jsx(Xe,{size:14}):a.jsx(Ze,{size:14})})]})}),a.jsx(se,{children:i("documentPanel.documentManager.columns.summary")}),a.jsx(se,{children:i("documentPanel.documentManager.columns.status")}),a.jsx(se,{children:i("documentPanel.documentManager.columns.length")}),a.jsx(se,{children:i("documentPanel.documentManager.columns.chunks")}),a.jsx(se,{onClick:()=>ne("created_at"),className:"cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-800 select-none",children:a.jsxs("div",{className:"flex items-center",children:[i("documentPanel.documentManager.columns.created"),m==="created_at"&&a.jsx("span",{className:"ml-1",children:b==="asc"?a.jsx(Xe,{size:14}):a.jsx(Ze,{size:14})})]})}),a.jsx(se,{onClick:()=>ne("updated_at"),className:"cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-800 select-none",children:a.jsxs("div",{className:"flex items-center",children:[i("documentPanel.documentManager.columns.updated"),m==="updated_at"&&a.jsx("span",{className:"ml-1",children:b==="asc"?a.jsx(Xe,{size:14}):a.jsx(Ze,{size:14})})]})}),a.jsx(se,{className:"w-16 text-center",children:i("documentPanel.documentManager.columns.select")})]})}),a.jsx(pt,{className:"text-sm overflow-auto",children:_e&&_e.map(c=>a.jsxs(ma,{children:[a.jsx(le,{className:"truncate font-mono overflow-visible max-w-[250px]",children:u?a.jsxs(a.Fragment,{children:[a.jsxs("div",{className:"group relative overflow-visible tooltip-container",children:[a.jsx("div",{className:"truncate",children:oa(c,30)}),a.jsx("div",{className:"invisible group-hover:visible tooltip",children:c.file_path})]}),a.jsx("div",{className:"text-xs text-gray-500",children:c.id})]}):a.jsxs("div",{className:"group relative overflow-visible tooltip-container",children:[a.jsx("div",{className:"truncate",children:c.id}),a.jsx("div",{className:"invisible group-hover:visible tooltip",children:c.file_path})]})}),a.jsx(le,{className:"max-w-xs min-w-45 truncate overflow-visible",children:a.jsxs("div",{className:"group relative overflow-visible tooltip-container",children:[a.jsx("div",{className:"truncate",children:c.content_summary}),a.jsx("div",{className:"invisible group-hover:visible tooltip",children:c.content_summary})]})}),a.jsxs(le,{children:[c.status==="processed"&&a.jsx("span",{className:"text-green-600",children:i("documentPanel.documentManager.status.completed")}),c.status==="processing"&&a.jsx("span",{className:"text-blue-600",children:i("documentPanel.documentManager.status.processing")}),c.status==="pending"&&a.jsx("span",{className:"text-yellow-600",children:i("documentPanel.documentManager.status.pending")}),c.status==="failed"&&a.jsx("span",{className:"text-red-600",children:i("documentPanel.documentManager.status.failed")}),c.error_msg&&a.jsx("span",{className:"ml-2 text-red-500",title:c.error_msg,children:"⚠️"})]}),a.jsx(le,{children:c.content_length??"-"}),a.jsx(le,{children:c.chunks_count??"-"}),a.jsx(le,{className:"truncate",children:new Date(c.created_at).toLocaleString()}),a.jsx(le,{className:"truncate",children:new Date(c.updated_at).toLocaleString()}),a.jsx(le,{className:"text-center",children:a.jsx(ot,{checked:V.includes(c.id),onCheckedChange:f=>je(c.id,f===!0),className:"mx-auto"})})]},c.id))})]})})})]})]})]})]})}export{jn as D,Na as S,ra as a,za as b,pa as c,yn as d,da as e};
